import { IApportionmentMeasuresUnit } from '@/@core/domain/ApportionmentMeasuresUnit'
import { IApportionmentMeasuresUnitResponse } from '@/@core/infra/api/ApportionmentMeasuresUnitsApiV3/ApportionmentMeasuresUnitsApiV3.types'

export const apportionmentMeasuresUnitResponseMock1: IApportionmentMeasuresUnitResponse =
  {
    id: 1,
    name: 'MWh'
  }
export const apportionmentMeasuresUnitResponseMock2: IApportionmentMeasuresUnitResponse =
  {
    id: 2,
    name: 'm³'
  }

export const apportionmentMeasuresUnitMock1: IApportionmentMeasuresUnit = {
  id: 1,
  name: 'MWh'
}
export const apportionmentMeasuresUnitMock2: IApportionmentMeasuresUnit = {
  id: 2,
  name: 'm³'
}
