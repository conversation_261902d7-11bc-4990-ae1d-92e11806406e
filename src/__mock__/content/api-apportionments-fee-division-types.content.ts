import { IApportionmentFeeDivisionTypeResponse } from '@/@core/infra/api/ApportionmentsFeeDivisionTypesApiV3/ApportionmentsFeeDivisionTypesApiV3.types'
import { IApportionmentFeeDivisionType } from '@/@core/domain/ApportionmentFeeDivisionType'

/** Response */
export const apportionmentFeeDivisionTypeResponseMock1: IApportionmentFeeDivisionTypeResponse =
  {
    id: 1,
    name: 'Proporcional pelo consumo'
  }
export const apportionmentFeeDivisionTypeResponseMock2: IApportionmentFeeDivisionTypeResponse =
  {
    id: 2,
    name: 'Proporcional pela demanda máxima'
  }

/** Response parsed domain  */
export const apportionmentFeeDivisionTypeMock1: IApportionmentFeeDivisionType =
  {
    id: 1,
    name: 'Proporcional pelo consumo'
  }
export const apportionmentFeeDivisionTypeMock2: IApportionmentFeeDivisionType =
  {
    id: 2,
    name: 'Proporcional pela demanda máxima'
  }
