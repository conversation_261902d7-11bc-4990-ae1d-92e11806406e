import {
  Response,
  ResponseDownload
} from '@/@core/infra/api/MonitoringDocumentApiV4/MonitoringDocumentApiV4.types'
import IMonitoringDocument from '@/@core/domain/MonitoringDocument'

/** Response */
export const monitoringDocumentResponseMock1: Response = {
  id: 1,
  original_name: 'documento.png',
  filename: 'YsrPmZ3YBQA7p7PkJnaF2jP10BMVmSJbd2MM5j6G.png',
  original_path: '{host}/files/eyJpdiI6IkVkd2l..',
  smaller_path: '{host}/files/eyJpdiI6IkVkd2l..',
  bigger_path: '{host}/files/eyJpdiI6IkVkd2l..'
}
export const monitoringDocumentResponseDownloadMock1: ResponseDownload = {
  filename: 'YsrPmZ3YBQA7p7PkJnaF2jP10BMVmSJbd2MM5j6G.png'
}

/** Response parsed domain  */
export const monitoringDocumentMock1: IMonitoringDocument = {
  id: 1,
  originalName: 'documento.png',
  filename: 'YsrPmZ3YBQA7p7PkJnaF2jP10BMVmSJbd2MM5j6G.png',
  originalPath: '{host}/files/eyJpdiI6IkVkd2l..',
  smallerPath: '{host}/files/eyJpdiI6IkVkd2l..',
  biggerPath: '{host}/files/eyJpdiI6IkVkd2l..'
}
