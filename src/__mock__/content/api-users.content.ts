import { IUserResponse } from '@/@core/infra/api/UsersApiV3/UsersApiV3.types'
import { IUser } from '@/@core/domain'

/** Response */
export const userResponseMock1: IUserResponse = {
  id: 1,
  account: {
    id: 1,
    name: 'name Account',
    management: 1
  },
  account_user: {
    id: 1,
    user_id: 1,
    admin: true
  },
  active: true,
  admin: true,
  avatar: '',
  cellphone: '***********',
  email: '<EMAIL>',
  name: 'first name',
  last_name: 'exemplo',
  receive_alert_emails: 0,
  last_access: null,
  cognito_user_id: null
}
export const userResponseMock2: IUserResponse = {
  id: 2,
  account: {
    id: 2,
    name: 'name Account 2',
    management: 1
  },
  account_user: null,
  active: true,
  admin: true,
  avatar: '',
  cellphone: '',
  email: '<EMAIL>',
  name: 'name 2',
  last_name: '',
  receive_alert_emails: 0,
  last_access: null,
  cognito_user_id: null
}

/** Response parsed domain  */
export const userMock1: IUser = {
  id: 1,
  account: {
    id: 1,
    name: 'name Account',
    management: 1
  },
  accountId: 1,
  accountName: 'name Account',
  accountUser: {
    id: 1,
    userId: 1,
    admin: true
  },
  accountUserId: 1,
  accountUserAdmin: true,
  firstName: 'first name',
  lastName: 'exemplo',
  admin: true,
  avatar: '',
  cellphone: '+55 (11) 92222-3333',
  email: '<EMAIL>',
  active: true,
  receiveAlertEmails: 0,
  lastAccess: null,
  cognitoUserId: null
}
export const userMock2: IUser = {
  id: 2,
  account: {
    id: 2,
    name: 'name Account 2',
    management: 1
  },
  accountId: 2,
  accountName: 'name Account 2',
  accountUser: null,
  accountUserId: null,
  accountUserAdmin: false,
  firstName: 'name 2',
  lastName: '',
  admin: true,
  avatar: '',
  cellphone: '',
  email: '<EMAIL>',
  active: true,
  receiveAlertEmails: 0,
  lastAccess: null,
  cognitoUserId: null
}
