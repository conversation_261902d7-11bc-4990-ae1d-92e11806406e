import { IApportionmentTypes } from '@/@core/domain/ApportionmentTypes'
import { IApportionmentTypesResponse } from '@/@core/infra/api/ApportionmentTypesApiV3/ApportionmentTypesApiV3.types'

/** Response */
export const apportionmentTypeResponseMock1: IApportionmentTypesResponse = {
  id: 1,
  name: 'Energia'
}
export const apportionmentTypeResponseMock2: IApportionmentTypesResponse = {
  id: 2,
  name: 'Água'
}
/** Response parsed domain  */
export const apportionmentTypeMock1: IApportionmentTypes = {
  id: 1,
  name: 'Energia'
}
export const apportionmentTypeMock2: IApportionmentTypes = {
  id: 2,
  name: 'Água'
}
