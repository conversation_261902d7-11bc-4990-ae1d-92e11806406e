import { IApportionmentPeriodResponse } from '@/@core/infra/api/ApportionmentsPeriodsApiV3/ApportionmentsPeriodsApiV3.types'
import { IApportionmentPeriod } from '@/@core/domain/ApportionmentPeriod'

/** Response */
export const apportionmentPeriodResponseMock1: IApportionmentPeriodResponse = {
  id: 186,
  apportionment_id: 118,
  period: '2024-12-01',
  period_end: '2024-12-31',
  period_start: '2024-12-01'
}
export const apportionmentPeriodResponseMock2: IApportionmentPeriodResponse = {
  id: 185,
  apportionment_id: 118,
  period: '2024-11-01',
  period_end: '2024-11-30',
  period_start: '2024-11-01'
}

/** Response parsed domain  */
export const apportionmentPeriodMock1: IApportionmentPeriod = {
  id: 186,
  apportionmentId: 118,
  period: '2024-12-01',
  periodEnd: '2024-12-31',
  periodStart: '2024-12-01'
}
export const apportionmentPeriodMock2: IApportionmentPeriod = {
  id: 185,
  apportionmentId: 118,
  period: '2024-11-01',
  periodEnd: '2024-11-30',
  periodStart: '2024-11-01'
}
