import { IUsersEntities } from '@/@core/domain/UsersEntities'
import { IUsersEntitiesResponse } from '@/@core/infra/api/UsersEntitiesApiV3/UsersEntitiesApiV3.types'

/** Response */
export const usersEntitiesResponseMock1: IUsersEntitiesResponse = {
  id: 1,
  account_user: {
    id: 1,
    account_id: 1,
    user_id: 1,
    admin: true
  },
  account_entity: {
    id: 1,
    account_id: 1,
    entity_id: 1,
    entity_type: 'company'
  }
}

/** Response parsed domain  */
export const usersEntitiesMock1: IUsersEntities = {
  id: 1,
  accountUser: {
    id: 1,
    accountId: 1,
    userId: 1,
    admin: true
  },
  accountUserId: 1,
  accountEntity: {
    id: 1,
    accountId: 1,
    entityId: 1,
    entityType: 'company'
  },
  accountEntityId: 1
}

export const usersEntitiesMock2: IUsersEntities = {
  id: 2,
  accountUser: {
    id: 1,
    accountId: 1,
    userId: 1,
    admin: true
  },
  accountUserId: 2,
  accountEntity: {
    id: 2,
    accountId: 1,
    entityId: 1,
    entityType: 'equipment'
  },
  accountEntityId: 2
}
