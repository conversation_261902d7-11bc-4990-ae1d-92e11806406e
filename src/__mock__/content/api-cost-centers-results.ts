import { ICostCenterResult } from '@/@core/domain/CostCenterResult'
import { ICostCenterResultResponse } from '@/@core/infra/api/CostCentersResultsApiV3/CostCentersResultsApiV3.types'

export const costCenterResultResponseMock1: ICostCenterResultResponse = {
  cost_center_id: 411,
  cost_center_name:
    'R115 - CDC - 0512 - Equipment - Medi\u00e7\u00e3o Belo Horizonte',
  tariff_type: 'Cativo',
  tariff_value: 291.19,
  date: '2024-07-01',
  equipments_value: 322972.69,
  additional_fees_value: 0.29,
  additional_consumption_value: 291.18,
  total_value: 323264.16,
  consumption_total: 1193.629,
  equipments_value_previous: 312966.74,
  additional_fees_value_previous: 0.25,
  additional_consumption_value_previous: null,
  total_value_previous: 312966.99,
  percentage_diff_consumption_used: 10.78,
  percentage_diff: 3.29
}

export const costCenterResultResponseMock2: ICostCenterResultResponse = {
  cost_center_id: 412,
  cost_center_name: '0548 - Biocor - Cemig',
  tariff_type: 'Cativo',
  tariff_value: 291.19,
  date: '2024-07-01',
  equipments_value: 48795593.51,
  additional_fees_value: 51.24,
  additional_consumption_value: null,
  total_value: 48795644.75,
  consumption_total: 167573.04,
  equipments_value_previous: 105811683.69,
  additional_fees_value_previous: 86.17,
  additional_consumption_value_previous: null,
  total_value_previous: 105811769.86,
  percentage_diff_consumption_used: -53.96,
  percentage_diff: -53.88
}

export const costCenterResultMock1: ICostCenterResult = {
  additionalConsumptionValue: 291.18,
  additionalConsumptionValuePrevious: null,
  additionalFeesValue: 0.29,
  additionalFeesValuePrevious: 0.25,
  costCenterId: 411,
  costCenterName: 'R115 - CDC - 0512 - Equipment - Medição Belo Horizonte',
  date: '2024-07-01',
  equipmentsValue: 322972.69,
  equipmentsValuePrevious: 312966.74,
  percentageDiff: 3.29,
  percentageDiffConsumptionUsed: 10.78,
  tariffType: 'Cativo',
  tariffValue: 291.19,
  totalValue: 323264.16,
  totalValuePrevious: 312966.99,
  consumptionTotal: 1193.629
}

export const costCenterResultMock2: ICostCenterResult = {
  additionalConsumptionValue: null,
  additionalConsumptionValuePrevious: null,
  additionalFeesValue: 51.24,
  additionalFeesValuePrevious: 86.17,
  costCenterId: 412,
  costCenterName: '0548 - Biocor - Cemig',
  date: '2024-07-01',
  equipmentsValue: 48795593.51,
  equipmentsValuePrevious: 105811683.69,
  percentageDiff: -53.88,
  percentageDiffConsumptionUsed: -53.96,
  tariffType: 'Cativo',
  tariffValue: 291.19,
  totalValue: 48795644.75,
  totalValuePrevious: 105811769.86,
  consumptionTotal: 167573.04
}
