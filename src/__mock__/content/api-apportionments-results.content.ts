import { IApportionmentResult } from '@/@core/domain/ApportionmentResult'
import { IApportionmentResultResponse } from '@/@core/infra/api/ApportionmentsResultsV3/ApportionmentsResultsV3.types'

/** Response */
export const apportionmentResultResponseMock1: IApportionmentResultResponse = {
  additional_consumption_value: 0,
  additional_consumption_value_previous: 0,
  additional_fees_value: 0,
  additional_fees_value_previous: 0,
  apportionment_id: 115,
  date: '2024-09-01',
  equipments_value: 1457916.87,
  equipments_value_previous: 33818938.38,
  percentage_diff: -95.69,
  total_value_current: 1457916.87,
  total_value_previous: 33818938.38
}
export const apportionmentResultResponseMock2: IApportionmentResultResponse = {
  additional_consumption_value: 0,
  additional_consumption_value_previous: 291.18,
  additional_fees_value: 0,
  additional_fees_value_previous: 99.99,
  apportionment_id: 115,
  date: '2024-08-01',
  equipments_value: 33818938.38,
  equipments_value_previous: 94339521.75,
  percentage_diff: -64.15,
  total_value_current: 33818938.38,
  total_value_previous: 94339912.92
}
/** Response parsed domain  */
export const apportionmentResultMock1: IApportionmentResult = {
  additionalConsumptionValue: 0,
  additionalConsumptionValuePrevious: 0,
  additionalFeesValue: 0,
  additionalFeesValuePrevious: 0,
  apportionmentId: 115,
  date: '2024-09-01',
  equipmentsValue: 1457916.87,
  equipmentsValuePrevious: 33818938.38,
  percentageDiff: -95.69,
  totalValueCurrent: 1457916.87,
  totalValuePrevious: 33818938.38
}
export const apportionmentResultMock2: IApportionmentResult = {
  additionalConsumptionValue: 0,
  additionalConsumptionValuePrevious: 291.18,
  additionalFeesValue: 0,
  additionalFeesValuePrevious: 99.99,
  apportionmentId: 115,
  date: '2024-08-01',
  equipmentsValue: 33818938.38,
  equipmentsValuePrevious: 94339521.75,
  percentageDiff: -64.15,
  totalValueCurrent: 33818938.38,
  totalValuePrevious: 94339912.92
}
