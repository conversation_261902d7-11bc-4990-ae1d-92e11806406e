import { Response } from '@/@core/infra/api/PropertiesExternalApiV3/PropertiesExternalApiV3.types'
import { IPropertiesExternal } from '@/@core/domain/PropertiesExternal'

/** Response */
export const propertyExternalResponseMock1: Response = {
  id: 1,
  name: 'U12',
  label: 'Tensão Fase/Fase (A-B)',
  model: { id: 1, name: 'KS-3000' },
  property: { id: 194, name: 'voltab' },
  accumulated: 0
}
export const propertyExternalResponseMock2: Response = {
  id: 2,
  name: 'U12',
  label: 'Tensão Fase/Fase (A-B)',
  model: { id: 2, name: 'KS-3000 LoRa' },
  property: { id: 194, name: 'voltab' },
  accumulated: 0
}
/** Response parsed domain  */
export const propertyExternalMock1: IPropertiesExternal = {
  id: 1,
  name: 'U12',
  label: 'Tensão Fase/Fase (A-B)',
  model: { id: 1, name: 'KS-3000' },
  property: { id: 194, name: 'voltab' },
  accumulated: 0
}
export const propertyExternalMock2: IPropertiesExternal = {
  id: 2,
  name: 'U12',
  label: 'Tensão Fase/Fase (A-B)',
  model: { id: 2, name: 'KS-3000 LoRa' },
  property: { id: 194, name: 'voltab' },
  accumulated: 0
}
