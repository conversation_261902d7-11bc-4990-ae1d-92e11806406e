import { ICostCentersTariff } from '@/@core/domain/CostCentersTariffs'
import { CostCentersTariffsResponse } from '@/@core/infra/api/CostCentersTariffsApiV3/CostCentersTariffsApiV3.types'

/** Response */
export const costCentersTariffsResponseMock1: CostCentersTariffsResponse = {
  id: 1,
  cost_center_id: 101,
  vigency_start: '2025-01-01',
  vigency_end: '2025-12-31',
  value: 10000.0
}

export const costCentersTariffsResponseMock2: CostCentersTariffsResponse = {
  id: 2,
  cost_center_id: 102,
  vigency_start: '2025-02-01',
  vigency_end: '2025-11-30',
  value: 15000.5
}

/** Response parsed domain  */
export const costCentersTariffsMock1: ICostCentersTariff = {
  id: 1,
  costCenterId: 101,
  vigencyStart: '2025-01-01',
  vigencyEnd: '2025-12-31',
  value: 10000.0
}

export const costCentersTariffsMock2: ICostCentersTariff = {
  id: 2,
  costCenterId: 102,
  vigencyStart: '2025-02-01',
  vigencyEnd: '2025-11-30',
  value: 15000.5
}
