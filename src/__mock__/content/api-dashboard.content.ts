import {
  IDashboardTabResponse,
  IDashboardTabStructureResponse
} from '@/@core/infra/api/DashboardsApiV3/DashboardsApiV3.types'
import { IDashboardTab } from '@/@core/domain/DashboardTabs'

/** DynamicListCard */
export const dashboardTabStructureListCardMock: IDashboardTabStructureResponse =
  {
    name: 'DynamicListCard',
    dataStructure: {
      type: 'card',
      cardOptions: {
        title: 'Demanda Instantânea',
        behavior: 'horizontal-list',
        contents: [
          {
            content: {
              unit: 'kW',
              textLabel: '',
              textValue: '',
              fieldLabel: 'entity_name',
              fieldValue: 'value',
              descriptionLabel: '',
              descriptionValue: 'timestamp'
            },
            apiRoute: {
              url: '/api/v3/dashboards/{instance}/populate-chart',
              method: 'post',
              params: {
                id: null,
                limit: 1,
                order: 'desc',
                group_by: 'company',
                properties: ['repo_active'],
                summarize_by: 'sum'
              }
            }
          }
        ]
      }
    },
    position: 1
  }
/** DynamicIntake */
export const dashboardTabStructureIntakeMock: IDashboardTabStructureResponse = {
  name: 'DynamicIntake',
  dataStructure: {
    type: 'card',
    style: {
      classType: 'peak'
    },
    cardOptions: {
      title: 'Consumo Total',
      content: {
        unit: 'MWh',
        title: '',
        value: 'value',
        peakTime: null,
        description: 'endDate'
      },
      apiRoute: '/api/v3/energy-consumption/total-{instance}'
    }
  },
  position: 1
}
/** DynamicDemand */
export const dashboardTabStructureDemandMock: IDashboardTabStructureResponse = {
  name: 'DynamicDemand',
  dataStructure: {
    type: 'card',
    cardOptions: {
      title: 'Demanda Ponta',
      peak: true,
      footer: {
        id: 'usado',
        show: true,
        title: 'Usado',
        fields: {
          firstValue: 'max',
          thirdField: 'percentage_exceeded',
          titleField: 'used',
          secondValue: 'contract',
          firstValueSufix: '',
          titleFieldSufix: '%',
          secondValueSufix: 'kW'
        },
        loading: true
      },
      contents: [
        {
          id: 'consolidada',
          last: false,
          unit: 'kW',
          title: 'Máxima Registrada',
          value: 'max',
          loading: true,
          apiRoute: 'api/v3/demandas/consolidada',
          apiResponse: {
            data: [],
            error: false,
            messageError: 'Algo inesperado ocorreu'
          },
          description: 'time_max'
        },
        {
          id: 'ultima',
          last: true,
          title: 'Última Leitura',
          value: 'value',
          loading: true,
          apiRoute: 'api/v3/demandas/ultima',
          apiResponse: {
            data: [],
            error: false,
            messageError: 'Algo inesperado ocorreu'
          },
          description: 'date'
        }
      ]
    }
  },
  position: 1
}
/** DynamicChart */
export const dashboardTabStructureChartMock: IDashboardTabStructureResponse = {
  name: 'DynamicChart',
  dataStructure: {
    type: 'chart',
    title: 'Perfil de Carga (kW)',
    apiRoutes: {
      main: '/api/v3/grafic-demand/{instance}',
      range: '/api/v3/grafic-demand/max-{instance}'
    },
    rangeTitle: 'Máximo do dia',
    chartOptions: {
      xAxis: {
        type: 'datetime'
      },
      yAxis: {
        title: {
          text: null
        },
        labels: {
          format: '{value} kW'
        }
      },
      legend: {
        enabled: true,
        padding: 5
      },
      series: [
        {
          key: 'peak',
          data: [],
          name: 'Ponta',
          type: 'column',
          color: '#FF5A00',
          groupBy: {
            field: 'peakTime',
            values: [true]
          },
          valueDefault: 'value'
        },
        {
          key: 'reactivedemand',
          data: [],
          name: 'Demanda Reativa',
          type: 'spline',
          color: '#FFA410',
          valueDefault: 'reactiveDemand'
        },
        {
          key: 'outpeak',
          data: [],
          name: 'Fora Ponta',
          type: 'column',
          color: '#35A55B',
          groupBy: {
            field: 'peakTime',
            values: [false, null]
          },
          valueDefault: 'value'
        },
        {
          key: 'contractpeak',
          data: [],
          name: 'Contrato Ponta',
          type: 'spline',
          color: '#FF0000',
          dashStyle: 'Dash',
          valueDefault: 'contractedPeak'
        },
        {
          key: 'contractoutpeak',
          data: [],
          name: 'Contrato Fora Ponta',
          type: 'spline',
          color: '#1010FF',
          dashStyle: 'Dash',
          valueDefault: 'contractedOutOfPeak'
        }
      ],
      tooltip: {
        split: true,
        shared: false,
        pointFormat:
          '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:,.3f} kW</b><br/>',
        valueSuffix: ' kW',
        valueDecimals: 3
      },
      exporting: {
        filename: 'entity_name'
      },
      navigator: {
        baseSeries: 2
      },
      plotOptions: {
        column: {
          minPointLength: 3
        },
        series: {
          gapSize: 1,
          connectNulls: false
        }
      }
    },
    forNoRecords: {
      content: {
        hour: 'time',
        value: null,
        peakTime: false,
        contractedPeak: null,
        reactiveDemand: null,
        contractedOutOfPeak: null
      }
    }
  },
  position: 1
}
/** AlarmsNew */
export const dashboardTabStructureAlarmsNewMock: IDashboardTabStructureResponse =
  {
    name: 'AlarmsNew',
    dataStructure: {
      route: '/alarmsNew'
    },
    position: 1
  }
/** DynamicDonutCard */
export const dashboardTabStructureDonutCardMock: IDashboardTabStructureResponse =
  {
    name: 'DynamicDonutCard',
    dataStructure: {
      type: 'card',
      cardOptions: {
        title: 'Comparação de Energia Estabilizada',
        contents: [
          {
            content: {
              unit: 'kW',
              textLabel: 'Primeiro Andar',
              textValue: '',
              fieldLabel: '',
              fieldValue: 'value'
            },
            apiRoute: {
              url: '/api/v3/dashboards/equipment/populate-chart',
              method: 'post',
              params: {
                id: 166,
                order: 'asc',
                properties: ['active'],
                summarize_by: 'sum'
              }
            }
          },
          {
            content: {
              unit: 'kW',
              textLabel: '',
              textValue: '',
              fieldLabel: '',
              fieldValue: 'value'
            },
            apiRoute: {
              url: '/api/v3/dashboards/equipment/populate-chart',
              method: 'post',
              params: {
                id: 178,
                order: 'desc',
                aggregate: 'sum',
                properties: ['active'],
                summarize_by: 'sum'
              }
            }
          }
        ]
      },
      chartOptions: {
        chart: {
          type: 'pie',
          plotShadow: false,
          plotBorderWidth: null,
          plotBackgroundColor: null
        },
        title: {
          text: ''
        },
        legend: {
          align: 'right',
          layout: 'vertical',
          verticalAlign: 'middle'
        },
        series: [
          {
            innerSize: '80%',
            colorByPoint: true
          }
        ],
        credits: {
          enabled: false
        },
        tooltip: {
          pointFormat: '{point.name} - {point.percentage:.1f} %'
        },
        plotOptions: {
          pie: {
            cursor: 'pointer',
            dataLabels: {
              format: '{point.name}: {point.percentage:.1f} %',
              enabled: true
            },
            showInLegend: true,
            allowPointSelect: true
          }
        },
        accessibility: {
          point: {
            valueSuffix: '%'
          }
        }
      }
    },
    position: 1
  }
/** DynamicChartNew */
export const dashboardTabStructureChartNewMock: IDashboardTabStructureResponse =
  {
    name: 'DynamicWidget',
    dataStructure: {
      body: [
        {
          name: 'DynamicChartNew',
          type: 'dynamic',
          dataStructure: {
            apiRoutes: [
              {
                url: '/api/v3/dashboards/{instance}/processed-data',
                config: 'separate_series',
                method: 'post',
                params: {
                  id: {
                    path: 'id'
                  },
                  limit: 10000,
                  order: 'asc',
                  accumulate: true,
                  final_date: 'last_day',
                  properties: ['modulacao'],
                  initial_date: 'first_day'
                },
                serieskey: 'series0',
                urlParams: {
                  id: {
                    path: 'id'
                  },
                  instance: {
                    path: 'instance'
                  }
                },
                postParamsForm: {
                  final_date: {
                    path: 'date[1]',
                    source: 'head[2].dataStructure.values'
                  },
                  initial_date: {
                    path: 'date[0]',
                    source: 'head[2].dataStructure.values'
                  }
                }
              },
              {
                url: '/api/v3/dashboards/{instance}/processed-data',
                config: 'separate_series',
                method: 'post',
                params: {
                  id: {
                    path: 'id'
                  },
                  limit: 10000,
                  order: 'asc',
                  final_date: 'last_day',
                  properties: ['modulacao_media'],
                  initial_date: 'first_day'
                },
                serieskey: 'series1',
                urlParams: {
                  id: {
                    path: 'id'
                  },
                  instance: {
                    path: 'instance'
                  }
                },
                postParamsForm: {
                  final_date: {
                    path: 'date[1]',
                    source: 'head[2].dataStructure.values'
                  },
                  initial_date: {
                    path: 'date[0]',
                    source: 'head[2].dataStructure.values'
                  }
                }
              }
            ],
            chartOptions: {
              xAxis: {
                type: 'datetime'
              },
              yAxis: {
                title: {
                  text: null
                },
                labels: {
                  format: 'R$ {value}'
                }
              },
              legend: {
                enabled: true,
                padding: 5
              },
              series: [
                {
                  key: 'series0',
                  data: [],
                  name: 'Modulação',
                  type: 'area',
                  color: '#50df92',
                  showInNavigator: true
                },
                {
                  key: 'series1',
                  data: [],
                  name: 'Resultado Médio',
                  type: 'spline',
                  color: '#203932',
                  showInNavigator: true
                }
              ],
              tooltip: {
                split: true,
                shared: true
              },
              plotOptions: {
                area: {
                  stacking: 'normal'
                }
              }
            }
          }
        }
      ],
      head: [
        {
          name: 'DynamicIcon',
          type: 'dynamic',
          dataStructure: {
            type: 'z-icon',
            iconTitle: 'thunderbolt-24px'
          }
        },
        {
          list: [
            {
              spanTitle: 'Modulação Horária'
            }
          ],
          type: 'span'
        },
        {
          name: 'DynamicFormGrid',
          type: 'dynamic',
          dataStructure: {
            rules: [
              {
                group: [
                  {
                    type: 'default',
                    label: 'Período',
                    model: 'date',
                    range: true,
                    format: 'DD/MM/YY HH:mm',
                    confirm: true,
                    clearable: true,
                    dateRange: null,
                    fieldType: 'datepicker'
                  }
                ]
              }
            ],
            values: {
              date: null
            },
            default: {
              date: 'default'
            }
          }
        }
      ]
    },
    position: 2
  }
/** DynamicChartGauge */
export const dashboardTabStructureChartGaugeMock: IDashboardTabStructureResponse =
  {
    name: 'DynamicChartGauge',
    dataStructure: {
      apiRoutes: [
        {
          url: '/api/v3/dashboards/{instance}/consumption-data',
          config: 'separate_series',
          method: 'post',
          params: {
            id: {
              path: 'id'
            },
            limit: 1,
            order: 'desc',
            final_date: 'now',
            properties: ['consumo_ativo'],
            initial_date: 'first'
          },
          urlParams: {
            id: {
              path: 'id'
            },
            instance: {
              path: 'instance'
            }
          },
          postParamsForm: {
            final_date: {
              path: 'date[1]',
              source: 'head[1].dataStructure.values'
            },
            initial_date: {
              path: 'date[0]',
              source: 'head[1].dataStructure.values'
            }
          }
        }
      ],
      chartOptions: {
        pane: {
          size: '140%',
          center: ['50%', '85%'],
          endAngle: 90,
          background: {
            shape: 'arc',
            innerRadius: '60%',
            outerRadius: '100%',
            backgroundColor: '#EEE'
          },
          startAngle: -90
        },
        chart: {
          type: 'solidgauge'
        },
        yAxis: {
          max: 100,
          min: 0,
          stops: [
            [0.1, '#f60f60'],
            [0.7, '#000000']
          ],
          title: {
            y: -50
          },
          labels: {
            y: 16
          },
          lineWidth: 0,
          tickWidth: 0,
          tickAmount: 2,
          minorTickInterval: null
        },
        series: [
          {
            data: [],
            tooltip: {
              pointFormat:
                '<span>{series.name}</span>: <b>{point.y:,.2f} kWh</b><br/>',
              valueDecimals: 2
            },
            dataLabels: {
              format:
                '<div style="text-align:center;font-weight:normal"><span style="font-size:20px;color:{point.color}">{y: .2f}%</span></span></div>'
            }
          }
        ],
        tooltip: {
          pointFormat:
            '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:,.2f} kWh</b><br/>',
          valueDecimals: 2
        },
        plotOptions: {
          solidgauge: {
            dataLabels: {
              y: 1,
              useHTML: true,
              borderWidth: 0
            }
          }
        }
      }
    },
    position: 1
  }
/** DynamicDefault */
export const dashboardTabStructureDefaultMock: IDashboardTabStructureResponse =
  {
    name: 'DynamicDefault',
    dataStructure: {},
    position: 1
  }

/** Response */
export const dashboardTabResponseMock1: IDashboardTabResponse = {
  label: 'Medição',
  status: true,
  structure: [],
  value: 1
}
export const dashboardTabResponseMock2: IDashboardTabResponse = {
  label: 'Alarme',
  status: true,
  structure: [],
  value: 1
}

/** Response parsed domain  */
export const dashboardTabMock1: IDashboardTab = {
  label: 'Medição',
  xWidgets: []
}
export const dashboardTabMock2: IDashboardTab = {
  label: 'Alarme',
  xWidgets: []
}
