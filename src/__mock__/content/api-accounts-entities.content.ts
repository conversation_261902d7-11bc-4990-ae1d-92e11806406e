import { IAccountsEntities } from '@/@core/domain/AccountsEntities'
import { AccountsEntitiesResponse } from '@/@core/infra/api/AccountsEntitiesApiV3/AccountsEntitiesApiV3.types'

/** Response */
export const accountsEntitiesResponseMock1: AccountsEntitiesResponse = {
  entity: {
    id: 1,
    name: 'Root Company 1',
    company_id_parent: null
  },
  account_entity: {
    id: 1,
    type: 'Company'
  }
}
export const accountsEntitiesResponseMock2: AccountsEntitiesResponse = {
  entity: {
    id: 2,
    name: 'Child Company',
    company_id_parent: 1
  },
  account_entity: {
    id: 102,
    type: 'Company'
  }
}

/** Response parsed domain  */
export const accountsEntitiesMock1: IAccountsEntities = {
  entityId: 1,
  entityName: 'Root Company 1',
  entityCompanyId: null,
  accountEntityId: 1,
  accountEntityType: 'Company'
}

export const accountsEntitiesMock2: IAccountsEntities = {
  entityId: 2,
  entityName: 'Root Company 2',
  entityCompanyId: null,
  accountEntityId: 32,
  accountEntityType: 'Company'
}

export const accountsEntitiesMock3: IAccountsEntities = {
  entityId: 3,
  entityName: 'Child Company 3',
  entityCompanyId: 1,
  accountEntityId: 2,
  accountEntityType: 'Company'
}

export const accountsEntitiesMock4: IAccountsEntities = {
  entityId: 4,
  entityName: 'Child Company 4',
  entityCompanyId: 3,
  accountEntityId: 33,
  accountEntityType: 'Equipment'
}
