import {
  ResponseList,
  Response
} from '@/@core/infra/api/IntegrationsAccountsApiV3/IntegrationsAccountsApiV3.types'
import IIntegrationsAccounts, {
  IIntegrationAccount
} from '@/@core/domain/IntegrationAccount'

/** Response */
export const integrationsAccountsResponseMock1: ResponseList = {
  id: 24,
  name: '024 - Integrations - Biocor - 15 minutos',
  integration: {
    id: 2,
    name: 'Gestal'
  },
  account: {
    id: 45,
    name: 'Gesta<PERSON>'
  },
  user: {
    id: 18,
    name: 'Demonstração'
  }
}
export const integrationsAccountsResponseMock2: ResponseList = {
  id: 23,
  name: '023 - Gestal Integration - horário',
  integration: {
    id: 2,
    name: 'Gestal'
  },
  account: {
    id: 45,
    name: 'Gestal'
  },
  user: {
    id: 18,
    name: 'Demonstração'
  }
}
export const integrationsAccounstResponseMock1: Response = {
  id: 24,
  name: '024 - Integrations - Biocor - 15 minutos',
  integration: {
    id: 2,
    name: '<PERSON>esta<PERSON>'
  },
  account: {
    id: 45,
    name: 'Gesta<PERSON>'
  },
  user: {
    id: 18,
    name: '<PERSON>tra<PERSON>'
  },
  token: 'Apiopj8Os06msSC1',
  username: null,
  frequency: {
    id: 1,
    name: '15 minutos'
  },
  delay: 10
}
export const integrationsAccounstResponseMock2: Response = {
  id: 23,
  name: '023 - Gestal Integration - horário',
  integration: {
    id: 2,
    name: 'Gestal'
  },
  account: {
    id: 45,
    name: 'Gestal'
  },
  user: {
    id: 18,
    name: 'Demonstração'
  },
  token: null,
  username: null,
  frequency: {
    id: 2,
    name: 'horário'
  },
  delay: 10
}

/** Response parsed domain  */
export const integrationsAccountsMock1: IIntegrationsAccounts = {
  id: 24,
  name: '024 - Integrations - Biocor - 15 minutos',
  integration: {
    id: 2,
    name: 'Gestal'
  },
  integrationId: 2,
  account: {
    id: 45,
    name: 'Gestal'
  },
  accountId: 45,
  user: {
    id: 18,
    name: 'Demonstração'
  },
  userId: 18
}
export const integrationsAccountsMock2: IIntegrationsAccounts = {
  id: 23,
  name: '023 - Gestal Integration - horário',
  integration: {
    id: 2,
    name: 'Gestal'
  },
  integrationId: 2,
  account: {
    id: 45,
    name: 'Gestal'
  },
  accountId: 45,
  user: {
    id: 18,
    name: 'Demonstração'
  },
  userId: 18
}
export const integrationAccountMock1: IIntegrationAccount = {
  id: 24,
  name: '024 - Integrations - Biocor - 15 minutos',
  integration: {
    id: 2,
    name: 'Gestal'
  },
  integrationId: 2,
  account: {
    id: 45,
    name: 'Gestal'
  },
  accountId: 45,
  user: {
    id: 18,
    name: 'Demonstração'
  },
  userId: 18,
  token: 'Apiopj8Os06msSC1',
  username: '',
  frequency: {
    id: 1,
    name: '15 minutos'
  },
  delay: 10
}
export const integrationAccountMock2: IIntegrationAccount = {
  id: 23,
  name: '023 - Gestal Integration - horário',
  integration: {
    id: 2,
    name: 'Gestal'
  },
  integrationId: 2,
  account: {
    id: 45,
    name: 'Gestal'
  },
  accountId: 45,
  user: {
    id: 18,
    name: 'Demonstração'
  },
  userId: 18,
  token: '',
  username: '',
  frequency: {
    id: 2,
    name: 'horário'
  },
  delay: 10
}
