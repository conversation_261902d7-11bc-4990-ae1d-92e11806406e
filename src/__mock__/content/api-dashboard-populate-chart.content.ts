import IDashboardPopulate<PERSON>hart from '@/@core/domain/DashboardPopulateChart'
import { Response } from '@/@core/infra/api/DashboardPopulateChartApiV3/DashboardPopulateChartApiV3.types'

/** Response */
export const dashboardPopulateChartResponseMock1: Response = {
  series: [
    {
      entity: 'equipment',
      entity_name: '0538 - Equipment Gas',
      property: 'gflow',
      data: [
        { timestamp: 1709820000, value: 141.96204079904697 },
        { timestamp: 1709820900, value: 108.63877006358726 },
        { timestamp: 1709821800, value: 120.99667139253958 }
      ]
    }
  ],
  summary: null
}
export const dashboardPopulateChartResponseMock2: Response = {
  series: null,
  summary: {
    entity: 'equipment',
    entity_name: '0538 - Equipment Gas',
    property: 'gflow_normalized',
    data: [
      { timestamp: 1709820000, value: 122.15027949239739 },
      { timestamp: 1709820900, value: 134.62778793327539 },
      { timestamp: 1709823600, value: 120.80370310647285 }
    ]
  }
}

export const dashboardPopulateChartMock1: IDashboardPopulateChart = {
  series: [
    {
      entity: 'equipment',
      entityName: '0538 - Equipment Gas',
      property: 'gflow',
      data: [
        { timestamp: 1709820000, value: 141.96204079904697 },
        { timestamp: 1709820900, value: 108.63877006358726 },
        { timestamp: 1709821800, value: 120.99667139253958 }
      ]
    }
  ],
  summary: null
}

export const dashboardPopulateChartMock2: IDashboardPopulateChart = {
  series: [],
  summary: {
    entity: 'equipment',
    entityName: '0538 - Equipment Gas',
    property: 'gflow_normalized',
    data: [
      { timestamp: 1709820000, value: 122.15027949239739 },
      { timestamp: 1709820900, value: 134.62778793327539 },
      { timestamp: 1709823600, value: 120.80370310647285 }
    ]
  }
}
