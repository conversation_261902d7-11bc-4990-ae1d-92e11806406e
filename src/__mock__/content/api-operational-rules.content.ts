import { Response } from '@/@core/infra/api/OperationalRulesApiV3/OperationalRulesApiV3.types'
import { IOperationalRule } from '@/@core/domain/OperationalRule'

/** Response */
export const operationalRuleResponseMock1: Response = {
  id: 1,
  title: 'soma'
}
export const operationalRuleResponseMock2: Response = {
  id: 2,
  title: 'subtração'
}
/** Response parsed domain  */
export const operationalRuleMock1: IOperationalRule = {
  id: 1,
  title: 'soma'
}
export const operationalRuleMock2: IOperationalRule = {
  id: 2,
  title: 'subtração'
}
