import { ICostCenterEquipment } from '@/@core/domain/CostCenterEquipment'
import { ICostCenterEquipmentResponse } from '@/@core/infra/api/CostCentersEquipmentsApiV3/CostCentersEquipmentsApiV3.types'

/** Response */
export const costCenterEquipmentResponseMock1: ICostCenterEquipmentResponse = {
  id: 371,
  cost_center_id: 422,
  usage_limit: 100,
  equipment: { id: 918, name: 'Valência e Dias Ltda.' },
  group: null,
  cost_composition_type: { id: 1, name: 'Fixa' }
}
export const costCenterEquipmentResponseMock2: ICostCenterEquipmentResponse = {
  id: 375,
  cost_center_id: 426,
  usage_limit: 100,
  equipment: { id: 3691, name: 'Benites Comercial Ltda.' },
  group: null,
  cost_composition_type: { id: 1, name: 'Fixa' }
}

/** Response parsed domain  */
export const costCenterEquipmentMock1: ICostCenterEquipment = {
  id: 371,
  costCenterId: 422,
  usageLimit: 100,
  equipment: { id: 918, name: 'Valência e Dias Ltda.' },
  group: null,
  costCompositionType: { id: 1, name: 'Fixa' }
}
export const costCenterEquipmentMock2: ICostCenterEquipment = {
  id: 375,
  costCenterId: 426,
  usageLimit: 100,
  equipment: { id: 3691, name: 'Benites Comercial Ltda.' },
  group: null,
  costCompositionType: { id: 1, name: 'Fixa' }
}
