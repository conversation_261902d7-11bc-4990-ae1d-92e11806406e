import { Response } from '@/@core/infra/api/CompaniesContactsApiV4/CompaniesContactsApiV4.types'
import { ICompanyContacts } from '@/@core/domain/CompanyContacts'

export const companyContactsResponseMock: Response = {
  id: 1,
  company_contact_category_id: 1,
  company_id: 1,
  name: 'name',
  email: 'email',
  telephone: 'telephone',
  cellphone: 'cellphone',
  company: {
    id: 1,
    nome: 'nome',
    empresa_pai_id: 2
  },
  company_contact_category: {
    id: 1,
    name: 'name'
  }
}

export const companyContactsResponseMock2: Response = {
  id: 2,
  company_contact_category_id: 2,
  company_id: 2,
  name: 'name',
  email: 'email',
  telephone: 'telephone',
  cellphone: 'cellphone',
  company: {
    id: 2,
    nome: 'nome',
    empresa_pai_id: 3
  },
  company_contact_category: {
    id: 2,
    name: 'name'
  }
}

export const companyContactMock1: ICompanyContacts = {
  id: 1,
  name: 'name',
  email: 'email',
  telephone: 'telephone',
  cellphone: 'cellphone',
  companyId: 1,
  companyContactCategory: {
    id: 1,
    name: 'name'
  }
}

export const companyContactMock2: ICompanyContacts = {
  id: 2,
  name: 'name',
  email: 'email',
  telephone: 'telephone',
  cellphone: 'cellphone',
  companyId: 2,
  companyContactCategory: {
    id: 2,
    name: 'name'
  }
}
