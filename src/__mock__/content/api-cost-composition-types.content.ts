import { ICostCompositionType } from '@/@core/domain/CostCompositionTypes'
import { CostCompositionTypesResponse } from '@/@core/infra/api/CostCompositionTypesApiV3/CostCompositionTypesApiV3.types'

/** Response */
export const costCompositionTypesResponseMock1: CostCompositionTypesResponse = {
  id: 1,
  name: 'Fixa',
  deleted_at: null
}

export const costCompositionTypesResponseMock2: CostCompositionTypesResponse = {
  id: 2,
  name: 'Proporcional',
  deleted_at: '2025-02-01'
}

/** Response parsed domain  */
export const costCompositionTypesMock1: ICostCompositionType = {
  id: 1,
  name: 'Fixa',
  deletedAt: ''
}

export const costCompositionTypesMock2: ICostCompositionType = {
  id: 2,
  name: 'Proporcional',
  deletedAt: '2025-02-01'
}
