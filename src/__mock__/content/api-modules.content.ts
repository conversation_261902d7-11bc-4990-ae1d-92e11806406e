import { IModule } from '@/@core/domain'

export const moduleMock1: IModule = {
  id: 21,
  name: '<PERSON><PERSON><PERSON>',
  slug: 'management'
}
export const moduleMock2: IModule = {
  id: 22,
  name: 'Con<PERSON>',
  slug: 'accounts'
}
export const moduleMock3: IModule = {
  id: 24,
  name: 'Distribuidoras',
  slug: 'distributors',
  parentId: 22
}
export const moduleMock4: IModule = {
  id: 25,
  name: 'module 4',
  slug: 'slug-4',
  parentId: 21
}
export const moduleMock5: IModule = {
  id: 26,
  name: 'module 5',
  slug: 'slug-5',
  parentId: 21
}
