import { ICostCenter } from '@/@core/domain/CostCenters'
import { CostCentersResponse } from '@/@core/infra/api/CostCentersApiV3/CostCentersApiV3.types'

export const costCenterResponseMock1: CostCentersResponse = {
  id: 1,
  apportionment_id: 101,
  name: '<PERSON><PERSON><PERSON>',
  email: '<EMAIL>',
  apportionment_tariff_type: {
    id: 10,
    name: '<PERSON><PERSON><PERSON>'
  },
  equipments: [
    {
      id: 201,
      cost_center_id: 301,
      equipment_id: 401,
      usage_limit: 100
    },
    {
      id: 202,
      cost_center_id: 302,
      equipment_id: 402,
      usage_limit: 200
    }
  ],
  company_id: 123
}

export const costCenterResponseMock2: CostCentersResponse = {
  id: 2,
  apportionment_id: 102,
  name: 'Fernanda Lima',
  email: '<EMAIL>',
  apportionment_tariff_type: {
    id: 11,
    name: '<PERSON><PERSON><PERSON>uz<PERSON>'
  },
  equipments: [
    {
      id: 203,
      cost_center_id: 303,
      equipment_id: 403,
      usage_limit: 150
    }
  ],
  company_id: 234
}

export const costCenterMock1: ICostCenter = {
  id: 1,
  apportionmentId: 101,
  name: 'Caio Silva',
  email: '<EMAIL>',
  apportionmentTariffType: {
    id: 10,
    name: 'Tarifa Padrão'
  },
  equipments: [
    {
      id: 201,
      costCenterId: 301,
      equipmentId: 401,
      usageLimit: 100
    },
    {
      id: 202,
      costCenterId: 302,
      equipmentId: 402,
      usageLimit: 200
    }
  ],
  companyId: 123
}
export const costCenterMock2: ICostCenter = {
  id: 2,
  apportionmentId: 102,
  name: 'Fernanda Lima',
  email: '<EMAIL>',
  apportionmentTariffType: {
    id: 11,
    name: 'Tarifa Reduzida'
  },
  equipments: [
    {
      id: 203,
      costCenterId: 303,
      equipmentId: 403,
      usageLimit: 150
    }
  ],
  companyId: 234
}
