import { IMe } from '@/@core/domain'
import { MeResponse as Response } from '@/@core/infra/api/AuthApiV3/AuthApiV3.types'

export const meResponseMock1: Response = {
  user: {
    id: 1,
    name: 'demo',
    avatar: '',
    cellphone: '',
    email: '<EMAIL>',
    active: false,
    admin: false
  },
  account: {
    id: 1,
    name: 'account zordon'
  },
  account_user: {
    id: 1,
    admin: false
  },
  groups: []
}

export const meMock1: IMe = {
  user: {
    id: 1,
    name: 'demo',
    avatar: '',
    cellphone: '',
    email: '<EMAIL>',
    active: false,
    admin: false
  },
  accountId: 1,
  accountName: 'account zordon',
  accountUserId: 1,
  accountUserAdmin: false,
  groups: [],
  cognitoUserId: null,
  notification: false
}
