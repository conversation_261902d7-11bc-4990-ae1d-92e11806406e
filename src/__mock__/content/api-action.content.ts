import { IAction } from '@/@core/domain/Actions'

export const actionMock1: IAction = {
  id: 45,
  name: 'Listar usuários',
  slug: 'list-users',
  module: { id: 15, name: '<PERSON><PERSON><PERSON><PERSON>' },
  moduleId: 15,
  submodule: { id: 20, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  submoduleId: 20
}

export const actionMock2: IAction = {
  id: 28,
  name: 'Listar Alarmes',
  slug: 'list-alarms',
  module: { id: 13, name: '<PERSON>arm<PERSON>' },
  moduleId: 13,
  submodule: null,
  submoduleId: null
}

export const actionMock3: IAction = {
  id: 46,
  name: '<PERSON><PERSON><PERSON>',
  slug: 'create-users',
  module: { id: 15, name: '<PERSON><PERSON><PERSON><PERSON>' },
  moduleId: 15,
  submodule: { id: 20, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  submoduleId: 20
}

export const actionMock4: IAction = {
  id: 47,
  name: '<PERSON><PERSON><PERSON>',
  slug: 'delete-users',
  module: { id: 15, name: '<PERSON><PERSON><PERSON><PERSON>' },
  moduleId: 15,
  submodule: { id: 20, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  submoduleId: 20
}
