import { IMenuDashboard } from '@/@core/domain/MenuDashboard'
import { IMenuResponse } from '@/@core/infra/api/MenuApiV4/MenuApiV4.types'

export const menuDashboardResponseMock1: IMenuResponse = {
  entity_id: 119,
  entity_name: '0119 - DOCLABS',
  entity_type: 'company',
  entity_data: null,
  entities: [
    {
      entity_id: 166,
      entity_name: '0166 - AC 2o Pavimento',
      entity_type: 'equipment',
      entity_data: {
        device_code: '79907616646778',
        device_id: 155,
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    },
    {
      entity_id: 167,
      entity_name: '0167 - QDE 2o Pavimento',
      entity_type: 'equipment',
      entity_data: {
        device_code: '79907216646778',
        device_id: 154,
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    },
    {
      entity_id: 168,
      entity_name: '0168 - QDE 3o Pavimento',
      entity_type: 'equipment',
      entity_data: {
        device_code: '79908216646778',
        device_id: 157,
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    }
  ],
  timezone: -3
}
export const menuDashboardResponseMock2: IMenuResponse = {
  entity_id: 117,
  entity_name: '117 Empresa Filial',
  entity_type: 'company',
  entity_data: null,
  entities: [
    {
      entity_id: 187,
      entity_name: '0187 - Equipment water',
      entity_type: 'equipment',
      entity_data: {
        device_code: '226_water_187',
        device_id: 226,
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    },
    {
      entity_id: 190,
      entity_name: 'Equipamento 190',
      entity_type: 'equipment',
      entity_data: {
        device_code: '123213',
        device_id: 227,
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    },
    {
      entity_id: 130,
      entity_name: 'PoC MQTT',
      entity_type: 'company',
      entity_data: null,
      entities: [
        {
          entity_id: 214,
          entity_name: 'Equipamento 214 - PoC MQTT',
          entity_type: 'equipment',
          entity_data: {
            device_code: '1668113100112360',
            device_id: 181,
            status: {
              name: 'Desconectado',
              slug: 'desconectado'
            }
          },
          entities: [],
          timezone: -3
        }
      ],
      timezone: -3
    }
  ],
  timezone: -3
}
export const menuDashboardResponseMock3: IMenuResponse = {
  entity_id: 136,
  entity_name: '136 - SHOPPING PN',
  entity_type: 'company',
  entity_data: null,
  entities: [
    {
      entity_id: 275,
      entity_name: '0275 - Contact 1',
      entity_type: 'equipment',
      entity_data: {
        device_code: 'replicate_4C752584CEBC_pls_contact_1',
        device_id: 242,
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    }
  ],
  timezone: -3
}
export const menuDashboardMock1: IMenuDashboard = {
  entityId: 119,
  entityName: '0119 - DOCLABS',
  entityType: 'company',
  entityData: null,
  entities: [
    {
      entityId: 166,
      entityName: '0166 - AC 2o Pavimento',
      entityType: 'equipment',
      entityData: {
        device: {
          code: '79907616646778',
          id: 155
        },
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    },
    {
      entityId: 167,
      entityName: '0167 - QDE 2o Pavimento',
      entityType: 'equipment',
      entityData: {
        device: {
          code: '79907216646778',
          id: 154
        },
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    },
    {
      entityId: 168,
      entityName: '0168 - QDE 3o Pavimento',
      entityType: 'equipment',
      entityData: {
        device: {
          code: '79908216646778',
          id: 157
        },
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    }
  ],
  timezone: -3
}
export const menuDashboardMock2: IMenuDashboard = {
  entityId: 117,
  entityName: '117 Empresa Filial',
  entityType: 'company',
  entityData: null,
  entities: [
    {
      entityId: 187,
      entityName: '0187 - Equipment water',
      entityType: 'equipment',
      entityData: {
        device: {
          code: '226_water_187',
          id: 226
        },
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    },
    {
      entityId: 190,
      entityName: 'Equipamento 190',
      entityType: 'equipment',
      entityData: {
        device: {
          code: '123213',
          id: 227
        },
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    },
    {
      entityId: 130,
      entityName: 'PoC MQTT',
      entityType: 'company',
      entityData: null,
      entities: [
        {
          entityId: 214,
          entityName: 'Equipamento 214 - PoC MQTT',
          entityType: 'equipment',
          entityData: {
            device: {
              code: '1668113100112360',
              id: 181
            },
            status: {
              name: 'Desconectado',
              slug: 'desconectado'
            }
          },
          entities: [],
          timezone: -3
        }
      ],
      timezone: -3
    }
  ],
  timezone: -3
}
export const menuDashboardMock3: IMenuDashboard = {
  entityId: 136,
  entityName: '136 - SHOPPING PN',
  entityType: 'company',
  entityData: null,
  entities: [
    {
      entityId: 275,
      entityName: '0275 - Contact 1',
      entityType: 'equipment',
      entityData: {
        device: {
          code: 'replicate_4C752584CEBC_pls_contact_1',
          id: 242
        },
        status: {
          name: 'Desconectado',
          slug: 'desconectado'
        }
      },
      entities: [],
      timezone: -3
    }
  ],
  timezone: -3
}
