import { Response } from '@/@core/infra/api/UserActionsApiV3/UserActionsApiV3.types'
import IUserAction from '@/@core/domain/UserAction'

/** Response */
export const userActionResponseMock1: Response = {
  id: 2425,
  action: { id: 45, name: 'Listar Usuários' },
  module: { id: 15, name: 'Cadastros', slug: 'register' },
  submodule: { id: 20, name: 'Usuários', slug: 'users' }
}
export const userActionResponseMock2: Response = {
  id: 2170,
  action: { id: 18, name: 'Deletar Rateios' },
  module: { id: 8, name: 'Rateios', slug: 'apportionments' },
  submodule: null
}
/** Response parsed domain  */
export const userActionMock1: IUserAction = {
  id: 2425,
  action: { id: 45, name: 'Listar Usuários' },
  actionId: 45,
  actionName: 'Listar Usuários',
  module: { id: 15, name: '<PERSON><PERSON><PERSON><PERSON>', slug: 'register' },
  moduleId: 15,
  moduleName: 'Cadastros',
  moduleSlug: 'register',
  submodule: { id: 20, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', slug: 'users' },
  submoduleId: 20,
  submoduleName: 'Usuários',
  submoduleSlug: 'users'
}
export const userActionMock2: IUserAction = {
  id: 2170,
  action: { id: 18, name: 'Deletar Rateios' },
  actionId: 18,
  actionName: 'Deletar Rateios',
  module: { id: 8, name: 'Rateios', slug: 'apportionments' },
  moduleId: 8,
  moduleName: 'Rateios',
  moduleSlug: 'register',
  submodule: null,
  submoduleId: null,
  submoduleName: null,
  submoduleSlug: null
}
export const userActionMock3: IUserAction = {
  id: 2426,
  action: { id: 46, name: 'Criar Usuário' },
  actionId: 46,
  actionName: 'Criar Usuário',
  module: { id: 15, name: 'Cadastros', slug: 'register' },
  moduleId: 15,
  moduleName: 'Cadastros',
  moduleSlug: 'register',
  submodule: { id: 20, name: 'Usuários', slug: 'users' },
  submoduleId: 20,
  submoduleName: 'Usuários',
  submoduleSlug: 'users'
}
export const userActionMock4: IUserAction = {
  id: 2427,
  action: { id: 47, name: 'Deletar Usuário' },
  actionId: 47,
  actionName: 'Deletar Usuário',
  module: { id: 15, name: 'Cadastros', slug: 'register' },
  moduleId: 15,
  moduleName: 'Cadastros',
  moduleSlug: 'register',
  submodule: { id: 20, name: 'Usuários', slug: 'users' },
  submoduleId: 20,
  submoduleName: 'Usuários',
  submoduleSlug: 'users'
}
