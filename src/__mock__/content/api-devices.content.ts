import { Response } from '@/@core/infra/api/DevicesApiV4/DevicesApiV4.types'
import IDevice from '@/@core/domain/Device'

/** Response */
export const deviceResponseMock1: Response = {
  alert_seconds: 3600,
  code: '4C752584D5E4_mdb_rtu_108',
  comment: null,
  company: { id: 132, name: 'SCIJK - T1 - LOJAS' },
  connection: 'gsm',
  current_relation: 1,
  equipment: { id: 446, name: '312 - SANTA LOLLA' },
  hourly_frequency: 4,
  iccid: '',
  id: 412,
  ip: null,
  master: null,
  meter_constant: '0.25',
  model: null,
  potential_relation: 1,
  ssid_op: 'Claro',
  status: { id: 2, name: '<PERSON>con<PERSON>tad<PERSON>' },
  subtype: { id: 1, name: '<PERSON><PERSON>' },
  type: { id: 1, name: 'Energia' },
  url_firmware: '',
  version: '123.456T7'
}
export const deviceResponseMock2: Response = {
  alert_seconds: 3600,
  code: '7992454C752584C820',
  comment: null,
  company: null,
  connection: 'gsm',
  current_relation: 1,
  equipment: null,
  hourly_frequency: 4,
  iccid: null,
  id: 446,
  ip: null,
  master: {
    id: 1,
    code: 'code-1'
  },
  meter_constant: '0.25',
  model: null,
  potential_relation: 1,
  ssid_op: 'Claro',
  status: { id: 2, name: 'Desconectado' },
  subtype: { id: 1, name: 'Fronteira' },
  type: { id: 1, name: 'Energia' },
  url_firmware: null,
  version: '123.456T7'
}
/** Response parsed domain  */
export const deviceMock1: IDevice = {
  id: 412,
  code: '4C752584D5E4_mdb_rtu_108',
  connection: 'gsm',
  urlFirmware: '',
  iccid: '',
  version: '123.456T7',
  ip: '',
  alertSeconds: 3600,
  ssidOp: 'Claro',
  hourlyFrequency: 4,
  modelId: null,
  model: null,
  equipmentId: 446,
  equipment: { id: 446, name: '312 - SANTA LOLLA' },
  companyId: 132,
  company: { id: 132, name: 'SCIJK - T1 - LOJAS' },
  typeId: 1,
  type: { id: 1, name: 'Energia' },
  subtypeId: 1,
  subtype: { id: 1, name: 'Fronteira' },
  statusId: 2,
  status: { id: 2, name: 'Desconectado' },
  masterId: null,
  master: null,
  comment: null
}
export const deviceMock2: IDevice = {
  id: 446,
  code: '7992454C752584C820',
  connection: 'gsm',
  urlFirmware: '',
  iccid: '',
  version: '123.456T7',
  ip: '',
  alertSeconds: 3600,
  ssidOp: 'Claro',
  hourlyFrequency: 4,
  modelId: null,
  model: null,
  equipmentId: null,
  equipment: null,
  companyId: null,
  company: null,
  typeId: 1,
  type: { id: 1, name: 'Energia' },
  subtypeId: 1,
  subtype: { id: 1, name: 'Fronteira' },
  statusId: 2,
  status: { id: 2, name: 'Desconectado' },
  masterId: 1,
  master: {
    id: 1,
    code: 'code-1'
  },
  comment: null
}
