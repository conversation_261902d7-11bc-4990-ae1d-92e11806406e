import {
  ResponseList,
  Response
} from '@/@core/infra/api/DistributorsApiV4/DistributorsApiV4.types'
import { IDistributorList, IDistributor } from '@/@core/domain/Distributor'

/** Response */
export const distributorListResponseMock1: ResponseList = {
  id: 11,
  name: 'CELESC',
  state: { id: 24, name: 'Santa Catarina' },
  submarket: { id: 1, name: 'Sul', acronyms: 'S' },
  type: { id: 1, name: 'Energia' }
}
export const distributorListResponseMock2: ResponseList = {
  id: 12,
  name: 'CESP',
  state: { id: 26, name: 'São Paulo' },
  submarket: { id: 2, name: 'Sudeste/Centro Oeste', acronyms: 'SE/CO' },
  type: { id: 1, name: 'Energia' }
}
export const distributorResponseMock1: Response = {
  id: 11,
  name: 'CELESC',
  state: { id: 24, name: 'Santa Catarina' },
  submarket: { id: 1, name: 'Sul', acronyms: 'S' },
  type: { id: 1, name: 'Energia' },
  cnpj: '83878892000155',
  external_id: '00001301',
  reactive_post: {
    id: 11,
    start_time: '00:00:00',
    end_time: '06:00:00'
  },
  tariff_post: {
    id: 11,
    peak_start_time: '18:45:00',
    peak_end_time: '21:30:00',
    intermediate_start_time: null,
    intermediate_end_time: null
  }
}
/** Response parsed domain  */
export const distributorListMock1: IDistributorList = {
  id: 11,
  name: 'CELESC',
  state: { id: 24, name: 'Santa Catarina' },
  stateId: 24,
  stateName: 'Santa Catarina',
  type: { id: 1, name: 'Energia' },
  typeId: 1,
  typeName: 'Energia',
  submarket: { id: 1, name: 'Sul', acronyms: 'S' },
  submarketId: 1,
  submarketName: 'Sul'
}
export const distributorListMock2: IDistributorList = {
  id: 12,
  name: 'CESP',
  state: { id: 26, name: 'São Paulo' },
  stateId: 26,
  stateName: 'São Paulo',
  type: { id: 1, name: 'Energia' },
  typeId: 1,
  typeName: 'Energia',
  submarket: { id: 2, name: 'Sudeste/Centro Oeste', acronyms: 'SE/CO' },
  submarketId: 2,
  submarketName: 'Sudeste/Centro Oeste'
}
export const distributorMock1: IDistributor = {
  id: 11,
  name: 'CELESC',
  state: { id: 24, name: 'Santa Catarina' },
  stateId: 24,
  stateName: 'Santa Catarina',
  submarket: { id: 1, name: 'Sul', acronyms: 'S' },
  submarketId: 1,
  submarketName: 'Sul',
  type: { id: 1, name: 'Energia' },
  typeId: 1,
  typeName: 'Energia',
  cnpj: '83878892000155',
  externalId: '00001301',
  reactivePost: {
    id: 11,
    startTime: '00:00:00',
    endTime: '06:00:00'
  },
  reactivePostId: 11,
  tariffPost: {
    id: 11,
    peakStartTime: '18:45:00',
    peakEndTime: '21:30:00',
    intermediateStartTime: null,
    intermediateEndTime: null
  },
  tariffPostId: 11
}
