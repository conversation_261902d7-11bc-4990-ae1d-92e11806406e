import IMetric from '@/@core/domain/Metric'

export const metricMockGroup1: IMetric = {
  id: 1,
  name: 'Instalações',
  indicators: [
    {
      id: 5,
      name: 'New devices',
      slug: 'new-devices',
      type: 'count',
      metrics: [
        { name: 'Novos', values: [{ timestamp: 1701399600, value: 3 }] }
      ]
    },
    {
      id: 10,
      name: 'New Devices Energy Sectorized',
      slug: 'new-devices-energy-sectorized',
      type: 'count',
      metrics: [
        {
          name: 'Energia Setorizada',
          values: [{ timestamp: 1701399600, value: 3 }]
        }
      ]
    },
    {
      id: 8,
      name: 'New Devices test',
      slug: 'new-devices-test',
      type: 'count',
      metrics: [
        {
          name: 'test Setorizada',
          values: [{ timestamp: 1701399600, value: 3 }]
        }
      ]
    }
  ]
}
export const metricMockGroup2: IMetric = {
  id: 1,
  name: 'Monitoramento',
  indicators: [
    {
      id: 2,
      name: 'Devices Disconnected by Company',
      slug: 'devices-disconnected-by-company',
      type: 'series',
      metrics: [
        {
          name: '0119 - DOCLABS',
          values: [
            { timestamp: 1698807600, value: 17 },
            { timestamp: 1701399600, value: 9 }
          ]
        },
        {
          name: '0131 - SHOPPING PP',
          values: [
            { timestamp: 1698807600, value: 2 },
            { timestamp: 1701399600, value: 2 }
          ]
        }
      ]
    },
    {
      id: 4,
      name: 'Disconnections by Operator',
      slug: 'disconnections-by-operator',
      type: 'series',
      metrics: [
        {
          name: 'SEM-NOME',
          values: [{ timestamp: 1701399600, value: 9 }]
        },
        {
          name: 'Claro',
          values: [{ timestamp: 1701399600, value: 4 }]
        }
      ]
    },
    {
      id: 11,
      name: 'Disconnections by Distributor',
      slug: 'disconnections-by-distributor',
      type: 'series',
      metrics: [
        {
          name: 'AES TIETE ENERGIA',
          values: [
            { timestamp: 1698807600, value: 4 },
            { timestamp: 1701399600, value: null }
          ]
        }
      ]
    }
  ]
}
export const metricMockGroup3: IMetric = {
  id: 3,
  name: 'Casos x Resoluções',
  indicators: [
    {
      id: 1,
      name: 'Disconnections by Period',
      slug: 'disconnections-by-period',
      type: 'count',
      metrics: [
        {
          name: 'Desconectados',
          values: [{ timestamp: 1701399600, value: 126 }]
        }
      ]
    },
    {
      id: 3,
      name: 'Reconnections By Period',
      slug: 'reconnections-by-period',
      type: 'series',
      metrics: [
        {
          name: 'Reconectados',
          values: [{ timestamp: 1701399600, value: 122 }]
        }
      ]
    }
  ]
}
export const metricMockGroup4: IMetric = {
  id: 4,
  name: 'Ordem de Serviço',
  indicators: [
    {
      id: 18,
      name: 'Service Order Opened Monthly',
      slug: 'service-order-opened-monthly',
      type: 'series',
      metrics: [
        {
          name: 'Aberta no mês',
          values: [{ timestamp: 1677639600, value: 0 }]
        },
        {
          name: 'Em aberto começo do mês',
          values: [{ timestamp: 1677639600, value: 69 }]
        },
        {
          name: 'Em aberto final do mês',
          values: [{ timestamp: 1677639600, value: 65 }]
        },
        {
          name: 'Executada no mês',
          values: [{ timestamp: 1677639600, value: 5 }]
        }
      ]
    }
  ]
}
export const metricMockGroup5: IMetric = {
  id: 5,
  name: 'Ordem de Serviço - Por semana',
  indicators: [
    {
      id: 19,
      name: 'Service Order frontier by week',
      slug: 'service-order-frontier-by-week',
      type: 'series',
      metrics: [
        {
          name: '19/03/2023',
          values: [{ timestamp: 1677639600, value: 1 }]
        },
        {
          name: '26/03/2023',
          values: [{ timestamp: 1677639600, value: 2 }]
        }
      ]
    }
  ]
}
