import IProperty from '@/@core/domain/Property'

export const PropertyMock1: IProperty = {
  id: 44,
  name: 'Tempest',
  displayName: 'Tempest',
  description: 'Tempest description',
  type: 'string',
  storeable: 1,
  processable: 1,
  subTypes: [
    {
      id: 1,
      name: 'Energia',
      type: {
        id: 1,
        name: 'energia'
      }
    }
  ]
}

export const PropertyMock2: IProperty = {
  id: 35,
  name: 'Telecom',
  displayName: 'Tempest',
  description: 'Telecom description',
  type: 'string',
  storeable: 1,
  processable: 1,
  subTypes: [
    {
      id: 1,
      name: 'Energia',
      type: {
        id: 1,
        name: 'energia'
      }
    }
  ]
}
