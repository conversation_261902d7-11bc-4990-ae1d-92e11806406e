import { Response } from '@/@core/infra/api/IntegrationsApiV3/IntegrationsApiV3.types'
import IIntegration from '@/@core/domain/Integration'

/** Response */
export const integrationResponseMock1: Response = {
  id: 2,
  name: 'Gestal'
}
export const integrationResponseMock2: Response = {
  id: 1,
  name: 'SCG<PERSON>'
}

/** Response parsed domain  */
export const integrationMock1: IIntegration = {
  id: 2,
  name: 'Gestal'
}
export const integrationMock2: IIntegration = {
  id: 1,
  name: 'SCG<PERSON>'
}
