import { IMenu } from '@/@core/domain/Menu'

export const menu1: IMenu = {
  moduleId: 2,
  moduleName: 'Dashboard',
  moduleSlug: 'dashboard',
  subModules: [
    {
      id: 32,
      name: 'Dashboard Equipments',
      slug: 'dashboard-equipments'
    }
  ]
}
export const menu2: IMenu = {
  moduleId: 1,
  moduleName: 'ACL',
  moduleSlug: 'acl',
  subModules: [
    {
      id: 3,
      name: 'Agentes CCEE',
      slug: 'agents-ccee'
    },
    {
      id: 5,
      name: 'Balanço Energético',
      slug: 'energetic-statement'
    }
  ]
}
export const menu3: IMenu = {
  moduleId: 8,
  moduleName: 'Rateios',
  moduleSlug: 'apportionments',
  subModules: []
}
export const menu4: IMenu = {
  moduleId: 10,
  moduleName: 'Operação',
  moduleSlug: 'operation',
  subModules: [
    {
      id: 13,
      name: 'Alarme',
      slug: 'alarms'
    },
    {
      id: 14,
      name: 'Dispositivos alarmados',
      slug: 'monitor-alarms'
    },
    {
      id: 12,
      name: 'Mapa',
      slug: 'map'
    },
    {
      id: 11,
      name: 'Monitoramento',
      slug: 'monitor'
    },
    {
      id: 23,
      name: 'Propried<PERSON>',
      slug: 'properties'
    }
  ]
}
export const menu5 = {
  moduleId: 9,
  moduleName: 'Relatórios',
  moduleSlug: 'reports',
  subModules: []
}
export const menuFake: IMenu = {
  moduleId: 3,
  moduleName: 'Cadastro teste',
  moduleSlug: 'slug-test',
  subModules: []
}
