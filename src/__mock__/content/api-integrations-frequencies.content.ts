import { Response } from '@/@core/infra/api/IntegrationsFrequenciesApiV3/IntegrationsFrequenciesApiV3.types'
import IIntegrationFrequency from '@/@core/domain/IntegrationFrequency'

/** Response */
export const integrationFrequencyResponseMock1: Response = {
  id: 2,
  name: 'Gestal'
}
export const integrationFrequencyResponseMock2: Response = {
  id: 1,
  name: 'SCG<PERSON>'
}

/** Response parsed domain  */
export const integrationFrequencyMock1: IIntegrationFrequency = {
  id: 2,
  name: 'Gestal'
}
export const integrationFrequencyMock2: IIntegrationFrequency = {
  id: 1,
  name: '<PERSON><PERSON><PERSON>'
}
