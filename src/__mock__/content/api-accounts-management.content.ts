import IAccountsManagement from '@/@core/domain/AccountsManagement'
import { IAccountsManagementResponse } from '@/@core/infra/api/AccountsManagementApiV3/AccountsManagementApiV3.types'

/** Response */
export const accountsManagementResponseMock1: IAccountsManagementResponse = {
  account_id: 44,
  account_name: 'conta tempest'
}
export const accountsManagementResponseMock2: IAccountsManagementResponse = {
  account_id: 35,
  account_name: 'conta telecom'
}

/** Response parsed domain  */
export const accountsManagementMock1: IAccountsManagement = {
  account: {
    id: 44,
    name: 'conta tempest'
  },
  accountId: 44,
  accountName: 'conta tempest'
}
export const accountsManagementMock2: IAccountsManagement = {
  account: {
    id: 35,
    name: 'conta telecom'
  },
  accountId: 35,
  accountName: 'conta telecom'
}
