/** Response */
export const installationDocumentsResponseMock1 = {
  id: 1,
  company: {
    id: 1,
    name: 'company 1'
  },
  companyId: 1,
  companyName: 'company 1',
  equipment: {
    id: 1,
    name: 'equipment 1',
    device: null
  },
  equipmentId: 1,
  equipmentName: 'equipment 1',
  luc: 'luc',
  noc: 'noc',
  installer: 'instalador',
  date: '11/11/2024',
  deviceCode: '12345',
  status: 'pending',
  documentUrl: 'documentUrl',
  protocol_settings: 'protocol_settings',
  address_settings: 'address_settings',
  baudRate_settings: 'baudRate_settings',
  parity_settings: 'parity_settings',
  currentRelation_settings: 'currentRelation_settings',
  potentialRelation_settings: 'potentialRelation_settings',
  meterConstant_settings: 'meterConstant_settings',
  powerSystem_settings: 'powerSystem_settings',
  deviceModel_settings: 'deviceModel_settings'
}
export const installationDocumentsResponseMock2 = {
  id: 2,
  company_name: 'company_name 2',
  company_id: 2,
  equipment_id: 2,
  equipment_name: 'equipment_name 2',
  luc: 'luc 2',
  noc: 'noc 2',
  installer: 'instalador 2',
  date: '02/02/2024',
  device_code: '2222',
  status: 'pending',
  document_url: 'document_url',
  config: [{ key: '2', value: '2' }]
}
