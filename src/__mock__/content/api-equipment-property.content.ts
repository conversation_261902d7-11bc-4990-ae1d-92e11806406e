import { IEquipmentPropertyResponse } from '@/@core/infra/api/EquipmentPropertiesApiV4/EquipmentPropertiesApiV4.types'
import { IEquipmentProperty } from '@/@core/domain/EquipmentProperty'

/** Response */
export const equipmentPropertyResponseMock1: IEquipmentPropertyResponse = {
  id: 6,
  equipment: { id: 11, name: 'Equipamento Zordon' },
  property: { id: 44, name: 'Proriedade' },
  offset: 22,
  conversion_factor: 33
}
export const equipmentPropertyResponseMock2: IEquipmentPropertyResponse = {
  id: 2,
  equipment: { id: 12, name: 'Equipamento Zordon' },
  property: { id: 41, name: 'Proriedade' },
  offset: 31,
  conversion_factor: 51
}
/** Response parsed domain  */
export const equipmentPropertyMock1: IEquipmentProperty = {
  id: 6,
  equipment: { id: 11, name: 'Equipamento Zordon' },
  equipmentId: 11,
  property: { id: 44, name: '<PERSON>ried<PERSON>' },
  propertyId: 44,
  offset: 22,
  conversionFactor: 33
}
export const equipmentPropertyMock2: IEquipmentProperty = {
  id: 2,
  equipment: { id: 12, name: 'Equipamento Zordon' },
  equipmentId: 12,
  property: { id: 41, name: 'Proriedade' },
  propertyId: 41,
  offset: 31,
  conversionFactor: 51
}
