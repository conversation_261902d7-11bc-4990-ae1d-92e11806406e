import { ICostCentersAdditionalConsumptions } from '@/@core/domain/CostCentersAdditionalConsumptions'
import { CostCentersAdditionalConsumptionsResponse } from '@/@core/infra/api/CostCentersAdditionalConsumptionsApiV3/CostCentersAdditionalConsumptionsApiV3.types'

export const costCentersAdditionalConsumptionsResponseMock1: CostCentersAdditionalConsumptionsResponse =
  {
    id: 1,
    cost_center_id: 101,
    period: '2025-01',
    value: 5000.75
  }

export const costCentersAdditionalConsumptionsResponseMock2: CostCentersAdditionalConsumptionsResponse =
  {
    id: 2,
    cost_center_id: 102,
    period: '2025-02',
    value: 8500.0
  }

export const costCentersAdditionalConsumptionsMock1: ICostCentersAdditionalConsumptions =
  {
    id: 1,
    costCenterId: 101,
    period: '2025-01',
    value: 5000.75
  }
export const costCentersAdditionalConsumptionsMock2: ICostCentersAdditionalConsumptions =
  {
    id: 2,
    costCenterId: 102,
    period: '2025-02',
    value: 8500.0
  }
