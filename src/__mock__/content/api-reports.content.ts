import { IReport, IReportsList } from '@/@core/domain/Report'
import {
  IReportsListResponse,
  IReportsResponse
} from '@/@core/infra/api/ReportsApiV3/ReportsApiV3.types'

export const reportsListResponseMock1: IReportsListResponse = {
  id: 24,
  entity_id: 3,
  entity_type: 'Log de acessos',
  name: 'TESTE 66',
  updated_at: '27/05/2025 12:39',
  entity: {
    id: 3,
    name: 'Conta',
    type: 'account'
  }
}
export const reportsListResponseMock2: IReportsListResponse = {
  id: 23,
  entity_id: 3,
  entity_type: 'Log de acessos',
  name: 'TESTE 23',
  updated_at: '27/05/2025 11:27',
  entity: {
    id: 3,
    name: 'Conta',
    type: 'account'
  }
}
export const reportsListMock1: IReportsList = {
  id: 24,
  entityId: 3,
  entityName: 'Conta',
  entityType: 'Log de acessos',
  name: 'TESTE 66',
  updatedAt: '27/05/2025 12:39',
  entity: {
    id: 3,
    name: 'Conta',
    type: 'account'
  }
}
export const reportsListMock2: IReportsList = {
  id: 23,
  entityId: 3,
  entityName: 'Conta',
  entityType: 'Log de acessos',
  name: 'TESTE 23',
  updatedAt: '27/05/2025 11:27',
  entity: {
    id: 3,
    name: 'Conta',
    type: 'account'
  }
}

export const reportsResponseMock1: IReportsResponse = {
  id: 24,
  name: 'TESTE 66',
  updated_at: '27/05/2025 12:39',
  filter: {
    type_data: {
      access_log: {
        value: false,
        fields: {}
      },
      consumption: {
        value: true,
        fields: { consumption: ['consumo_ativo', 'consumo_reativo'] }
      }
    },
    entity_fields: {
      entity: 'account'
    },
    period_fields: {
      syntax_date: 'last_week'
    },
    aggregate_fields: {},
    entity_data_fields: {
      entity_data: [
        {
          id: 9,
          nome: 'Desenvolvimento'
        }
      ]
    },
    entity_type_fields: {
      account_type: 'access_log'
    }
  }
}
export const reportsResponseMock2: IReportsResponse = {
  id: 23,
  name: 'TESTE 23',
  updated_at: '27/05/2025 11:27',
  filter: {
    type_data: {
      access_log: {
        value: false,
        fields: {}
      },
      consumption: {
        value: true,
        fields: {}
      }
    },
    entity_fields: {
      entity: 'account'
    },
    period_fields: {
      syntax_date: 'last_month'
    },
    aggregate_fields: {},
    entity_data_fields: {
      entity_data: [
        {
          id: 9,
          nome: 'Desenvolvimento'
        }
      ]
    },
    entity_type_fields: {
      account_type: 'access_log'
    }
  }
}
export const reportsMock1: IReport = {
  id: 24,
  name: 'TESTE 66',
  updatedAt: '27/05/2025 12:39',
  filter: {
    type_data: {
      access_log: {
        value: false,
        fields: {}
      },
      consumption: {
        value: true,
        fields: { consumption: ['consumo_ativo', 'consumo_reativo'] }
      }
    },
    entity_fields: {
      entity: 'account'
    },
    entity_data_fields: {
      entity_data: [
        {
          id: 9,
          name: 'Desenvolvimento'
        }
      ]
    },
    period_fields: {
      syntax_date: 'last_week'
    },
    aggregate_fields: {},
    entity_type_fields: {
      account_type: 'access_log'
    }
  }
}
export const reportsMock2: IReport = {
  id: 23,
  name: 'TESTE 23',
  updatedAt: '27/05/2025 11:27',
  filter: {
    type_data: {
      access_log: {
        value: false,
        fields: {}
      },
      consumption: {
        value: true,
        fields: {}
      }
    },
    entity_fields: {
      entity: 'account'
    },
    entity_data_fields: {
      entity_data: [
        {
          id: 9,
          name: 'Desenvolvimento'
        }
      ]
    },
    entity_type_fields: {
      account_type: 'access_log'
    },
    period_fields: {
      syntax_date: 'last_month'
    },
    aggregate_fields: {}
  }
}
