import { IMenuItem } from '@/types/system/layout'

export const menuItem1: IMenuItem = {
  active: false,
  id: 1,
  name: 'AC<PERSON>',
  order: 2,
  slug: '/acl',
  showSubmodule: false,
  subModules: [
    {
      active: false,
      id: 3,
      name: 'Agentes CCEE',
      order: 0,
      slug: '/agents-ccee',
      to: '/agents-ccee'
    },
    {
      active: false,
      id: 5,
      name: 'Balanço Energético',
      order: 0,
      slug: '/energetic-statement',
      to: '/energetic-statement'
    }
  ],
  to: '#'
}
export const menuItem2: IMenuItem = {
  active: false,
  id: 2,
  name: 'Dashboard',
  order: 1,
  slug: '/dashboard',
  showSubmodule: false,
  subModules: [],
  to: '/dashboard'
}
export const menuItem3: IMenuItem = {
  active: false,
  id: 2,
  name: 'Dashboard',
  order: 1,
  slug: '/dashboard',
  showSubmodule: false,
  subModules: [],
  to: '/dashboard'
}
