import { IApportionment } from '@/@core/domain/Apportionment'
import { IApportionmentsResponse } from '@/@core/infra/api/ApportionmentsApiV3/ApportionmentsV3.types'

export const apportionmentResponseMock1: IApportionmentsResponse = {
  id: 120,
  name: 'rateio exemplo',
  description: 'exemplo',
  mixed_consumption: true,
  company: {
    id: 137,
    name: '137 - Comany without account'
  },
  apportionment_type: {
    id: 3,
    name: 'Gás'
  },
  apportionment_tariff_type: {
    id: 3,
    name: 'Customizado'
  },
  apportionment_measure_unit: {
    id: 4,
    name: 'Nm³'
  },
  cost_center_ids: [479, 483, 484, 485, 486]
}
export const apportionmentResponseMock2: IApportionmentsResponse = {
  id: 119,
  name: 'Teste',
  description: '.',
  mixed_consumption: true,
  company: {
    id: 108,
    name: 'WEG CALDEIRARIA GRAVATAI'
  },
  apportionment_type: {
    id: 1,
    name: 'Energia'
  },
  apportionment_tariff_type: {
    id: 1,
    name: 'Cat<PERSON>'
  },
  apportionment_measure_unit: {
    id: 1,
    name: 'MWh'
  },
  cost_center_ids: [445]
}

export const apportionmentMock1: IApportionment = {
  id: 120,
  name: 'rateio exemplo',
  description: 'exemplo',
  mixedConsumption: true,
  company: {
    id: 137,
    name: '137 - Comany without account'
  },
  apportionmentType: {
    id: 3,
    name: 'Gás'
  },
  apportionmentTariffType: {
    id: 3,
    name: 'Customizado'
  },
  apportionmentMeasureUnit: {
    id: 4,
    name: 'Nm³'
  },
  costCenterIds: [479, 483, 484, 485, 486]
}
export const apportionmentMock2: IApportionment = {
  id: 119,
  name: 'Teste',
  description: '.',
  mixedConsumption: true,
  company: {
    id: 108,
    name: 'WEG CALDEIRARIA GRAVATAI'
  },
  apportionmentType: {
    id: 1,
    name: 'Energia'
  },
  apportionmentTariffType: {
    id: 1,
    name: 'Cativo'
  },
  apportionmentMeasureUnit: {
    id: 1,
    name: 'MWh'
  },
  costCenterIds: [445]
}
