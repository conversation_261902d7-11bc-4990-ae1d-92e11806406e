import { Response } from '@/@core/infra/api/EquipmentsTelemetryApiV4/EquipmentTelemetryApiV4.types'
import { IEquipmentTelemetry } from '@/@core/domain/EquipmentTelemetry'

/** Response */
export const equipmentTelemetryResponseMock1: Response = {
  id: 563,
  st_auto_forecast: false,
  st_auto_scde: false,
  st_modulation: false,
  st_monitoring: false,
  st_virtual: false,
  equipment: { id: 13748, name: '13748 - Equipment' },
  equipment_id: 13748
}
export const equipmentTelemetryResponseMock2: Response = {
  id: 564,
  st_auto_forecast: false,
  st_auto_scde: false,
  st_modulation: false,
  st_monitoring: false,
  st_virtual: false,
  equipment: { id: 13749, name: '13749 - Equipment' },
  equipment_id: 13749
}
/** Response parsed domain  */
export const equipmentTelemetryMock1: IEquipmentTelemetry = {
  id: 563,
  stAutoForecast: false,
  stAutoScde: false,
  stModulation: false,
  stMonitoring: false,
  stVirtual: false,
  equipment: { id: 13748, name: '13748 - Equipment' },
  equipmentId: 13748
}
export const equipmentTelemetryMock2: IEquipmentTelemetry = {
  id: 564,
  stAutoForecast: false,
  stAutoScde: false,
  stModulation: false,
  stMonitoring: false,
  stVirtual: false,
  equipment: { id: 13749, name: '13749 - Equipment' },
  equipmentId: 13749
}
