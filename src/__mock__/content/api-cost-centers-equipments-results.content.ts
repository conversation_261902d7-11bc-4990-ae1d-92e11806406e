import { ICostCenterEquipmentResultResponse } from '@/@core/infra/api/CostCentersEquipmentsResultsApiV3/CostCentersEquipmentsResultsApiV3.types'
import { ICostCenterEquipmentResult } from '@/@core/domain/CostCenterEquipmentResult'

/** Response */
export const costCenterEquipmentResultResponseMock1: ICostCenterEquipmentResultResponse =
  {
    cost_center_id: 414,
    tariff_value: 300.86,
    date: '2025-02-01',
    total_value: 111272445.21,
    total_value_previous: 2276394.68,
    percentage_diff: 4788.1,
    equipment: {
      id: 277,
      name: '0277- 13547 - hourly'
    }
  }
export const costCenterEquipmentResultResponseMock2: ICostCenterEquipmentResultResponse =
  {
    cost_center_id: 411,
    tariff_value: 300.86,
    date: '2025-02-01',
    total_value: 301292.63,
    total_value_previous: 334480.46,
    percentage_diff: -9.92,
    equipment: {
      id: 512,
      name: '0512 - Equipment - Medição Belo Horizonte'
    }
  }
/** Response parsed domain  */
export const costCenterEquipmentResultMock1: ICostCenterEquipmentResult = {
  costCenterId: 414,
  date: '2025-02-01',
  equipment: { id: 277, name: '0277- 13547 - hourly' },
  percentageDiff: 4788.1,
  tariffValue: 111272445.21,
  totalValue: 111272445.21,
  totalValuePrevious: 2276394.68
}
export const costCenterEquipmentResultMock2: ICostCenterEquipmentResult = {
  costCenterId: 411,
  date: '2025-02-01',
  equipment: { id: 512, name: '0512 - Equipment - Medição Belo Horizonte' },
  percentageDiff: -9.92,
  tariffValue: 301292.63,
  totalValue: 301292.63,
  totalValuePrevious: 334480.46
}
