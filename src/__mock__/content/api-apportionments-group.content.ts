import { IApportionmentsGroup } from '@/@core/domain/ApportionmentsGroups'
import { ResponseApportionmentsGroup } from '@/@core/infra/api/ApportionmentsGroupsApiV3/ApportionmentsGroupsApiV3.types'

/** Response */
export const apportionmentsGroupsResponseMock1: ResponseApportionmentsGroup = {
  id: 1,
  apportionment_id: 101,
  name: 'Grupo A',
  number_equipments: 2,
  equipments: [
    {
      id: 1,
      apportionment_group_id: 1,
      equipment_id: 1001
    },
    {
      id: 2,
      apportionment_group_id: 1,
      equipment_id: 1002
    }
  ]
}

export const apportionmentsGroupsResponseMock2: ResponseApportionmentsGroup = {
  id: 2,
  apportionment_id: 102,
  name: 'Grupo B',
  number_equipments: 3,
  equipments: [
    {
      id: 3,
      apportionment_group_id: 2,
      equipment_id: 2001
    },
    {
      id: 4,
      apportionment_group_id: 2,
      equipment_id: 2002
    },
    {
      id: 5,
      apportionment_group_id: 2,
      equipment_id: 2003
    }
  ]
}

/** Response parsed domain  */
export const apportionmentsGroupMock1: IApportionmentsGroup = {
  id: 1,
  apportionmentId: 101,
  name: 'Grupo A',
  numberEquipments: 2,
  equipments: [
    {
      id: 1,
      apportionmentGroupId: 1,
      equipmentId: 1001
    },
    {
      id: 2,
      apportionmentGroupId: 1,
      equipmentId: 1002
    }
  ]
}

export const apportionmentsGroupMock2: IApportionmentsGroup = {
  id: 2,
  apportionmentId: 102,
  name: 'Grupo B',
  numberEquipments: 3,
  equipments: [
    {
      id: 3,
      apportionmentGroupId: 2,
      equipmentId: 2001
    },
    {
      id: 4,
      apportionmentGroupId: 2,
      equipmentId: 2002
    },
    {
      id: 5,
      apportionmentGroupId: 2,
      equipmentId: 2003
    }
  ]
}
