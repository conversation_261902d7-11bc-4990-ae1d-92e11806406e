import { Response } from '@/@core/infra/api/DeviceConstantsApiV4/DeviceConstantsApiV4.types'
import IDeviceConstant from '@/@core/domain/DeviceConstant'

/** Response */
export const deviceConstantResponseMock1: Response = {
  id: 329,
  device_id: 497,
  potential_relation: 1,
  current_relation: 10,
  meter_constant: 2,
  loss_factor: null,
  initial_date: '2023-12-14 10:42:05'
}
export const deviceConstantResponseMock2: Response = {
  id: 330,
  device_id: 497,
  potential_relation: 1,
  current_relation: 10,
  meter_constant: 2,
  loss_factor: 3,
  initial_date: '2023-12-14 10:45:05'
}
/** Response parsed domain  */
export const deviceConstantMock1: IDeviceConstant = {
  id: 329,
  deviceId: 497,
  potentialRelation: 1,
  currentRelation: 10,
  meterConstant: 2,
  lossFactor: null,
  initialDate: '2023-12-14 10:42:05'
}
export const deviceConstantMock2: IDeviceConstant = {
  id: 330,
  deviceId: 497,
  potentialRelation: 1,
  currentRelation: 10,
  meterConstant: 2,
  lossFactor: 3,
  initialDate: '2023-12-14 10:45:05'
}
