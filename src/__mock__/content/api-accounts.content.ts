import IAccount from '@/@core/domain/Account'
import { IAccountsResponse } from '@/@core/infra/api/AccountsApiV3/AccountsApiV3.types'

/** Response */
export const accountResponseMock1: IAccountsResponse = {
  id: 44,
  name: 'Tempest',
  status: {
    id: 1,
    name: 'Ativa'
  },
  modules: [
    { id: 1, name: 'Dashboard' },
    { id: 2, name: 'ACL' },
    { id: 3, name: '<PERSON><PERSON> CCEE' }
  ],
  management: 1
}
export const accountResponseMock2: IAccountsResponse = {
  id: 35,
  name: 'Telecom',
  status: {
    id: 1,
    name: 'Ativa'
  },
  modules: [],
  management: 0
}

/** Response parsed domain  */
export const accountMock1: IAccount = {
  id: 44,
  name: 'Tempest',
  statusId: 1,
  statusName: 'Ativa',
  modules: [
    { id: 1, name: 'Dashboard' },
    { id: 2, name: '<PERSON><PERSON>' },
    { id: 3, name: '<PERSON><PERSON> CCEE' }
  ],
  management: 1
}

export const accountMock2: IAccount = {
  id: 35,
  name: 'Telecom',
  statusId: 1,
  statusName: 'Ativa',
  modules: [],
  management: 0
}
