import IAlarm from '@/@core/domain/Alarm'
import { AlarmFormData } from '@/@core/presentation/views/alarms.id/TabData/Tab.types'

export const alarmMock1: IAlarm = {
  id: 56,
  name: '<PERSON>or potência horário',
  description: '',
  timeConfirmation: 0,
  initialHour: '00:00',
  finalHour: '23:59',
  daysWeek: [0, 1, 2, 3, 4, 5, 6],
  daysRetention: 1,
  status: true,
  account: { id: 9, name: 'Desenvolvimento' },
  accountId: 9,
  category: { id: 3, name: 'Usu<PERSON><PERSON>' },
  categoryId: 3,
  readonly: false
}
export const alarmMock2: IAlarm = {
  id: 49,
  name: '<PERSON><PERSON> hor<PERSON>',
  description: '',
  timeConfirmation: 0,
  initialHour: '00:00',
  finalHour: '23:59',
  daysWeek: [0, 1, 2, 3, 4, 5, 6],
  daysRetention: 2,
  status: true,
  account: null,
  accountId: null,
  category: { id: 2, name: '<PERSON><PERSON>' },
  categoryId: 2,
  readonly: false
}

export const AlarmFormDataMock: AlarmFormData = {
  id: 56,
  name: '<PERSON>or potência horário',
  description: '',
  status: true,
  account: [{ id: 9, name: 'Desenvolvimento' }],
  categoryId: '3'
}
