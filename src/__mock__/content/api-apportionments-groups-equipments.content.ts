import { IApportionmentGroupEquipmentResponse } from '@/@core/infra/api/ApportionmentsGroupsEquipmentsApiV3/ApportionmentsGroupsEquipmentsApiV3.types'
import { IApportionmentGroupEquipment } from '@/@core/domain/ApportionmentGroupEquipment'

/** Response */
export const apportionmentGroupEquipmentResponseMock1: IApportionmentGroupEquipmentResponse =
  {
    id: 120,
    apportionment_group_id: 10,
    equipment: { id: 13350, nome: '<PERSON><PERSON> e Souza' },
    equipment_id: 13350
  }

export const apportionmentGroupEquipmentResponseMock2: IApportionmentGroupEquipmentResponse =
  {
    id: 121,
    apportionment_group_id: 11,
    equipment: { id: 13351, nome: '<PERSON><PERSON> e Souza' },
    equipment_id: 13351
  }

/** Response parsed domain  */
export const apportionmentGroupEquipmentMock1: IApportionmentGroupEquipment = {
  id: 120,
  apportionmentGroupId: 10,
  equipment: { id: 13350, name: '<PERSON><PERSON> e <PERSON>uza' },
  equipmentId: 13350
}

export const apportionmentGroupEquipmentMock2: IApportionmentGroupEquipment = {
  id: 120,
  apportionmentGroupId: 11,
  equipment: { id: 13351, name: 'Marés e Souza' },
  equipmentId: 13351
}
