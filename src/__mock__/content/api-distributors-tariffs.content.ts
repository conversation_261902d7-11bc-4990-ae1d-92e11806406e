import { Response } from '@/@core/infra/api/DistributorsTariffsApiV4/DistributorsTariffsApiV4.types'
import { IDistributorTariffs } from '@/@core/domain/DistributorTariffs'

/** Response */
export const distributortariffResponseMock1: Response = {
  id: 209,
  type: 'not_applicable',
  vigency_start: '2022-06-22',
  vigency_end: '2024-06-18',
  tusd_mwh_captive_value: 0,
  tusd_kw_value: 0,
  tusd_mwh_value: 240.65,
  tusd_kw_captive_value: 0,
  te_mwh_value: 143.89,
  distributor: {
    id: 1,
    nome: 'Enel - São Paulo.'
  },
  distributor_id: 1,
  tariff_modality: {
    id: 3,
    name: 'Convencional'
  },
  tariff_modality_id: 3,
  voltage_class: {
    id: 10,
    name: 'B4'
  },
  voltage_class_id: 10
}

/** Response parsed domain  */
export const distributortariffMock1: IDistributorTariffs = {
  id: 209,
  type: 'not_applicable',
  vigencyStart: '2022-06-22',
  vigencyEnd: '2024-06-18',
  tusdMwhCaptiveValue: 0,
  tusdKwValue: 0,
  tusdMwhValue: 240.65,
  tusdKwCaptiveValue: 0,
  teMwhValue: 143.89,
  distributor: {
    id: 1,
    nome: 'Enel - São Paulo.'
  },
  distributorId: 1,
  tariffModalityId: 3,
  tariffModality: {
    id: 3,
    name: 'Convencional'
  },
  voltageClass: {
    id: 10,
    name: 'B4'
  },
  voltageClassId: 10
}
