import IDeviceInstallation from '@/@core/domain/DeviceInstallation'
import { Response } from '@/@core/infra/api/DeviceInstallationsApiV4/DeviceInstallationsApiV4.types'

/** Response */
export const deviceInstallationResponseMock1: Response = {
  id: 1,
  device_id: 1,
  key: 'key 1',
  value: 'value 1',
  key_name: 'key name 1'
}
export const deviceInstallationResponseMock2: Response = {
  id: 2,
  device_id: 2,
  key: 'key 2',
  value: 'value 2',
  key_name: 'key name 2'
}
/** Response parsed domain  */
export const deviceInstallationMock1: IDeviceInstallation = {
  id: 1,
  deviceId: 1,
  key: 'key 1',
  value: 'value 1',
  keyName: 'key name 1'
}
export const deviceInstallationMock2: IDeviceInstallation = {
  id: 2,
  deviceId: 2,
  key: 'key 2',
  value: 'value 2',
  keyName: 'key name 2'
}
