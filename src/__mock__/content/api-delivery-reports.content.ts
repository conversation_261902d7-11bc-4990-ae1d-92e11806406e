import IDeliveryReports from '@/@core/domain/DeliveryReports'
import { Response } from '@/@core/infra/api/DeliveryReportsApiV4/DeliveryReportsApiV4.types'

/** Response */
export const deliveryReportResponseMock1: Response = {
  id: 1,
  company_id: 3,
  company_name: 'Company 3',
  equipment_id: 1,
  equipment_name: 'Equipment 3',
  luc: 'txt',
  noc: 'txt',
  installer: 'txt',
  date: '2024-03-01',
  device_code: '12345',
  device_id: 1,
  status: 'pending',
  document_url: null,
  config: [
    {
      key: 'modelo_do_medidor',
      value: 'valor'
    },
    {
      key: 'protocolo',
      value: '1234'
    },
    {
      key: 'baud_rate',
      value: 'alknsdlknas'
    }
  ]
}

/** Response parsed domain  */
export const deliveryReportMock1: IDeliveryReports = {
  id: 1,
  company: { id: 3, name: 'Company 3' },
  companyId: 3,
  companyName: 'Company 3',
  equipment: { id: 1, name: 'Equipment 3', device: null },
  equipmentId: 1,
  equipmentName: 'Equipment 3',
  luc: 'txt',
  noc: 'txt',
  installer: 'txt',
  date: '2024-03-01',
  deviceCode: '12345',
  deviceId: 1,
  status: 'pending',
  documentUrl: '',
  protocol_settings: '',
  address_settings: '',
  baudRate_settings: '',
  parity_settings: '',
  currentRelation_settings: '',
  potentialRelation_settings: '',
  meterConstant_settings: '',
  powerSystem_settings: '',
  deviceModel_settings: ''
}

export const deliveryReportMock2 = {
  company: { id: 3, name: 'Company 3' },
  companyId: 3,
  companyName: 'Company 3',
  equipment: { id: 1, name: 'Equipment 3', device: null },
  equipmentId: 1,
  equipmentName: 'Equipment 3',
  luc: 'txt',
  noc: 'txt',
  installer: 'txt',
  date: '2024-03-01',
  deviceCode: '12345',
  documentUrl: '',
  protocol_settings: '',
  address_settings: '',
  baudRate_settings: '',
  parity_settings: '',
  currentRelation_settings: '',
  potentialRelation_settings: '',
  meterConstant_settings: '',
  powerSystem_settings: '',
  deviceModel_settings: ''
}
