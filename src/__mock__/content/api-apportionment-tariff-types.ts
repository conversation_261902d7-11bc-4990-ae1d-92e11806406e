import { IApportionmentTariffTypes } from '@/@core/domain/ApportionmentTariffTypes'
import { IApportionmentTariffTypesResponse } from '@/@core/infra/api/ApportionmentTariffTypesV3/ApportionmentTariffTypesV3.types'

/** Response */
export const apportionmentTariffTypesResponseMock1: IApportionmentTariffTypesResponse =
  {
    id: 1,
    name: 'Cativo'
  }
export const apportionmentTariffTypesResponseMock2: IApportionmentTariffTypesResponse =
  {
    id: 2,
    name: '<PERSON>re'
  }

/** Response parsed domain  */
export const apportionmentTariffTypesMock1: IApportionmentTariffTypes = {
  id: 1,
  name: 'Cativo'
}
export const apportionmentTariffTypesMock2: IApportionmentTariffTypes = {
  id: 2,
  name: '<PERSON>re'
}
