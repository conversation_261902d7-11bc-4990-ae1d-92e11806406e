import { IMenuItem } from '@/types/system/layout'

export const module1: IMenuItem = {
  active: false,
  id: 2,
  name: 'Dashboard',
  order: 1,
  slug: '/dashboard',
  showSubmodule: false,
  subModules: [],
  to: '/dashboard'
}
export const module2: IMenuItem = {
  active: false,
  id: 1,
  name: 'ACL',
  order: 2,
  slug: '/acl',
  showSubmodule: false,
  subModules: [
    {
      active: false,
      id: 3,
      name: 'Agent<PERSON> CCEE',
      order: 0,
      slug: '/agents-ccee',
      to: '/agents-ccee'
    },
    {
      active: false,
      id: 5,
      name: 'Balanço Energético',
      order: 0,
      slug: '/energetic-statement',
      to: '/energetic-statement'
    }
  ],
  to: ''
}
export const module3: IMenuItem = {
  active: false,
  id: 8,
  name: 'Rate<PERSON>',
  order: 3,
  slug: '/apportionments',
  showSubmodule: false,
  subModules: [],
  to: '/apportionments'
}
export const module4: IMenuItem = {
  active: false,
  id: 10,
  name: 'Opera<PERSON>',
  order: 4,
  slug: '/operation',
  showSubmodule: false,
  subModules: [
    {
      active: false,
      id: 13,
      name: '<PERSON><PERSON><PERSON>',
      order: 9,
      slug: '/alarms',
      to: '/alarms'
    },
    {
      active: false,
      id: 14,
      name: 'Dispositivos alarmados',
      order: 0,
      slug: '/monitor-alarms',
      to: '/monitor-alarms'
    },
    {
      active: false,
      id: 12,
      name: 'Mapa',
      order: 0,
      slug: '/map',
      to: '/probes-map'
    },
    {
      active: false,
      id: 11,
      name: 'Monitoramento',
      order: 0,
      slug: '/monitor',
      to: '/monitor'
    },
    {
      active: false,
      id: 23,
      name: 'Propriedades',
      order: 0,
      slug: '/properties',
      to: '/properties'
    }
  ],
  to: ''
}
export const module5: IMenuItem = {
  active: false,
  id: 9,
  name: 'Relatórios',
  order: 5,
  slug: '/reports',
  showSubmodule: false,
  subModules: [],
  to: '/reports'
}
