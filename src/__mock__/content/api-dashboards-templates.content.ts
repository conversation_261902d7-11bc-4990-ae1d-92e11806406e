import { IDashboardTemplate } from '@/@core/domain/DashboardTemplate'
import { IDashboardTemplatesResponse } from '@/@core/infra/api/DashboardsTemplatesApiV3/DashboardsTemplatesApiV3.types'

/** Response */
export const dashboardsTemplatesResponseMock1: IDashboardTemplatesResponse = {
  id: 1,
  name: 'Energia Fronteira'
}
export const dashboardsTemplatesResponseMock2: IDashboardTemplatesResponse = {
  id: 2,
  name: 'Energia Setorizada'
}
export const dashboardsTemplatesResponseMock3: IDashboardTemplatesResponse = {
  id: 3,
  name: 'Energia Virtual'
}
export const dashboardsTemplatesResponseMock4: IDashboardTemplatesResponse = {
  id: 4,
  name: 'água'
}
export const dashboardsTemplatesResponseMock5: IDashboardTemplatesResponse = {
  id: 5,
  name: 'água com Totalizador'
}

export const dashboardsTemplatesMock1: IDashboardTemplate = {
  id: 1,
  name: 'Energia Fronteira'
}
export const dashboardsTemplatesMock2: IDashboardTemplate = {
  id: 2,
  name: 'Energia Setorizada'
}
export const dashboardsTemplatesMock3: IDashboardTemplate = {
  id: 3,
  name: 'Energia Virtual'
}
export const dashboardsTemplatesMock4: IDashboardTemplate = {
  id: 4,
  name: 'água'
}
export const dashboardsTemplatesMock5: IDashboardTemplate = {
  id: 5,
  name: 'água com Totalizador'
}
