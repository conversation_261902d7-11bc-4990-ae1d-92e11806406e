import IEquipmentBilling from '@/@core/domain/EquipmentBilling'
import { Response } from '@/@core/infra/api/EquipmentBillingApiV4/EquipmentBillingApiV4.types'

export const equipmentBillingResponseMock1: Response = {
  id: 1,
  equipment_id: 1,
  open_date: '2024-04-01',
  close_date: 'faturamento aberto',
  open_comment: '2024-04-15',
  close_comment: 'faturamento fechado'
}

export const equipmentBillingResponseMock2: Response = {
  id: 2,
  equipment_id: 1,
  open_date: '2024-04-16',
  close_date: 'faturamento aberto',
  open_comment: null,
  close_comment: null
}

export const equipmentBillingMock1: IEquipmentBilling = {
  id: 1,
  equipmentId: 1,
  openDate: '2024-04-01',
  closeDate: 'faturamento aberto',
  openComment: '2024-04-15',
  closeComment: 'faturamento fechado'
}

export const equipmentBillingMock2: IEquipmentBilling = {
  id: 2,
  equipmentId: 1,
  openDate: '2024-04-16',
  closeDate: 'faturamento aberto',
  openComment: null,
  closeComment: null
}
