import { IReportGenerator } from '@/@core/domain/ReportGenerator'
import { IReportGeneratorResponse } from '@/@core/infra/api/ReportsGeneratorApiV3/ReportsGeneratorApiV3.types'

export const reportsGeneratorResponseMock1: IReportGeneratorResponse = {
  information: {
    requested_period: {
      initial: '2025-05-01 00:00:00',
      final: '2025-05-31 23:59:59'
    },
    request_date: '2025-05-26 17:52:15'
  },
  table: {
    header: [
      {
        label: 'Column text',
        format: 'text',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column Date',
        format: 'date',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column dateTime',
        format: 'dateTime',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column dateHour',
        format: 'dateHour',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column time',
        format: 'time',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column number',
        format: 'number',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column not exist',
        format: 'number_number' as 'number',
        greatness: null,
        graphic: false
      }
    ],
    body: [
      [
        'Desenvolvimento',
        '2025-05-05',
        '2025-05-05 11:58:00',
        '2025-05-05 11:58:00',
        '2025-05-05',
        11,
        22
      ],
      ['', 1748293595, 1748293595, 1748293595, 1748293595, '11', 22],
      ['', 1748293595, 1748293595, 1748293595, 1748293595, null, 22]
    ]
  }
}
export const reportsGeneratorResponseMock2: IReportGeneratorResponse = {
  information: {
    requested_period: {
      initial: '',
      final: ''
    },
    request_date: ''
  },
  table: {
    header: [
      {
        label: 'Column text',
        format: 'text',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column Date',
        format: 'date',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column dateTime',
        format: 'dateTime',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column dateHour',
        format: 'dateHour',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column time',
        format: 'time',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column number',
        format: 'number',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column not exist',
        format: 'number_number' as 'number',
        greatness: null,
        graphic: false
      }
    ],
    body: [
      [
        'Desenvolvimento',
        '2025-05-05',
        '2025-05-05 11:58:00',
        '2025-05-05 11:58:00',
        '2025-05-05',
        11,
        22
      ],
      ['', 1748293595, 1748293595, 1748293595, 1748293595, '11', 22],
      ['', 1748293595, 1748293595, 1748293595, 1748293595, null, 22]
    ]
  }
}
export const reportsGeneratorMock1: IReportGenerator = {
  information: {
    requestedPeriod: {
      initial: '2025-05-01 00:00:00',
      final: '2025-05-31 23:59:59'
    },
    requestDate: '2025-05-26 17:52:15'
  },
  table: {
    header: [
      {
        label: 'Column text',
        format: 'text',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column Date',
        format: 'date',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column dateTime',
        format: 'dateTime',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column dateHour',
        format: 'dateHour',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column time',
        format: 'time',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column number',
        format: 'number',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column not exist',
        format: 'number_number' as 'number',
        greatness: null,
        graphic: false
      }
    ],
    body: [
      [
        'Desenvolvimento',
        '05/05/2025',
        '05/05/2025 11:58',
        '05/05/2025 11:58',
        '00:00',
        11,
        22
      ],
      [
        '',
        '26/05/2025',
        '26/05/2025 21:06',
        '21/01/1970 05:38',
        '21:06',
        11,
        22
      ],
      [
        '',
        '26/05/2025',
        '26/05/2025 21:06',
        '21/01/1970 05:38',
        '21:06',
        '',
        22
      ]
    ]
  }
}
export const reportsGeneratorMock2: IReportGenerator = {
  information: {
    requestedPeriod: {
      initial: '2025-05-01 00:00:00',
      final: '2025-05-31 23:59:59'
    },
    requestDate: '2025-05-26 17:52:15'
  },
  table: {
    header: [
      {
        label: 'Column text',
        format: 'text',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column Date',
        format: 'date',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column dateTime',
        format: 'dateTime',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column dateHour',
        format: 'dateHour',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column time',
        format: 'time',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column number',
        format: 'number',
        greatness: null,
        graphic: false
      },
      {
        label: 'Column not exist',
        format: 'number_number' as 'number',
        greatness: null,
        graphic: false
      }
    ],
    body: [
      [
        'Desenvolvimento',
        '05/05/2025',
        '05/05/2025 11:58',
        '05/05/2025 11:58',
        '00:00',
        11,
        22
      ],
      [
        '',
        '26/05/2025',
        '26/05/2025 21:06',
        '21/01/1970 05:38',
        '21:06',
        11,
        22
      ],
      [
        '',
        '26/05/2025',
        '26/05/2025 21:06',
        '21/01/1970 05:38',
        '21:06',
        '',
        22
      ]
    ]
  }
}
