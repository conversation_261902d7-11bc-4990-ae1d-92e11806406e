import { ISliceState } from '@/@core/framework/plugins/redux/features/pageAccounts'

export const storeAccountsMock: ISliceState = {
  loading: false,
  formSearch: {
    q: '',
    order: 'desc',
    sort: 'id',
    limit: 15,
    page: 1,
    statusIds: []
  },
  table: {
    items: [
      {
        id: 1,
        name: 'name 1',
        statusId: 1,
        statusName: 'name1',
        modules: []
      },
      {
        id: 2,
        name: 'name 2',
        statusId: 1,
        statusName: 'status name 1',
        modules: []
      },
      {
        id: 3,
        name: 'name 3',
        statusId: 1,
        statusName: 'status name 1',
        modules: []
      },
      {
        id: 4,
        name: 'name 4',
        statusId: 1,
        statusName: 'status name 1',
        modules: []
      }
    ],
    total: 0,
    page: 0,
    limit: 0,
    lastPage: 0
  }
}
