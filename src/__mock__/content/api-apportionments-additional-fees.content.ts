import { IApportionmentAdditionalFeesResponse } from '@/@core/infra/api/ApportionmentsAdditionalFeesApiV3/ApportionmentsAdditionalFeesApiV3.types'
import { IApportionmentAdditionalFees } from '@/@core/domain/ApportionmentAdditionalFees'

/** Response */
export const apportionmentAdditionalFeesResponseMock1: IApportionmentAdditionalFeesResponse =
  {
    id: 126,
    apportionment_id: 118,
    division_type: { id: 1, name: 'Proporcional pelo consumo' },
    name: 'Fatura 08',
    period: '2024-08-01',
    value: 213150.22
  }
export const apportionmentAdditionalFeesResponseMock2: IApportionmentAdditionalFeesResponse =
  {
    id: 127,
    apportionment_id: 118,
    division_type: { id: 2, name: 'Proporcional pelo consumo' },
    name: 'Fatura 09',
    period: '2024-09-01',
    value: 213150.22
  }

/** Response parsed domain  */
export const apportionmentAdditionalFeesMock1: IApportionmentAdditionalFees = {
  id: 126,
  apportionmentId: 118,
  divisionType: { id: 1, name: 'Proporcional pelo consumo' },
  name: 'Fatura 08',
  period: '2024-08-01',
  value: 213150.22
}
export const apportionmentAdditionalFeesMock2: IApportionmentAdditionalFees = {
  id: 127,
  apportionmentId: 118,
  divisionType: { id: 2, name: 'Proporcional pelo consumo' },
  name: 'Fatura 09',
  period: '2024-09-01',
  value: 213150.22
}
