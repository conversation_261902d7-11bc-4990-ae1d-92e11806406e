import { ISliceState } from '@/@core/framework/plugins/redux/features/pageAlarms'

export const storeAlarmsMock: ISliceState = {
  loading: false,
  formSearch: {
    q: '',
    order: 'desc',
    sort: 'id',
    limit: 15,
    page: 1,
    status: 1,
    alarmsCategoryId: 1
  },
  table: {
    items: [
      {
        id: 1,
        name: 'name 1',
        description: 'description',
        timeConfirmation: 1,
        initialHour: '00:00',
        finalHour: '01:00',
        daysWeek: [1, 2],
        daysRetention: 1,
        status: true,
        account: { id: 1, name: 'account' },
        accountId: 1,
        category: { id: 1, name: 'category' },
        categoryId: 1,
        readonly: false
      }
    ],
    total: 0,
    page: 0,
    limit: 0,
    lastPage: 0
  }
}
