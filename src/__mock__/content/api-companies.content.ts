import { ICompany } from '@/@core/domain/Company'
import { Response } from '@/@core/infra/api/CompaniesApiV4/CompaniesApiV4.types'
import { ICompanyPage } from '@/@core/presentation/views/companies.id/TabData/Tab.types'

/** Response */
export const companyResponseMock1: Response = {
  account: { id: 1, name: 'Conta Zordon' },
  city: { id: 4409, name: 'Guabiruba' },
  id: 2,
  name: 'NIPON PLATING - MITSUYASU',
  parent: {
    id: 1,
    name: 'parent'
  },
  state: { id: 24, name: 'Santa Catarina' },
  unit_code: '',
  address: 'address',
  cnae: '1234567',
  cnpj: '*********',
  code_ibge: 1234567,
  complement: 'complement',
  corporate_name: 'corporate_name',
  district: 'district',
  energy_demands_off_peak: [],
  energy_demands_peak: [],
  number: '123456',
  zip_code: '123456',
  timezone: -3,
  type: 'Empresa'
}
export const companyResponseMock2: Response = {
  account: null,
  city: null,
  id: 5,
  name: 'NIPON PLATING - MITSUYASU',
  parent: null,
  state: null,
  unit_code: null,
  address: null,
  cnae: null,
  cnpj: null,
  code_ibge: null,
  complement: null,
  corporate_name: null,
  district: null,
  energy_demands_off_peak: [],
  energy_demands_peak: [],
  number: null,
  zip_code: null,
  timezone: 0,
  type: null
}

/** Response parsed domain  */
export const companyMock1: ICompany = {
  id: 2,
  name: 'NIPON PLATING - MITSUYASU',
  unitCode: '',
  parentId: 1,
  parent: {
    id: 1,
    name: 'parent'
  },
  cityId: 4409,
  city: {
    id: 4409,
    name: 'Guabiruba'
  },
  stateId: 24,
  state: {
    id: 24,
    name: 'Santa Catarina'
  },
  accountId: 1,
  account: {
    id: 1,
    name: 'Conta Zordon'
  },
  address: 'address',
  cnae: '1234567',
  cnpj: '*********',
  complement: 'complement',
  district: 'district',
  number: '123456',
  accountName: 'Conta Zordon',
  cityName: 'Guabiruba',
  codeIbge: 1234567,
  corporateName: 'corporate_name',
  energyDemandsOffPeak: [],
  energyDemandsPeak: [],
  parentName: 'parent',
  stateName: 'Santa Catarina',
  zipCode: '123456',
  timezone: -3,
  type: 'Empresa'
}
export const companyMockPage: ICompanyPage = {
  id: 2,
  name: 'NIPON PLATING - MITSUYASU',
  unitCode: '',
  parentId: 1,
  parent: {
    id: 1,
    name: 'parent'
  },
  cityId: 4409,
  city: {
    id: 4409,
    name: 'Guabiruba'
  },
  stateId: 24,
  state: {
    id: 24,
    name: 'Santa Catarina'
  },
  accountId: 1,
  account: {
    id: 1,
    name: 'Conta Zordon'
  },
  address: 'address',
  cnae: '1234567',
  cnpj: '*********',
  complement: 'complement',
  district: 'district',
  number: '123456',
  codeIbge: 1234567,
  corporateName: 'corporate_name',
  zipCode: '123456',
  timezone: -3,
  type: 'company'
}
export const companyMock2: ICompany = {
  id: 5,
  name: 'NIPON PLATING - MITSUYASU',
  unitCode: null,
  parentId: null,
  parent: null,
  cityId: null,
  city: null,
  stateId: null,
  state: null,
  accountId: null,
  account: null,
  address: null,
  cnae: null,
  cnpj: null,
  complement: null,
  district: null,
  number: null,
  accountName: null,
  cityName: null,
  codeIbge: null,
  corporateName: null,
  energyDemandsOffPeak: [],
  energyDemandsPeak: [],
  parentName: null,
  stateName: null,
  zipCode: null,
  timezone: 0,
  type: null
}
