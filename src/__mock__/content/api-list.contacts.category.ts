import { Response } from '@/@core/infra/api/ListCompaniesContactsCategoriesApiV4/ListCompaniesContactsCategoriesApiV4.types'
import { ICompaniesContactsCategories } from '@/@core/domain/CompaniesContactsCategories'

export const companiesContactsCategoriesResponseMock1: Response = {
  id: 1,
  name: 'name'
}

export const companiesContactsCategoriesResponseMock2: Response = {
  id: 2,
  name: 'name 2'
}

export const companiesContactsCategoriesMock1: ICompaniesContactsCategories = {
  id: 1,
  name: 'name'
}

export const companiesContactsCategoriesMock2: ICompaniesContactsCategories = {
  id: 2,
  name: 'name 2'
}
