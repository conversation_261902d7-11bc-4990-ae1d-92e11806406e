import { IApportionmentTariff } from '@/@core/domain/ApportionmentTariffs'
import { IApportionmentsTariffsResponse } from '@/@core/infra/api/ApportionmentsTariffsApiV3/ApportionmentsTariffsApiV3.types'

/** Response */
export const apportionmentsTariffsResponseMock1: IApportionmentsTariffsResponse =
  {
    id: 1,
    vigency_start: '2025-01-01',
    vigency_end: '2025-12-31',
    value: 10000.0,
    apportionment_id: 101
  }

export const apportionmentsTariffsResponseMock2: IApportionmentsTariffsResponse =
  {
    id: 2,
    vigency_start: '2025-02-01',
    vigency_end: '2025-11-30',
    value: 15000.5,
    apportionment_id: 102
  }

/** Response parsed domain  */
export const apportionmentsTariffsMock1: IApportionmentTariff = {
  id: 1,
  vigencyStart: '2025-01-01',
  vigencyEnd: '2025-12-31',
  value: 10000.0,
  apportionmentId: 101
}

export const apportionmentsTariffsMock2: IApportionmentTariff = {
  id: 2,
  vigencyStart: '2025-02-01',
  vigencyEnd: '2025-11-30',
  value: 15000.5,
  apportionmentId: 102
}
