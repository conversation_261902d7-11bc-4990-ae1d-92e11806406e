import { IIntegrationEquipmentResponse } from '@/@core/infra/api/IntegrationsEquipmentsApiV3/IntegrationsEquipmentsApiV3.types'
import { IIntegrationEquipment } from '@/@core/domain/IntegrationEquipment'

/** Response */
export const integrationEquipmentResponseMock1: IIntegrationEquipmentResponse =
  {
    id: 121,
    active: false,
    external_id: '1',
    integration: { id: 2, name: 'Gestal' },
    integration_account: { id: 23, name: '023 - Gestal Integration - horário' },
    equipment: { id: 13748, name: '13748 - Equipment' },
    route: { slug: 'gas', name: 'Medições de Gás' }
  }
export const integrationEquipmentResponseMock2: IIntegrationEquipmentResponse =
  {
    id: 120,
    active: true,
    external_id: '1',
    integration: { id: 2, name: 'Gestal' },
    integration_account: { id: 27, name: '321' },
    equipment: { id: 13748, name: '13748 - Equipment' },
    route: { slug: 'energy', name: 'Medições de Energia' }
  }
/** Response parsed domain  */
export const integrationEquipmentMock1: IIntegrationEquipment = {
  id: 121,
  active: false,
  externalId: '1',
  integration: { id: 2, name: 'Gestal' },
  integrationAccount: { id: 23, name: '023 - Gestal Integration - horário' },
  equipment: { id: 13748, name: '13748 - Equipment' },
  route: { slug: 'gas', name: 'Medições de Gás' }
}
export const integrationEquipmentMock2: IIntegrationEquipment = {
  id: 120,
  active: true,
  externalId: '1',
  integration: { id: 2, name: 'Gestal' },
  integrationAccount: { id: 27, name: '321' },
  equipment: { id: 13748, name: '13748 - Equipment' },
  route: { slug: 'energy', name: 'Medições de Energia' }
}
