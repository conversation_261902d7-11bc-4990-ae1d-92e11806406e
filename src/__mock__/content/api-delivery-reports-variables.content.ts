import IDeliveryReportsVariables from '@/@core/domain/DeliveryReportsVariables'
import { Response } from '@/@core/infra/api/DeliveryReportsVariablesApiV4/DeliveryReportsVariablesApiV4.types'

/** Response */
export const deliveryReportVariablesResponseMock1: Response = {
  id: 1,
  delivery_report_id: 1,
  variable_id: 3,
  variable_name: 'variavel 3',
  meter_value: 1.22,
  pliers_ammeter_value: 1.01,
  telemetry_value: 2.02,
  documents: []
}

export const deliveryReportVariablesResponseMock2: Response = {
  id: 22,
  delivery_report_id: 1,
  variable_id: null,
  variable_name: '',
  meter_value: 3.12,
  pliers_ammeter_value: '5.01',
  telemetry_value: 2.59,
  documents: null
}

/** Response parsed domain  */
export const deliveryReportVariablesMock1: IDeliveryReportsVariables = {
  id: 1,
  deliveryReportId: 1,
  variable: { id: 3, name: 'variavel 3' },
  variableId: 3,
  variableName: 'variavel 3',
  meterValue: '1.22',
  pliersAmmeterValue: '1.01',
  telemetryValue: '2.020',
  documents: []
}

export const deliveryReportVariablesMock2: IDeliveryReportsVariables = {
  id: 22,
  deliveryReportId: 1,
  variable: null,
  variableId: 0,
  variableName: '',
  meterValue: '3.12',
  pliersAmmeterValue: '',
  telemetryValue: '2.590',
  documents: []
}
