import { Response } from '@/@core/infra/api/CompaniesSalesforceApiv4/CompaniesSalesforceApiv4.types'
import { ICompanySalesforce } from '@/@core/domain/CompanySalesforce'

export const companySalesforceResponseMock1: Response = {
  id: 1,
  company_id: 1,
  external_id: 'externalId',
  source: 'source'
}

export const companySalesforceResponseMock2: Response = {
  id: 2,
  company_id: 2,
  external_id: 'externalId2',
  source: 'source2'
}

export const companySalesforceMock1: ICompanySalesforce = {
  id: 1,
  companyId: 1,
  externalId: 'externalId',
  source: 'source'
}

export const companySalesforceMock2: ICompanySalesforce = {
  id: 2,
  companyId: 2,
  externalId: 'externalId2',
  source: 'source2'
}
