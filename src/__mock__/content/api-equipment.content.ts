import { Response } from '@/@core/infra/api/EquipmentsApiV4/EquipmentsApiV4.types'
import { IEquipment } from '@/@core/domain/Equipment'

/** Response */
export const equipmentResponseMock1: Response = {
  id: 6,
  name: 'Equipamento 006',
  account: { id: 1, name: 'Conta Zordon' },
  company: { id: 4, name: 'BBC' },
  device: { id: 8, code: '********', model_id: 3, type_id: 1 },
  model: { id: 3, name: 'Konect' },
  distributor: { id: 13, name: 'ENEL SP' },
  rule_operational: { id: 3, title: 'ignorar' },
  is_virtual: false,
  scde_key: '123456'
}
export const equipmentResponseMock2: Response = {
  id: 521,
  name: 'Tópico físico',
  account: null,
  company: { id: 143, name: '0143 - Company' },
  device: null,
  model: null,
  distributor: { id: 13, name: 'ENEL SP' },
  rule_operational: { id: 3, title: 'ignorar' },
  is_virtual: false,
  scde_key: null
}
/** Response parsed domain  */
export const equipmentMock1: IEquipment = {
  id: 6,
  name: 'Equipamento 006',
  account: { id: 1, name: '<PERSON><PERSON>' },
  accountId: 1,
  accountName: 'Conta Zordon',
  company: { id: 4, name: 'BBC' },
  companyId: 4,
  companyName: 'BBC',
  device: { id: 8, code: '********', modelId: 3, typeId: 1 },
  model: { id: 3, name: 'Konect' },
  distributor: { id: 13, name: 'ENEL SP' },
  distributorId: 13,
  distributorName: 'ENEL SP',
  ruleOperational: { id: 3, title: 'ignorar' },
  isVirtual: false,
  scdeKey: '123456'
}
export const equipmentMock2: IEquipment = {
  id: 521,
  name: 'Tópico físico',
  account: null!,
  accountId: null!,
  accountName: null!,
  company: { id: 143, name: '0143 - Company' },
  companyId: 143,
  companyName: '0143 - Company',
  device: null,
  model: null,
  distributor: { id: 13, name: 'ENEL SP' },
  distributorId: 13,
  distributorName: 'ENEL SP',
  ruleOperational: { id: 3, title: 'ignorar' },
  isVirtual: false,
  scdeKey: null
}
