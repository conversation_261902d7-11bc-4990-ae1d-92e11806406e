import { Response } from '@/@core/infra/api/SubTypesApiV3/SubTypesApiV3.types'
import ISubType, { ISubTypeGrouped } from '@/@core/domain/SubType'

/** Response */
export const subTypeResponseMock1: Response = {
  id: 6,
  name: '<PERSON><PERSON>',
  type: { id: 1, name: 'Energia' }
}

/** Response parsed domain  */
export const subTypeMock1: ISubType = {
  id: 6,
  name: 'Fronteira',
  type: { id: 1, name: 'Energia' }
}
export const subTypeGroupedMock1: ISubTypeGrouped = {
  id: 1,
  name: 'Energia',
  subTypes: [{ id: 6, name: 'Fronteira' }]
}
