import { storeAuthMock } from '../content/store.auth'
import { storeLanguageMock } from '../content/store.language'
import { storeThemeMock } from '../content/store.theme'
import { storeSystemMock } from '../content/store.system'
import { storeSystemLoadingMock } from '../content/store.systemLoading'
import { storeSystemToastMock } from '../content/store.systemToast'
import { storeAccountsMock } from '../content/store.page.accounts'
import { storeAccountIdMock } from '../content/store.page.accountId'
import { storeAlarmsMock } from '../content/store.page.alarms'
import { storeListDeviceModelMock } from '../content/store.list.deviceModel'
import { storeDeviceInstallationFieldsMock } from '../content/store.list.deviceInstallationFields'
import { storeListSubTypeMock } from '../content/store.list.deviceListSubType'
import { storePropertiesMock } from '../content/store.page.property'
import { storeListStateMock } from '../content/store.list.state'
import { storeListSubmarketMock } from '../content/store.list.submarket'
import { storeListTypeMock } from '../content/store.list.types'
import { storeListCitiesMock } from '../content/store.list.cities'
import { storeListCompanyCategoriesMock } from '../content/store.list.company.categories'
import { storeSystembreadcrumbMock } from '../content/store.systemBreadcrumb'

import { RootState } from '@/@core/framework/plugins/redux/store'
import { storeListExternalCodesMock } from '../content/store.list.external.codes'
import { storeListIntegrationsFrequenciesMock } from '../content/store.list.integrations.frequencies'
import { storeListOperationalRuleMock } from '../content/store.list.operational.rule'

export const preloadedStateMock: RootState = {
  auth: storeAuthMock,
  pageAccounts: storeAccountsMock,
  pageAccountsId: storeAccountIdMock,
  pageAlarms: storeAlarmsMock,
  system: storeSystemMock,
  systemLanguage: storeLanguageMock,
  systemLoading: storeSystemLoadingMock,
  systemTheme: storeThemeMock,
  systemToast: storeSystemToastMock,
  systemBreadcrumb: storeSystembreadcrumbMock,
  listDeviceModel: storeListDeviceModelMock,
  pageProperties: storePropertiesMock,
  listDeviceInstallationFields: storeDeviceInstallationFieldsMock,
  listSubType: storeListSubTypeMock,
  listState: storeListStateMock,
  listSubmarket: storeListSubmarketMock,
  listType: storeListTypeMock,
  listCity: storeListCitiesMock,
  listCompanyCategories: storeListCompanyCategoriesMock,
  listExternalCodes: storeListExternalCodesMock,
  listIntegrationsFrequencies: storeListIntegrationsFrequenciesMock,
  listOperationalRule: storeListOperationalRuleMock,
  listTypeSubTypeGrouped: {
    list: []
  },
  listApportionmentTariffTypes: {
    list: []
  }
}
