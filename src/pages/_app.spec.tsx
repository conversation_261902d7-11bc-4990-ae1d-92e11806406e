import React from 'react'
import { Router } from 'next/router'
import { cleanup, fireEvent, screen } from '@testing-library/react'
import { renderWithRedux } from '@/utils/setupTest'
import { appCookie, systemCookie } from '@/@core/infra/memory/cookie'
import App from './_app'

cleanup()

jest.mock('@/@core/presentation/layouts', () => ({
  Layout: (p: { children: React.ReactNode }) => <>{p.children}</>
}))
jest.mock('@/provider', () => ({
  AppProvider: (p: { children: React.ReactNode }) => <>{p.children}</>
}))

describe('src/pages/_app', () => {
  beforeEach(() => {
    appCookie.init()
  })

  const ComponentTest = () => {
    return <>Hello</>
  }

  test('render Component', async () => {
    const { unmount } = renderWithRedux(
      <App Component={ComponentTest} pageProps={{}} router={{} as Router} />
    )
    expect(await screen.findByText('Hello')).toBeInTheDocument()

    unmount()
  })

  test('test event keydown', async () => {
    const { container, unmount } = renderWithRedux(
      <App Component={ComponentTest} pageProps={{}} router={{} as Router} />
    )

    expect(await screen.findByText('Hello')).toBeInTheDocument()

    expect(systemCookie.get()).toEqual({
      language: 'pt-BR',
      theme: 'light',
      themeColor: 'default'
    })

    /** change theme to dark */
    fireEvent.keyDown(container, {
      altKey: true,
      code: 'KeyT'
    })
    expect(systemCookie.get().theme).toBe('dark')

    /** change theme to light */
    fireEvent.keyDown(container, {
      altKey: true,
      code: 'KeyT'
    })
    expect(systemCookie.get().theme).toBe('light')

    /** change language to en */
    fireEvent.keyDown(container, {
      altKey: true,
      code: 'KeyL'
    })
    expect(systemCookie.get().language).toBe('en')

    /** change language to pt-BR */
    fireEvent.keyDown(container, {
      altKey: true,
      code: 'KeyL'
    })
    expect(systemCookie.get().language).toBe('pt-BR')

    unmount()
  })
})
