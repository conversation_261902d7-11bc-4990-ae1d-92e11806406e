import { Layout } from '@/@core/presentation/layouts'
import {
  SetupObserver,
  SetupkeyDown
} from '@/@core/presentation/views/_app/index.setup'
import '@/assets/css/app.scss'
import { AppProvider } from '@/provider'
import { AppPropsLayout } from '@/types/system/layout'
import '@fortawesome/fontawesome-svg-core/styles.css'

export default function App({ Component, pageProps }: AppPropsLayout) {
  return (
    <AppProvider>
      <SetupkeyDown />
      <SetupObserver />
      <Layout layout={Component.layout}>
        <Component {...pageProps} />
      </Layout>
    </AppProvider>
  )
}
