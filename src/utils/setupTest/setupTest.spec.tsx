import { menuItem1 } from '@/__mock__/content/menu.layout.contents'
import { preloadedStateMock } from '@/__mock__/store/preloadedState'
import { actions } from '@/@core/framework/plugins/redux/features/system'
import { RootState } from '@/@core/framework/plugins/redux/store'

import { render, renderWithRedux } from './setupTest'

const ComponentTest = () => {
  return <div>component setup test</div>
}

describe('/utils/tests/setup', () => {
  const initialState: RootState = Object.assign({}, preloadedStateMock)

  it('render', () => {
    const { getByText } = render(<ComponentTest />)

    expect(getByText('component setup test')).toBeTruthy()
  })

  it('renderWithRedux without mock', () => {
    const { getByText, dispatch, getState } = renderWithRedux(<ComponentTest />)
    expect(getByText('component setup test')).toBeTruthy()

    expect(getState().system.menuItems.length).toEqual(0)

    dispatch(actions.setMenuItems([menuItem1]))

    expect(getState().system.menuItems.length).toEqual(1)
  })

  it('renderWithRedux with mock', () => {
    const { getByText, dispatch, getState } = renderWithRedux(
      <ComponentTest />,
      {
        initialState
      }
    )
    expect(getByText('component setup test')).toBeTruthy()

    expect(getState().system.menuItems.length).toEqual(0)

    dispatch(actions.setMenuItems([menuItem1]))

    expect(getState().system.menuItems.length).toEqual(1)
  })
})
