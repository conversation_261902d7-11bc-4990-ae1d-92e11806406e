import { Provider } from 'react-redux'
import { NextRouter } from 'next/router'
import { RouterContext } from 'next/dist/shared/lib/router-context.shared-runtime'

import { Action, configureStore } from '@reduxjs/toolkit'
import { render as renderTesting, renderHook } from '@testing-library/react'

import { preloadedStateMock } from '@/__mock__/store/preloadedState'
import { AppThemeModeProvider } from '@/provider/AppThemeModeProvider'
import { AppThemeProvider } from '@/provider/AppThemeProvider'
import { ProviderTeleport } from '@/provider/AppProviderTeleport'
import { AppLocalizationProvider } from '@/provider/AppLocalizationProvider'

import { RootState } from '@/@core/framework/plugins/redux/store'
import reducers from '@/@core/framework/plugins/redux/reducers'
import { ThemeModeTypes } from '@/types/theme'

const getStore = (initialState = preloadedStateMock) => {
  return configureStore({
    reducer: reducers,
    preloadedState: initialState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        immutableCheck: false,
        serializableCheck: false
      })
  })
}

export const renderHookWithRedux = function <T>(
  hook: (args: T) => T,
  { initialState }: { initialState?: RootState } = {}
) {
  const store = getStore(initialState)

  return renderHook(hook, {
    wrapper: ({ children }) => (
      <RouterContext.Provider value={routerMock}>
        <Provider store={store}>
          <ProviderTeleport>{children}</ProviderTeleport>
        </Provider>
      </RouterContext.Provider>
    )
  })
}

export const renderWithRedux = (
  ui: React.ReactNode,
  {
    initialState,
    mode = 'light'
  }: {
    initialState?: RootState
    mode?: ThemeModeTypes
  } = {}
) => {
  const store = getStore(initialState)

  const utils = {
    dispatch: (action: Action) => store.dispatch(action),
    getState: () => store.getState()
  }

  const toggleThemeMode = () => {
    console.log('change mode theme')
  }

  const rend = renderTesting(
    <RouterContext.Provider value={routerMock}>
      <AppThemeModeProvider toggleThemeMode={toggleThemeMode}>
        <AppThemeProvider mode={mode}>
          <Provider store={store}>
            <ProviderTeleport>
              <AppLocalizationProvider>
                <>{ui}</>
              </AppLocalizationProvider>
            </ProviderTeleport>
          </Provider>
        </AppThemeProvider>
      </AppThemeModeProvider>
    </RouterContext.Provider>
  )

  return {
    ...rend,
    ...utils
  }
}

export const render = renderTesting

const routerMock: NextRouter = {
  basePath: '',
  pathname: '/',
  route: '/',
  query: {},
  asPath: '/',
  back: jest.fn(),
  beforePopState: jest.fn(),
  prefetch: jest.fn(),
  push: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn()
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
  forward: jest.fn()
}
