const labels = {
  conected: 'Conectad<PERSON>',
  disconnected: '<PERSON><PERSON><PERSON>tad<PERSON>',
  observation: 'Observação',
  disabled: 'Desativado',
  alarm: '<PERSON>arm<PERSON>',
  standBy: 'Stand By'
}

const dataActionSupport = [
  {
    slug: 'conected',
    slugDepreciated: 'conectado',
    label: labels.conected,
    cssTag: 'alarm-tag-conected',
    cssMenuItem: 'status-menu-item-connected',
    actionSupportId: 1
  },
  {
    slug: 'disconnected',
    slugDepreciated: 'desconectado',
    label: labels.disconnected,
    cssTag: 'alarm-tag-disconnected',
    cssMenuItem: 'status-menu-item-disconnected',
    actionSupportId: 2
  },
  {
    slug: 'observation',
    slugDepreciated: 'em_observacao',
    label: labels.observation,
    cssTag: 'alarm-tag-observation',
    cssMenuItem: 'status-menu-item-observation',
    actionSupportId: 3
  },
  {
    slug: 'alarm',
    slugDepreciated: 'alarme',
    label: labels.alarm,
    cssTag: 'alarm-tag-alarm',
    cssMenuItem: 'status-menu-item-alarm',
    actionSupportId: 4
  },
  {
    slug: 'disabled',
    slugDepreciated: 'desativado',
    label: labels.disabled,
    cssTag: 'alarm-tag-disabled',
    cssMenuItem: 'status-menu-item-disabled',
    actionSupportId: 5
  },
  {
    slug: 'standBy',
    slugDepreciated: 'em_espera',
    label: labels.standBy,
    cssTag: 'alarm-tag-standBy',
    cssMenuItem: 'status-menu-item-standBy',
    actionSupportId: 6
  }
]

const dataStatus = [
  { slug: 'pre_alarm', label: 'Pré alarme', css: 'observation' },
  { slug: 'alarm', label: 'Alarme', css: 'alarm' },
  { slug: 'normalized', label: 'Normalizado', css: 'conected' },
  { slug: 'recognized', label: 'Reconhecido', css: 'recognized' },
  { slug: 'standBy', label: 'Stand By', css: 'standBy' }
]

export const getDataActionSupportById = (id: number) => {
  return dataActionSupport.find((el) => el.actionSupportId === id)
}

export const getLabelByActionSupportId = (id: number) => {
  return getDataActionSupportById(id)?.label ?? ''
}

export const getSlugByActionSupportId = (id: number) => {
  return getDataActionSupportById(id)?.slug ?? ''
}

export const getLabelByStatus = (status: string) => {
  return dataStatus.find((el) => el.slug === status)?.label
}

export const getCssByStatus = (status: string) => {
  return dataStatus.find((el) => el.slug === status)?.css
}

export const getLabelByStatusDepreciated = (status: string) => {
  return dataActionSupport.find((el) => el.slugDepreciated === status)!.label
}

export const getCssTagByStatusDepreciated = (status: string) => {
  return dataActionSupport.find((el) => el.slugDepreciated === status)?.cssTag
}

export const getCssMenuItemByStatusDepreciated = (status: string) => {
  return dataActionSupport.find((el) => el.slugDepreciated === status)
    ?.cssMenuItem
}
