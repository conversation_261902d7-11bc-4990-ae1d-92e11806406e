import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import dayjs from 'dayjs'

export const timeLocal = {
  0: 'Atlantic/St_Helena',
  '-1': 'Atlantic/Cape_Verde',
  '-2': 'America/Noronha',
  '-3': 'America/Sao_Paulo',
  '-4': 'America/Manaus',
  '-5': 'America/Rio_Branco'
}

const FORMAT_DATE_UTIL = {
  year: 'numeric',
  month: 'numeric',
  day: 'numeric',
  hour: 'numeric',
  minute: 'numeric',
  second: 'numeric'
}

export const FORMAT_TIMEZONE_UTIL = {
  ptBr: 'pt'
}

export const CUSTOM_FORMAT_DATE_UTIL = {
  'dd-mm-yyyy': {
    year: FORMAT_DATE_UTIL.year,
    month: FORMAT_DATE_UTIL.month,
    day: FORMAT_DATE_UTIL.day
  },
  'dd-mm-yyyy hh:mm': {
    year: FORMAT_DATE_UTIL.year,
    month: FORMAT_DATE_UTIL.month,
    day: FORMAT_DATE_UTIL.day,
    hour: FORMAT_DATE_UTIL.hour,
    minute: FORMAT_DATE_UTIL.minute
  },
  'dd-mm-yyyy hh:mm:ss': {
    year: FORMAT_DATE_UTIL.year,
    month: FORMAT_DATE_UTIL.month,
    day: FORMAT_DATE_UTIL.day,
    hour: FORMAT_DATE_UTIL.hour,
    minute: FORMAT_DATE_UTIL.minute,
    second: FORMAT_DATE_UTIL.second
  }
}
export default class DateUtilsService {
  static getDateFormat({ value, gmt }) {
    dayjs.extend(utc)
    dayjs.extend(timezone)

    const local = timeLocal[gmt]
    const date = dayjs.unix(value).tz(local).format('DD/MM/YYYY HH:mm')

    return date
  }

  static getDateTimeWithTZ(value) {
    return new Date(
      new Date(value).toLocaleString('en-US', { timeZone: 'America/Sao_Paulo' })
    ).getTime()
  }

  static getDayOfMounth(witchDay, returnType) {
    const date = new Date()
    let day

    switch (witchDay) {
      case 'first':
        day = new Date(date.getFullYear(), date.getMonth(), 1)
        break

      case 'last':
        day = new Date(date.getFullYear(), date.getMonth() + 1, 0)
        break
    }

    if (returnType === 'getTime') {
      return day.getTime()
    }

    if (returnType === 'getSecond') {
      return day.getTime() / 1000
    }

    return day
  }

  static getFirstDayOfMounth(returnType) {
    return this.getDayOfMounth('first', returnType)
  }

  static getLastDayOfMounth(returnType) {
    return this.getDayOfMounth('last', returnType)
  }
}
