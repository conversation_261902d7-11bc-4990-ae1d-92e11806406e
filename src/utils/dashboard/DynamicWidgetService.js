export default class DynamicWidgetService {
  // promise
  static preparePromise(apiRoutes, inputData, rangeDate, http) {}

  // response

  //structure
  static parseStructure(structure) {
    const {
      title,
      type,
      chartOptions,
      forNoRecords,
      apiRoutes: apiRoute
    } = structure

    const { content } = forNoRecords ?? {}

    return {
      title,
      type,
      apiRoute,
      chartOptions,
      forNoRecords: {
        config: content ?? {}
      }
    }
  }
}
