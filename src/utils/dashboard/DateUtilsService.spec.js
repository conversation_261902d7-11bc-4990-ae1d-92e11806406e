import DateUtilsService, {
  CUSTOM_FORMAT_DATE_UTIL,
  FORMAT_TIMEZONE_UTIL,
  timeLocal
} from './DateUtilsService'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

describe('DateUtilsService', () => {
  beforeAll(() => {
    dayjs.extend(utc)
    dayjs.extend(timezone)
  })

  describe('getDateFormat', () => {
    it('should return formatted date for given value and GMT', () => {
      const mockValue = 1693824000 // Unix timestamp
      const gmt = '-3'
      const formattedDate = DateUtilsService.getDateFormat({
        value: mockValue,
        gmt
      })

      expect(formattedDate).toBe('04/09/2023 07:40')
    })
  })

  describe('getDateTimeWithTZ', () => {
    it('should return timestamp for date com timezone America/Sao_Paulo', () => {
      const mockValue = '2023-09-04T00:00:00Z'
      const timestamp = DateUtilsService.getDateTimeWithTZ(mockValue)

      expect(typeof timestamp).toBe('number')
      expect(timestamp).not.toBeNaN()
    })
  })

  describe('getDayOfMounth', () => {
    it('should return first day of month as date object', () => {
      const result = DateUtilsService.getDayOfMounth('first')
      const expected = new Date(
        new Date().getFullYear(),
        new Date().getMonth(),
        1
      )

      expect(result).toEqual(expected)
    })

    it('should return last day of the month as timestamp in milliseconds', () => {
      const result = DateUtilsService.getDayOfMounth('last', 'getTime')
      const expected = new Date(
        new Date().getFullYear(),
        new Date().getMonth() + 1,
        0
      ).getTime()

      expect(result).toEqual(expected)
    })

    it('should return last day of month as seconds', () => {
      const result = DateUtilsService.getDayOfMounth('last', 'getSecond')
      const expected =
        new Date(
          new Date().getFullYear(),
          new Date().getMonth() + 1,
          0
        ).getTime() / 1000

      expect(result).toEqual(expected)
    })
  })

  describe('getFirstDayOfMounth', () => {
    const spy = jest.spyOn(DateUtilsService, 'getDayOfMounth')
    const result = DateUtilsService.getFirstDayOfMounth('getTime')

    expect(spy).toHaveBeenCalledWith('first', 'getTime')
    expect(result).toBeDefined()
    spy.mockRestore()
  })

  describe('getLastDayOfMounth', () => {
    const spy = jest.spyOn(DateUtilsService, 'getDayOfMounth')
    const result = DateUtilsService.getLastDayOfMounth('getSecond')

    expect(spy).toHaveBeenCalledWith('last', 'getSecond')
    expect(result).toBeDefined()
    spy.mockRestore()
  })

  describe('Constants', () => {
    it('should have correct mappings in timeLocal', () => {
      expect(timeLocal[0]).toBe('Atlantic/St_Helena')
      expect(timeLocal['-1']).toBe('Atlantic/Cape_Verde')
      expect(timeLocal['-2']).toBe('America/Noronha')
      expect(timeLocal['-3']).toBe('America/Sao_Paulo')
      expect(timeLocal['-4']).toBe('America/Manaus')
      expect(timeLocal['-5']).toBe('America/Rio_Branco')
    })

    it('should return undefined for invalid timeLocal key', () => {
      expect(timeLocal['-10']).toBeUndefined()
    })
  })

  describe('FORMAT_TIMEZONE_UTIL', () => {
    it('should have correct ptBr mapping', () => {
      expect(FORMAT_TIMEZONE_UTIL.ptBr).toBe('pt')
    })
  })

  describe('CUSTOM_FORMAT_DATE_UTIL', () => {
    it('should have correct mappings for date formats', () => {
      expect(CUSTOM_FORMAT_DATE_UTIL['dd-mm-yyyy']).toEqual({
        year: 'numeric',
        month: 'numeric',
        day: 'numeric'
      })
      expect(CUSTOM_FORMAT_DATE_UTIL['dd-mm-yyyy hh:mm']).toEqual({
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric'
      })
      expect(CUSTOM_FORMAT_DATE_UTIL['dd-mm-yyyy hh:mm:ss']).toEqual({
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric'
      })
    })
  })
})
