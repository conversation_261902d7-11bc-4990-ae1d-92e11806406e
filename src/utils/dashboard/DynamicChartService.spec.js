import DynamicChartService, {
  formatUrl,
  preparePayload,
  formatMoment,
  formatDateRange,
  mergeDataParams
} from './DynamicChartService'

jest.mock('.', () => ({
  getInstanceType: jest.fn(() => 'mockInstance')
}))

describe('DynamicChartService', () => {
  const mockHttp = {
    post: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('preparePromise', () => {
    it('should throw an error if apiRoute is not provided', () => {
      expect(() =>
        DynamicChartService.preparePromise(null, {}, {}, mockHttp)
      ).toThrow('apiRoute is required')
    })

    it('should call http.post with correct url and payload', () => {
      const mockApiRoute = { main: '/api/{instance}' }
      const mockInputData = { id: 1 }
      const mockRangeDate = { date: ['2025-01-01', '2025-01-02'] }
      const expectedUrl = '/api/mockInstance'
      const expectedPayload = {
        final_date: '2025-01-02',
        initial_date: '2025-01-01',
        id: 1,
        empresa_id: undefined,
        date: undefined
      }

      mockHttp.post.mockResolvedValueOnce({})

      DynamicChartService.preparePromise(
        mockApiRoute,
        mockInputData,
        mockRangeDate,
        mockHttp
      )

      expect(mockHttp.post).toHaveBeenCalledWith(expectedUrl, expectedPayload)
    })
  })

  describe('parseResponse', () => {
    it('should return the data property of apiResponse', () => {
      const mockApiResponse = { data: { someKey: 'someValue' } }
      const result = DynamicChartService.parseResponse(mockApiResponse, {
        inputData: {}
      })

      expect(result).toEqual(mockApiResponse.data)
    })
  })

  describe('parsePayload', () => {
    it('should correctly parse the payload', () => {
      const mockPayload = {
        final_date: '2024-01-31',
        initial_date: '2024-01-01',
        id: 1,
        empresa_id: 2,
        date: '2024-01-01'
      }

      const result = DynamicChartService.parsePayload(mockPayload)

      expect(result).toEqual({
        final_date: '2024-01-31',
        initial_date: '2024-01-01',
        id: 1,
        empresa_id: 2,
        date: '2024-01-01'
      })
    })
  })
})

describe('Utility Functions', () => {
  describe('formatMoment', () => {
    it('should format date to startOf month', () => {
      const result = formatMoment('2024-01-15', 'startOf')
      expect(result).toBe('2024-01-01')
    })

    it('should format date to endOf month', () => {
      const result = formatMoment('2024-01-15', 'endOf')
      expect(result).toBe('2024-01-31')
    })

    it('should return todays date if formatted date is in the future', () => {
      const futureDate = new Date()
      futureDate.setFullYear(futureDate.getFullYear() + 1)

      const result = formatMoment(
        futureDate.toISOString().substr(0, 10),
        'default'
      )

      const today = new Date().toISOString().substr(0, 10)
      expect(result).toBe(today)
    })
  })

  describe('formatUrl', () => {
    it('should replace {instance} in apiRoute with instance type when apiRoute[type] is a string', () => {
      const mockApiRoute = { main: { url: '/api/{instance}' } }
      const mockInputData = {}

      const result = formatUrl(mockInputData, mockApiRoute, {
        date: ['2025-01-01', '2025-01-02']
      })

      expect(result).toBe('/api/mockInstance')
    })

    it('should replace {instance} in apiRoute with instance type when apiRoute[type].url exists', () => {
      const mockApiRoute = { range: { url: '/range/{instance}' } }
      const mockInputData = {}

      const result = formatUrl(mockInputData, mockApiRoute, { check: true })

      expect(result).toBe('/range/mockInstance')
    })

    it('should replace {instance} in apiRoute.url when apiRoute exists but not type-specific', () => {
      const mockApiRoute = { url: '/default/{instance}' }
      const mockInputData = {}

      const result = formatUrl(mockInputData, mockApiRoute, null)

      expect(result).toBe('/default/mockInstance')
    })
  })

  describe('preparePayload', () => {
    it('should prepare payload with correct date range and additional params', () => {
      const mockApiRoute = {
        range: {
          params: { key1: 'value1' },
          method: 'POST'
        }
      }
      const mockInputData = { date: '2024-01' }
      const mockRangeDate = { date: ['2024-01-01', '2024-01-31'], check: true }

      const result = preparePayload(mockInputData, mockApiRoute, mockRangeDate)

      expect(result).toEqual({
        data: {
          initial_date: '2024-01-01',
          final_date: '2024-01-31',
          date: '2024-01',
          key1: 'value1'
        },
        params: { key1: 'value1' },
        method: 'POST'
      })
    })

    it('should prepare payload with correct date range and empty params if not provided', () => {
      const mockApiRoute = {
        range: {
          method: 'POST'
        }
      }
      const mockInputData = { date: '2024-01' }
      const mockRangeDate = { date: ['2024-01-01', '2024-01-31'], check: true }

      const result = preparePayload(mockInputData, mockApiRoute, mockRangeDate)

      expect(result).toEqual({
        data: {
          initial_date: '2024-01-01',
          final_date: '2024-01-31',
          date: '2024-01'
        },
        params: {},
        method: 'POST'
      })
    })

    it('should prepare payload with correct date range', () => {
      const mockApiRoute = { main: '/api/{instance}' }
      const mockInputData = { date: '2024-01' }
      const mockRangeDate = { date: ['2024-01-01', '2024-01-31'], check: true }

      const result = preparePayload(mockInputData, mockApiRoute, mockRangeDate)

      expect(result).toEqual({
        data: {
          initial_date: '2024-01-01',
          final_date: '2024-01-31',
          date: '2024-01'
        },
        params: undefined,
        method: undefined
      })
    })
  })

  describe('formatDateRange', () => {
    it('should format date range when rangeDate is provided', () => {
      const originalPayload = { date: '2024-01' }
      const rangeDate = { date: ['2024-01-01', '2024-01-31'] }

      const result = formatDateRange(originalPayload, rangeDate)

      expect(result).toEqual({
        date: '2024-01',
        initial_date: '2024-01-01',
        final_date: '2024-01-31'
      })
    })

    it('should format date range with startOf and endOf month when rangeDate is not provided', () => {
      const originalPayload = { date: '2024-01' }

      const result = formatDateRange(originalPayload, null)

      expect(result).toEqual({
        date: '2024-01',
        initial_date: '2024-01-01',
        final_date: '2024-01-31'
      })
    })
  })

  describe('mergeDataParams', () => {
    it('should use data value when params[key] is null', () => {
      const inputObj = {
        data: { key1: 'value1', key2: 'value2' },
        params: { key1: null, key2: 'override' }
      }

      const result = mergeDataParams(inputObj)

      expect(result.data).toEqual({
        key1: 'value1',
        key2: 'override'
      })
    })
  })
})

describe('parseStructure', () => {
  it('should correctly parse structure object', () => {
    const mockStructure = {
      title: 'Test Chart',
      type: 'line',
      chartOptions: { responsive: true },
      forNoRecords: { content: { message: 'No records found' } },
      apiRoutes: { main: '/api/test' },
      rangeTitle: 'Date Range'
    }

    const expectedOutput = {
      title: 'Test Chart',
      type: 'line',
      apiRoute: { main: '/api/test' },
      chartOptions: { responsive: true },
      forNoRecords: {
        config: { message: 'No records found' }
      },
      rangeTitle: 'Date Range'
    }

    const result = DynamicChartService.parseStructure(mockStructure)
    expect(result).toEqual(expectedOutput)
  })

  it('should set forNoRecords.config to an empty object when content is undefined', () => {
    const mockStructure = {
      title: 'Test Chart',
      type: 'bar',
      chartOptions: { responsive: true },
      forNoRecords: undefined,
      apiRoutes: { main: '/api/test' },
      rangeTitle: 'Date Range'
    }

    const expectedOutput = {
      title: 'Test Chart',
      type: 'bar',
      apiRoute: { main: '/api/test' },
      chartOptions: { responsive: true },
      forNoRecords: {
        config: {}
      },
      rangeTitle: 'Date Range'
    }

    const result = DynamicChartService.parseStructure(mockStructure)
    expect(result).toEqual(expectedOutput)
  })
})
