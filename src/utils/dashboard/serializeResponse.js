export default function serializeResponse(componentType, apiResponse, config) {
  if (componentType === 'DynamicDonut') {
    return serializeResponseDonut(apiResponse)
  }
  if (componentType === 'DynamicDemand') {
    return serializeResponseDemand(apiResponse, config)
  }
}

const forEachSummarize = (apiResponse, fnCallback) => {
  return apiResponse
    .filter((el) => Array.isArray(el?.summary) && el?.summary.length)
    .map((el, index) =>
      el.summary.map((summaryItem) => fnCallback(summaryItem, index))
    )
}
const serializeResponseDonut = (apiResponse) => {
  return forEachSummarize(apiResponse, function (summaryItem) {
    const demandDisplayObject = {
      name: summaryItem.entity_name,
      y: summaryItem.data.value
    }
    donutList.push(demandDisplayObject)
  })
}
const serializeResponseDemand = (apiResponse, config) => {
  const demandList = []

  forEachSummarize(apiResponse, function (summaryItem, index) {
    const contentConfig = config[index].config

    demandList.push({
      fieldLabel: contentConfig.textLabel
        ? contentConfig.textLabel
        : summaryItem[contentConfig.fieldLabel],
      fieldValue: contentConfig.textValue
        ? contentConfig.textValue
        : summaryItem.data[contentConfig.fieldValue],
      descriptionValue: contentConfig.descriptionLabel
        ? contentConfig.descriptionLabel
        : summaryItem.data[contentConfig.descriptionValue],
      unit: contentConfig.unit,
      decimal:
        contentConfig?.decimal !== undefined ? contentConfig?.decimal : 2,
      entity: summaryItem.entity || '',
      entity_name: summaryItem.entity_name || '',
      property: summaryItem.property || '',
      data: {
        value: summaryItem.data.value || '',
        timestamp: summaryItem.data.timestamp
      }
    })
  })

  return demandList
}
