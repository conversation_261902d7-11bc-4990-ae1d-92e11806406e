import changeMonth from './changeMonth'
import dayjs from 'dayjs'

describe('changeMonth', () => {
  beforeEach(() => {
    jest.useFakeTimers()
    jest.setSystemTime(new Date('2023-12-15T12:00:00Z'))
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  test('should return correct values for a valid date', () => {
    const date = '2023-12'
    const result = changeMonth(date)

    expect(result.today).toBe(dayjs().startOf('day').unix())
  })

  test('should handle undefined date input', () => {
    const result = changeMonth(undefined)
    expect(result.today).toBe(dayjs().startOf('day').unix())
  })
})
