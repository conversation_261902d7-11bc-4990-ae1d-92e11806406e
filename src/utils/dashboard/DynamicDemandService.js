import { getInstanceType } from '@/utils/dashboard'
import DateUtilsService from '@/utils/dashboard/DateUtilsService'

export default class DynamicDemandService {
  static preparePromise(route, inputData, rangeDate, http) {
    const url = route.apiRoute.replace('{instance}', getInstanceType(inputData))

    const payload = {
      ...formatDateRange(inputData, rangeDate)
    }
    return http.post(url, payload)
  }

  // response
  static parseResponse(inputData, apiResponses, { routes, footer }) {
    /* 
      fields: {
        firstValue: "max",
        firstValueSufix: "",
        secondValue: "contract",
        secondValueSufix: "kW",
        thirdField: "percentage_exceeded",
        titleField: "used",
        titleFieldSufix: "%",
      }
      id: "usado",
      loading: true,
      show: true,
      title: "Usado",
    /* 

    /* 
      'routes[0]'
      description: "time_max",
      id: "consolidada",
      last: false,
      title: "Máxima Registrada",
      unit: "kW",
      value: "max"
    */

    const maxDataFormated = formatMaxData(apiResponses[0], { inputData })
    /*
      contract: 200,
      exceeded: 0,
      max: -0.75726808,
      percentage_exceeded: 0,
      time_max: "13/12/2024 02:15",
      used: -0.37863404,
    */

    const lastDataFormated = formatLastData(apiResponses[1], { inputData })
    /*
      date: "18/12/2024 00:00"
      value:-8.10944
    */

    const dataMax = {
      id: routes[0].config.id,
      title: routes[0].config.title,
      description: maxDataFormated[routes[0].config.description],
      value: maxDataFormated[routes[0].config.value],
      unit: routes[0].config.unit ?? '',
      decimal: undefined
    }
    const dataLast = {
      id: routes[1].config.id,
      title: routes[1].config.title,
      description: lastDataFormated[routes[1].config.description],
      value: lastDataFormated[routes[1].config.value],
      unit: routes[1].config.unit ?? '',
      decimal: undefined
    }

    const dataBar = {
      used: {
        prefix: footer?.title || 'Usado',
        value: formatValue(maxDataFormated?.[footer.fields.titleField] ?? '0'),
        sufix: footer?.fields?.titleFieldSufix ?? 'sufix'
      },
      contract: {
        prefix: maxDataFormated?.[footer.fields.firstValue] ?? '0',
        value: formatValue(maxDataFormated?.[footer.fields.secondValue] ?? '0'),
        sufix: footer.fields.secondValueSufix
      },
      contractValue: maxDataFormated?.[footer.fields.secondValue] ?? 0,
      usedValue: maxDataFormated?.[footer.fields.titleField] ?? 0,
      exceededValue: maxDataFormated?.[footer.fields.percentage_exceeded] ?? 0
    }

    return {
      values: [dataMax, dataLast],
      bar: dataBar
    }
  }

  static parseStructure(structure) {
    const { cardOptions } = structure
    const { title, type, footer, contents, peak } = cardOptions

    return {
      title,
      type,
      peak,
      footer,
      routes: contents.map((el) => ({
        config: {
          id: el.id,
          last: el.last,
          unit: el.unit,
          title: el.title,
          value: el.value,
          description: el.description
        },
        method: 'post', // valor fixo para uso no mobile
        // loading: el.loading,
        apiRoute: el.apiRoute
      }))
    }
  }
}
export const formatValue = (value) => {
  return value
    ? parseFloat(value).toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    : ''
}
export const formatMaxData = ({ data }, { inputData }) => {
  return {
    exceeded: data?.exceeded || 0,
    max: data?.max || 0,
    used: data?.used || 0,
    contract: data?.contract || 0,
    percentage_exceeded: data?.percentage_exceeded || 0,
    time_max: data?.time_max
      ? DateUtilsService.getDateFormat({
          value: data.time_max,
          gmt: inputData.timezone
        })
      : null

    // exceeded: 0,
    // max: -0.75726808,
    // used: -0.37863404,
    // contract: 200,
    // percentage_exceeded: 0,
    // time_max: 1734066900,
  }
}
export const formatLastData = ({ data }, { inputData }) => {
  let _date = null

  if (data && data.date) {
    _date = DateUtilsService.getDateFormat({
      value: data.date,
      gmt: inputData.timezone
    })
  }

  return {
    date: _date,
    value: data.value
  }
}
export const formatDateRange = (originalPayload, rangeDate) => {
  const finalPayload = originalPayload

  if (rangeDate && Array.isArray(rangeDate.date)) {
    return {
      ...finalPayload,
      initial_date: formatMoment(rangeDate.date[0]),
      final_date: formatMoment(rangeDate.date[1])
    }
  }

  const startMonth = `${finalPayload.date}-01`.replace(/-/g, '/')
  return {
    ...finalPayload,
    initial_date: formatMoment(startMonth, 'startOf'),
    final_date: formatMoment(startMonth, 'endOf')
  }
}
export const formatMoment = (date, type) => {
  let formatted
  const selectedMonth = new Date(date)
  const today = new Date()

  switch (type) {
    case 'startOf':
      formatted = new Date(
        selectedMonth.getFullYear(),
        selectedMonth.getMonth(),
        1
      )
      break
    case 'endOf':
      formatted = new Date(
        selectedMonth.getFullYear(),
        selectedMonth.getMonth() + 1,
        0
      )
      break
    default:
      formatted = new Date(date)
      break
  }

  const formattedDate = formatted > today ? today : formatted

  return formattedDate.toISOString().substr(0, 10)
}
