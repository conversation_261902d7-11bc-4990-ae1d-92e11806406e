import { getInstanceType } from '.'
import DateUtilsService from './DateUtilsService'

export default class DynamicIntakeService {
  static preparePromise(apiRoute, inputData, http) {
    const url = apiRoute.replace('{instance}', getInstanceType(inputData))

    const payload = {
      ...inputData
    }

    return http.post(url, payload)
  }

  // next
  static parseResponse(apiResponse, { inputData, config }) {
    const date = apiResponse.data?.endDate
      ? DateUtilsService.getDateFormat({
          value: apiResponse.data?.endDate,
          gmt: inputData.timezone
        })
      : ''
    const value = apiResponse.data?.value ?? ''

    return {
      date,
      value,
      unit: config?.unit,
      decimal: config?.decimal
    }
  }

  static parseStructure(structure) {
    const { cardOptions } = structure

    const { title, type, style, apiRoute, content } = cardOptions

    return {
      title,
      type,
      peak: style?.classType ?? '',
      routes: [
        {
          method: 'post',
          apiRoute,
          config: {
            description: content.description,
            peakTime: content.peakTime,
            title: content.title,
            unit: content.unit,
            value: content.value
          }
        }
      ]
    }
  }
}
