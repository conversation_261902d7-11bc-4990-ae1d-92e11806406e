import { getInstanceType } from '.'

export default class DynamicChartService {
  // promise
  static preparePromise(apiRoute, inputData, rangeDate, http) {
    if (!apiRoute) {
      throw new Error('apiRoute is required')
    }

    const url = formatUrl(inputData, apiRoute, rangeDate)

    const payload = this.parsePayload(
      preparePayload(inputData, apiRoute, rangeDate)
    )

    return http.post(url, payload)
  }
  static parseResponse(apiResponse, { inputData }) {
    const data = apiResponse.data

    return data

    // if ('series' in apiResponse) {
    //   return resolveResponseForSeries(apiResponse, structure)
    // }
    // if (Array.isArray(apiResponse)) {
    //   return resolveResponseForData(apiResponse, structure)
    // }
    // return resolveResponseForDays(apiResponse, structure)
  }
  // response
  static parseStructure(structure) {
    const {
      title,
      type,
      chartOptions,
      forNoRecords,
      apiRoutes: apiRoute,
      rangeTitle
    } = structure

    const { content } = forNoRecords ?? {}

    return {
      title,
      type,
      apiRoute,
      chartOptions,
      forNoRecords: {
        config: content ?? {}
      },
      rangeTitle
    }
  }

  static parsePayload(payload) {
    return {
      final_date: payload.final_date,
      initial_date: payload.initial_date,
      id: payload.id,
      empresa_id: payload.empresa_id,
      date: payload.date
      //   instance: payload.instance,
      //   changeType: payload.changeType,
      //   codigo: payload.codigo,
      //   route: payload.route,
    }
  }
}

// promise
export const formatDateRange = (originalPayload, rangeDate) => {
  const finalPayload = { ...originalPayload }
  const startMonth = `${finalPayload.date}-01`.replace(/-/g, '/')

  if (rangeDate && Array.isArray(rangeDate.date)) {
    return {
      ...finalPayload,
      initial_date: formatMoment(rangeDate.date[0]),
      final_date: formatMoment(rangeDate.date[1])
    }
  }
  return {
    ...finalPayload,
    initial_date: formatMoment(startMonth, 'startOf'),
    final_date: formatMoment(startMonth, 'endOf')
  }
}
export const formatMoment = (date, type = 'default') => {
  const selectedMonth = new Date(date)
  const today = new Date()

  let formatted = new Date(date)

  if (type === 'startOf') {
    formatted = new Date(
      selectedMonth.getFullYear(),
      selectedMonth.getMonth(),
      1
    )
  }
  if (type === 'endOf') {
    formatted = new Date(
      selectedMonth.getFullYear(),
      selectedMonth.getMonth() + 1,
      0
    )
  }

  const formattedDate = formatted > today ? today : formatted

  return formattedDate.toISOString().substr(0, 10)
}
export const formatUrl = (apiResponse, apiRoute, rangeDate) => {
  const type = rangeDate && rangeDate.check ? 'range' : 'main'

  const instance = getInstanceType(apiResponse)

  if (apiRoute && apiRoute[type] && apiResponse) {
    if (typeof apiRoute[type] === 'string') {
      return apiRoute[type].replace('{instance}', instance)
    } else {
      return apiRoute[type].url.replace('{instance}', instance)
    }
  }
  return apiRoute?.url?.replace('{instance}', instance)
}
export const preparePayload = (apiResponse, apiRoute, rangeDate) => {
  const type = rangeDate && rangeDate.check ? 'range' : 'main'

  let currentPayload = {}

  if (apiRoute && apiRoute[type] && apiResponse) {
    if (typeof apiRoute[type] === 'string') {
      currentPayload = {
        data: formatDateRange(apiResponse, rangeDate)
      }
    } else {
      currentPayload = {
        data: formatDateRange(apiResponse, rangeDate),
        params: apiRoute[type].params || {},
        method: apiRoute[type].method
      }
    }
  } else {
    currentPayload = {
      data: formatDateRange(apiResponse, rangeDate),
      params: apiRoute.params,
      method: apiRoute.method
    }
  }

  if ('params' in currentPayload) {
    currentPayload = mergeDataParams(currentPayload)
    return currentPayload
  }

  return currentPayload.data
}
export const mergeDataParams = (obj) => {
  const data = obj.data
  const params = obj.params

  if (data && params) {
    Object.keys(params).forEach((key) => {
      obj.data[key] =
        key in data && params[key] === null ? data[key] : params[key]
    })
  }
  return obj
}
