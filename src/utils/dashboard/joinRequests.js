import httpParseParams from '@/@core/utils/httpParseParams'

export default function joinRequests(apiRoute) {
  const isMethodGET = apiRoute.method && apiRoute.method.toUpperCase() === 'GET'

  const { route: currentURL } = apiRoute

  if (isMethodGET) {
    const query = httpParseParams(apiRoute.data)

    return {
      method: 'get',
      url: currentURL + query ? `?${query}` : ''
    }
  }

  return {
    method: 'post',
    url: currentURL,
    payload: apiRoute.data
  }
}
