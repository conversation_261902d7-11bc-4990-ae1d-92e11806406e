import { getInstanceType } from '.'
import joinRequests from './joinRequests'
import changeMonth from './changeMonth'
import DynamicServiceUtils from './DynamicServiceUtils'

export default class DynamicDonutCardService {
  static preparePromise(apiRoutes, inputData, rangeDate, http) {
    return prepareRoutes(apiRoutes, inputData).map(
      ({ method, url, payload }) => {
        return http[method](url, payload)
      }
    )
  }
  static parseResponse(apiResponse, { inputData, config }) {
    const [result] = [apiResponse]
      .map((el) => {
        return prepareData(el)
      })
      .filter((el) => {
        return !!(Array.isArray(el?.summary) && !!el?.summary.length)
      })
      .map((el) => {
        return el.summary.map((e) => parseData(e))
      })

    return result ?? []
  }
  static parseStructure(structure) {
    const { type, cardOptions, chartOptions } = structure

    const { title, contents } = cardOptions

    return {
      title,
      type,
      apiRoutes: contents,
      chartOptions
    }
  }
}
/** Methods promise */
const prepareRoutes = (apiRoutes, inputData, rangeDate) => {
  if (!Array.isArray(apiRoutes)) {
    return []
  }

  return apiRoutes.map(({ apiRoute }) => {
    if ('urlParams' in apiRoute) {
      apiRoute = DynamicServiceUtils.getUrlParams({ apiRoute, inputData })
    } else {
      apiRoute = DynamicServiceUtils.replaceInstance({ apiRoute, inputData })
    }

    let params = {}

    if (apiRoute?.params) {
      params = {
        ...apiRoute.params,
        id: apiRoute.params?.id ?? inputData.id,
        initial_date: inputData.initial_date,
        final_date: inputData.final_date
      }
    }

    return {
      method: apiRoute.method,
      url: apiRoute.url,
      payload: params
    }
  })
}
/** Methods response */
const prepareData = ({ data }) => {
  const { summary } = data ?? {}
  return { summary }
}
const parseData = (el, config) => {
  return {
    name: el.entity_name,
    y: el.data.value
  }
}
