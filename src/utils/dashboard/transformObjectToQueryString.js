export default function transformObjectToQueryString(obj, prefix) {
  const strToQuery = []
  let property
  for (property in obj) {
    const hasOwnProperty = Object.prototype.hasOwnProperty.call(obj, property)
    if (hasOwnProperty) {
      const valueMounted = prefix ? prefix + '[' + property + ']' : property
      const value = obj[property]
      strToQuery.push(
        value !== null && typeof value === 'object'
          ? transformObjectToQueryString(value, valueMounted)
          : encodeURIComponent(valueMounted) + '=' + encodeURIComponent(value)
      )
    }
  }
  return strToQuery.join('&')
}
