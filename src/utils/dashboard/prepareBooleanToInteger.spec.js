import prepareBooleanToInteger from './prepareBooleanToInteger'

describe('prepareBooleanToInteger', () => {
  let mockData

  beforeEach(() => {
    jest.clearAllMocks()
    jest.resetModules()
  })

  test('should convert boolean value to integer in an array', () => {
    mockData = { data: [{ value: true }, { value: false }, { value: true }] }
    const result = prepareBooleanToInteger(mockData)

    expect(result.data).toEqual([{ value: 1 }, { value: 0 }, { value: 1 }])
  })

  test('should convert boolean value to integer in an object', () => {
    mockData = { data: { value: true } }
    const result = prepareBooleanToInteger(mockData)

    expect(result.data).toEqual({ value: 1 })
  })

  test('should handle empty object data', () => {
    mockData = { data: {} }
    const result = prepareBooleanToInteger(mockData)

    expect(result.data).toEqual({ value: 0 })
  })
})
