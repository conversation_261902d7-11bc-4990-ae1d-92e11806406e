import { getInstanceType } from '.'
import joinRequests from './joinRequests'
import changeMonth from './changeMonth'

export default class DynamicChartNewService {
  static preparePromise(apiRoutes, inputData, rangeDate, http) {
    return prepareRoutes(apiRoutes, inputData, rangeDate).map(
      ({ method, url, payload }) => {
        return http[method](url, payload)
      }
    )
  }
  static parseResponse(apiResponse, { inputData }) {
    const data = apiResponse.data

    return data
  }
  static parseStructure(structure) {
    const { apiRoutes, chartOptions } = structure

    return {
      type: 'dynamic',
      apiRoutes,
      chartOptions
    }
    /**
     export const parseStructureDynamicChartNew = (
       structure: IDashboardTabWidgetY['dataStructure']
     ): IStructureDynamicChartNew => {
       const { apiRoutes, chartOptions } = structure 
     
       return {
         type: 'dynamic',
         apiRoutes,
         chartOptions
       }
      }
    */
  }
  static parsePayload(payload) {
    return {
      final_date: payload.final_date,
      initial_date: payload.initial_date,
      id: payload.id,
      empresa_id: payload.empresa_id,
      date: payload.date
      //   instance: payload.instance,
      //   changeType: payload.changeType,
      //   codigo: payload.codigo,
      //   route: payload.route,
    }
  }
}

/** Methods promise */
export const prepareRoutes = (apiRoutes, inputData, rangeDate) => {
  const allRoutes = []

  if (Array.isArray(apiRoutes)) {
    apiRoutes.forEach((apiRoute) => {
      const currentInputData = {
        ...inputData,
        route: formatUrl(inputData, apiRoute, rangeDate)
      }

      if ('url' in apiRoute && !!apiRoute.url) {
        allRoutes.push(
          joinRequests(
            preparePayload({ ...currentInputData }, { ...apiRoute }, rangeDate)
          )
        )
      } else {
        Object.keys(route).forEach((key) => {
          allRoutes.push(
            joinRequests(
              preparePayload(
                { ...currentInputData },
                { ...route[key] },
                rangeDate
              )
            )
          )
        })
      }
    })
  }

  return allRoutes
}
export const preparePayload = (inputData, apiRoute, rangeDate) => {
  const type = rangeDate && rangeDate.check ? 'range' : 'main'

  let currentPayload = {}

  if (apiRoute && apiRoute[type] && inputData) {
    if (typeof apiRoute[type] === 'string') {
      currentPayload = {
        route: apiRoute.url,
        data: formatDateRange(inputData, rangeDate)
      }
    } else {
      currentPayload = {
        route: apiRoute.url,
        data: formatDateRange(inputData, rangeDate),
        params: apiRoute[type].params || {},
        method: apiRoute[type].method
      }
    }
  } else {
    currentPayload = {
      route: apiRoute.url,
      data: formatDateRange(inputData, rangeDate),
      params: apiRoute.params,
      method: apiRoute.method
    }
  }

  if ('params' in currentPayload) {
    currentPayload = mergeDataParams(currentPayload)
    return currentPayload
  }

  return currentPayload.data
}
export const mergeDataParams = ({ data, params, route, method }) => {
  const ruleMonth = /([\d]{0,4})-([\d]{0,2})-([\d]{0,2})$/

  const result = {
    route,
    method,
    data: {}
  }

  const dates = changeMonth(data.date)

  if (data?.changeType === 'internal') {
    if (params?.initial_date && typeof params.initial_date === 'object') {
      params.initial_date = new Date(params.initial_date).getTime() / 1000
    }

    if (params?.final_date && typeof params.final_date === 'object') {
      params.final_date = new Date(params.final_date).getTime() / 1000
    }
  } else if (params) {
    params.initial_date = dates.firstDayTime
    params.final_date = dates.lastDayTime
  }

  if (data?.initial_date && ruleMonth.test(data.initial_date)) {
    result.data.initial_date = dates.firstDayTime
  }
  if (data?.initial_date && ruleMonth.test(data.final_date)) {
    result.data.final_date = dates.lastDayTime
  }

  if (data && params) {
    Object.keys(params).forEach((key) => {
      const value =
        key in data && params[key] === null ? data[key] : params[key]

      if (value === null) {
        return
      }

      result.data[key] = value
    })
  }

  return result
}
export const formatUrl = (inputData, apiRoute, rangeDate) => {
  const type = rangeDate && rangeDate.check ? 'range' : 'main'

  const instance = getInstanceType(inputData)

  if (apiRoute && apiRoute[type] && inputData) {
    if (typeof apiRoute[type] === 'string') {
      return apiRoute[type].replace('{instance}', instance)
    } else {
      return apiRoute[type].url.replace('{instance}', instance)
    }
  }

  return apiRoute?.url?.replace('{instance}', instance) || ''
}
export const formatDateRange = (originalPayload, rangeDate) => {
  const finalPayload = { ...originalPayload }
  const startMonth = `${finalPayload.date}-01`.replace(/-/g, '/')

  if (rangeDate && Array.isArray(rangeDate.date)) {
    return {
      ...finalPayload,
      initial_date: formatMoment(rangeDate.date[0]),
      final_date: formatMoment(rangeDate.date[1])
    }
  }
  return {
    ...finalPayload,
    initial_date: formatMoment(startMonth, 'startOf'),
    final_date: formatMoment(startMonth, 'endOf')
  }
}
export const formatMoment = (date, type = 'default') => {
  const selectedMonth = new Date(date)
  const today = new Date()

  let formatted = new Date(date)

  if (type === 'startOf') {
    formatted = new Date(
      selectedMonth.getFullYear(),
      selectedMonth.getMonth(),
      1
    )
  }
  if (type === 'endOf') {
    formatted = new Date(
      selectedMonth.getFullYear(),
      selectedMonth.getMonth() + 1,
      0
    )
  }

  const formattedDate = formatted > today ? today : formatted

  return formattedDate.toISOString().substr(0, 10)
}

/** Methods response */

/** ready response */
