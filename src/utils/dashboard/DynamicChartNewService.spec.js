import DynamicChartNewService, {
  prepareRoutes,
  preparePayload,
  mergeDataParams,
  formatUrl,
  formatDateRange,
  formatMoment
} from './DynamicChartNewService'
import joinRequests from './joinRequests'
import changeMonth from './changeMonth'
import { getInstanceType } from '.'

jest.mock('./joinRequests')
jest.mock('./changeMonth')
jest.mock('.')

changeMonth.mockReturnValue({
  firstDayTime: '2025-01-01',
  lastDayTime: '2025-01-31'
})

joinRequests.mockImplementation((data) => ({
  method: 'get',
  url: '/test',
  payload: data
}))

describe('DynamicChartNewService', () => {
  let httpMock

  beforeEach(() => {
    jest.clearAllMocks()
    jest.resetModules()
    httpMock = {
      get: jest.fn(),
      post: jest.fn()
    }
  })

  describe('preparePromise', () => {
    it('should call http methods correctly based on apiRoutes', () => {
      const apiRoutes = [{ method: 'get', url: '/test', payload: {} }]
      const inputData = {}
      const rangeDate = {}

      const result = DynamicChartNewService.preparePromise(
        apiRoutes,
        inputData,
        rangeDate,
        httpMock
      )
      expect(httpMock.get).toHaveBeenCalledWith('/test', {
        data: {
          final_date: '2025-01-31',
          initial_date: '2025-01-01'
        },
        method: 'get',
        route: '/test'
      })
      expect(result).toHaveLength(1)
    })
  })

  describe('parseResponse', () => {
    it('should return apiResponse data', () => {
      const apiResponse = { data: { key: 'value' } }
      const result = DynamicChartNewService.parseResponse(apiResponse, {
        inputData: {}
      })
      expect(result).toEqual(apiResponse.data)
    })
  })

  describe('parseStructure', () => {
    it('should return parsed structure', () => {
      const structure = {
        apiRoutes: ['route1'],
        chartOptions: { option: 'value' }
      }
      const result = DynamicChartNewService.parseStructure(structure)
      expect(result).toEqual({
        type: 'dynamic',
        apiRoutes: ['route1'],
        chartOptions: { option: 'value' }
      })
    })
  })

  describe('parsePayload', () => {
    it('should return correct payload structure', () => {
      const payload = {
        final_date: '2025-01-01',
        initial_date: '2025-01-31',
        id: 1,
        empresa_id: 2,
        date: '2025-01'
      }
      const result = DynamicChartNewService.parsePayload(payload)
      expect(result).toEqual(payload)
    })
  })
})

describe('prepareRoutes', () => {
  it('should process apiRoutes correctly', () => {
    joinRequests.mockImplementation((data) => data)
    const apiRoutes = [{ url: '/route' }]
    const inputData = { some: 'data' }
    const rangeDate = {}

    const result = prepareRoutes(apiRoutes, inputData, rangeDate)
    expect(result).toHaveLength(1)
  })

  it('should process routes when apiRoute.url is missing', () => {
    joinRequests.mockImplementation((data) => data)
    const apiRoutes = [{ noUrl: true }]
    const inputData = { some: 'data' }
    const rangeDate = {}
    const route = { key1: { url: '/test-route' } }

    Object.defineProperty(global, 'route', {
      value: route,
      writable: true
    })

    const result = prepareRoutes(apiRoutes, inputData, rangeDate)
    expect(result).toHaveLength(1)
    expect(result[0]).toMatchObject({ route: '/test-route' })
  })
})

describe('preparePayload', () => {
  it('should return formatted payload', () => {
    const apiRoute = {
      url: '/test',
      main: { method: 'get', params: {} }
    }

    const inputData = { date: '2025-01' }
    const rangeDate = { check: false }

    const result = preparePayload(inputData, apiRoute, rangeDate)
    expect(result).toMatchObject({ route: '/test', method: 'get' })
  })

  it('should handle string type apiRoute[type]', () => {
    const apiRoute = { url: '/test', main: 'stringType' }
    const inputData = { date: '2025-01' }
    const rangeDate = { check: false }

    const result = preparePayload(inputData, apiRoute, rangeDate)

    expect(result).toMatchObject({
      date: '2025-01',
      initial_date: '2025-01-01',
      final_date: '2025-01-31'
    })
  })
})

describe('mergeDataParams', () => {
  it('should merge data and params correctly', () => {
    changeMonth.mockReturnValue({
      firstDayTime: '2025-01-01',
      lastDayTime: '2025-01-31'
    })
    const payload = {
      data: { changeType: 'internal' },
      params: { initial_date: new Date(), final_date: new Date() },
      route: '/test',
      method: 'get'
    }
    const result = mergeDataParams(payload)
    expect(result.data.initial_date).toBeGreaterThan(0)
    expect(result.data.final_date).toBeGreaterThan(0)
  })

  it('should skip assigning null values to result.data', () => {
    const payload = {
      data: { key1: 'value1' },
      params: { key1: null, key2: 'value2' },
      route: '/test',
      method: 'get'
    }
    const result = mergeDataParams(payload)
    expect(result.data).not.toHaveProperty('value1')
    expect(result.data).toHaveProperty('key2', 'value2')
  })

  it('should return early if value is null', () => {
    const payload = {
      data: { key1: null, key2: 'value2' }, // key1 é explicitamente null
      params: { key1: null, key2: 'value2' },
      route: '/test',
      method: 'get'
    }
    const result = mergeDataParams(payload)
    expect(result.data).not.toHaveProperty('key1')
    expect(result.data.key2).toBe('value2')
  })
})

describe('formatUrl', () => {
  it('should format url correctly', () => {
    getInstanceType.mockReturnValue('instanceType')
    const apiRoute = { main: { url: '/test/{instance}' } }
    const inputData = {}
    const rangeDate = {}

    const result = formatUrl(inputData, apiRoute, rangeDate)
    expect(result).toBe('/test/instanceType')
  })

  it('should replace {instance} in string type apiRoute[type]', () => {
    getInstanceType.mockReturnValue('instanceType')
    const apiRoute = { main: '/test/{instance}' }
    const inputData = {}
    const rangeDate = {}

    const result = formatUrl(inputData, apiRoute, rangeDate)
    expect(result).toBe('/test/instanceType')
  })
  it('should set type as range when rangeDate.check is true', () => {
    const apiRoute = {
      range: { url: '/test-range' },
      main: { url: '/test-main' }
    }
    const inputData = {}
    const rangeDate = { check: true }
    const result = formatUrl(inputData, apiRoute, rangeDate)
    expect(result).toBe('/test-range')
  })

  it('should set type as main when rangeDate is undefined or check is false', () => {
    const apiRoute = {
      range: { url: '/test-range' },
      main: { url: '/test-main' }
    }
    const inputData = {}
    const rangeDate = { check: false }
    const result = formatUrl(inputData, apiRoute, rangeDate)
    expect(result).toBe('/test-main')
  })
})

describe('formatDateRange', () => {
  it('should return formatted date range when rangeDate contains an array', () => {
    const originalPayload = { someKey: 'someValue' }
    const rangeDate = { date: ['2025-01-01', '2025-01-31'] }

    const result = formatDateRange(originalPayload, rangeDate)
    expect(result.initial_date).toBe('2025-01-01')
    expect(result.final_date).toBe('2025-01-31')
  })

  it('should set type as range when rangeDate.check is true in preparePayload', () => {
    const rangeDate = { check: true }
    const inputData = {}
    const apiRoute = {}
    const result = preparePayload(inputData, apiRoute, rangeDate)
    expect(result.type).toBeUndefined() // 'type' is not a returned key but should be 'range' internally
  })

  it('should set type as main when rangeDate is undefined or check is false in preparePayload', () => {
    const rangeDate = { check: false }
    const inputData = {}
    const apiRoute = {}
    const result = preparePayload(inputData, apiRoute, rangeDate)
    expect(result.type).toBeUndefined() // 'type' is not a returned key but should be 'main' internally
  })
})

describe('preparePayload', () => {
  it('should set params to apiRoute[type].params when defined', () => {
    const apiRoute = { main: { params: { key: 'value' } } }
    const inputData = {}
    const rangeDate = { check: false }

    const result = preparePayload(inputData, apiRoute, rangeDate)

    expect(result).toEqual({
      route: undefined,
      method: undefined,
      data: {
        initial_date: '2025-01-01',
        final_date: '2025-01-31',
        key: 'value'
      }
    })
  })

  it('should set params to an empty object when apiRoute[type].params is undefined', () => {
    const apiRoute = { main: {} }
    const inputData = {}
    const rangeDate = { check: false }
    const result = preparePayload(inputData, apiRoute, rangeDate)
    expect(result.params).toBeUndefined()
  })
})

describe('formatMoment', () => {
  it('should return today if formatted date is in the future', () => {
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 10)

    const result = formatMoment(futureDate.toISOString())
    const today = new Date().toISOString().substr(0, 10)

    expect(result).toBe(today)
  })

  it('should return the formatted date if it is not in the future', () => {
    const pastDate = '2024-01-01'
    const result = formatMoment(pastDate)
    expect(result).toBe(pastDate)
  })
})
