import DynamicChartGaugeService, {
  formatDateRange,
  formatMoment,
  formatUrl,
  mergeDataParams,
  preparePayload
} from './DynamicChartGaugeService'
import { getInstanceType } from '.'
import joinRequests from './joinRequests'
import changeMonth from './changeMonth'

jest.mock('.', () => ({
  getInstanceType: jest.fn()
}))

jest.mock('./joinRequests', () => jest.fn())
jest.mock('./changeMonth', () => jest.fn())

// Mock para http
const mockHttp = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn()
}

describe('DynamicChartGaugeService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('preparePromise', () => {
    it('should handle array of apiRoutes with "url"', () => {
      const apiRoutes = [
        { url: 'test-url', method: 'get' },
        { url: 'another-url', method: 'post' }
      ]
      const inputData = { someData: 'value', date: '2023-01' } // Inclua "date"
      const rangeDate = { check: true }

      changeMonth.mockReturnValue({
        firstDayTime: '2023-01-01',
        lastDayTime: '2023-01-31'
      })

      joinRequests.mockReturnValue({
        method: 'get',
        url: 'mocked-url',
        payload: {}
      })

      const result = DynamicChartGaugeService.preparePromise(
        apiRoutes,
        inputData,
        rangeDate,
        mockHttp
      )

      expect(result).toHaveLength(apiRoutes.length)
      expect(mockHttp.get).toHaveBeenCalled()
    })

    it('should handle apiRoutes with nested "route"', () => {
      const apiRoutes = [
        {
          route: {
            key1: { url: 'test-url-1', method: 'get' },
            key2: { url: 'test-url-2', method: 'post' }
          }
        }
      ]
      const inputData = { someData: 'value', date: '2023-01' }
      const rangeDate = { check: true }

      changeMonth.mockReturnValue({
        firstDayTime: '2023-01-01',
        lastDayTime: '2023-01-31'
      })

      joinRequests
        .mockReturnValueOnce({
          method: 'get',
          url: 'test-url-1',
          payload: {}
        })
        .mockReturnValueOnce({
          method: 'post',
          url: 'test-url-2',
          payload: {}
        })

      const result = DynamicChartGaugeService.preparePromise(
        apiRoutes,
        inputData,
        rangeDate,
        mockHttp
      )

      expect(result).toHaveLength(2)

      expect(mockHttp.get).toHaveBeenCalledWith('test-url-1', {})
      expect(mockHttp.post).toHaveBeenCalledWith('test-url-2', {})
    })
  })

  describe('parseResponse', () => {
    it('should return data from apiResponse', () => {
      const apiResponse = { data: { key: 'value' } }
      const inputData = { someInput: 'value' }

      const result = DynamicChartGaugeService.parseResponse(apiResponse, {
        inputData
      })

      expect(result).toEqual(apiResponse.data)
    })
  })

  describe('parseStructure', () => {
    it('should parse structure and return apiRoutes and chartOptions', () => {
      const structure = {
        apiRoutes: [{ url: 'test' }],
        chartOptions: { someOption: 'value' }
      }

      const result = DynamicChartGaugeService.parseStructure(structure)

      expect(result).toEqual({
        apiRoutes: structure.apiRoutes,
        chartOptions: structure.chartOptions
      })
    })
  })
})

describe('Helper Functions', () => {
  describe('mergeDataParams', () => {
    it('should merge data and params correctly', () => {
      changeMonth.mockReturnValue({
        today: 1736305200,
        firstDayTime: 1735700460,
        lastDayTime: 1738378859
      })

      const data = {
        date: '2023-01',
        initial_date: '2023-01-01',
        final_date: '2023-01-31'
      }

      const params = {
        initial_date: null,
        final_date: null
      }

      const route = 'test-route'
      const method = 'post'

      const result = mergeDataParams({ data, params, route, method })

      expect(result.data.initial_date).toBe(1735700460)
      expect(result.data.final_date).toBe(1738378859)
      expect(result.route).toBe('test-route')
      expect(result.method).toBe('post')
    })

    it('should merge data and params correctly when changeType is "internal"', () => {
      // Mock de changeMonth
      changeMonth.mockReturnValue({
        today: 1736305200,
        firstDayTime: 1735700460,
        lastDayTime: 1738378859
      })

      const data = {
        date: '2023-01',
        changeType: 'internal'
      }

      const params = {
        initial_date: new Date('2023-01-01'),
        final_date: new Date('2023-01-31')
      }

      const route = 'test-route'
      const method = 'post'

      const result = mergeDataParams({ data, params, route, method })

      expect(params.initial_date).toBe(1672531200) // Timestamp de '2023-01-01'
      expect(params.final_date).toBe(1675123200) // Timestamp de '2023-01-31'

      expect(result.data.initial_date).toBe(1672531200)
      expect(result.data.final_date).toBe(1675123200)
      expect(result.route).toBe('test-route')
      expect(result.method).toBe('post')
    })

    it('should skip adding values to result.data when value is null', () => {
      // Mock de changeMonth
      changeMonth.mockReturnValue({
        today: 1736305200,
        firstDayTime: 1735700460,
        lastDayTime: 1738378859
      })

      const data = {
        date: '2023-01',
        changeType: 'external'
      }

      const params = {
        initial_date: null,
        final_date: '2023-01-31',
        someKey: null
      }

      const route = 'test-route'
      const method = 'post'

      const result = mergeDataParams({ data, params, route, method })

      expect(result.data).not.toHaveProperty('1735700460')
      expect(result.data).not.toHaveProperty('someKey')

      expect(result.data.final_date).toBe(1738378859)

      expect(result.route).toBe('test-route')
      expect(result.method).toBe('post')
    })

    it('should use data[key] when key exists in data and params[key] is null', () => {
      changeMonth.mockReturnValue({
        today: 1736305200,
        firstDayTime: 1735700460,
        lastDayTime: 1738378859
      })

      const data = {
        date: '2023-01',
        changeType: 'external',
        customKey: 'customValue'
      }

      const params = {
        customKey: null,
        anotherKey: 'shouldRemain'
      }

      const route = 'test-route'
      const method = 'post'

      const result = mergeDataParams({ data, params, route, method })

      expect(result.data.customKey).toBe('customValue')
      expect(result.data.anotherKey).toBe('shouldRemain')
      expect(result.route).toBe('test-route')
      expect(result.method).toBe('post')
    })
  })

  describe('formatUrl', () => {
    it('should format URL correctly', () => {
      getInstanceType.mockReturnValue('mock-instance')
      const inputData = {}
      const apiRoute = {
        range: { url: 'test-{instance}' },
        main: { url: 'main-{instance}' }
      }
      const rangeDate = { check: true }

      const result = formatUrl(inputData, apiRoute, rangeDate)

      expect(result).toBe('test-mock-instance')
    })

    it('should format the URL when apiRoute[type] is a string', () => {
      const inputData = {}
      const apiRoute = {
        main: 'test-{instance}'
      }
      const rangeDate = { check: false }
      const instance = 'mock-instance'

      // Mock de getInstanceType
      getInstanceType.mockReturnValue(instance)

      const result = formatUrl(inputData, apiRoute, rangeDate)

      expect(result).toBe('test-mock-instance')
    })
  })

  describe('preparePayload', () => {
    it('should prepare payload with merged data params', () => {
      changeMonth.mockReturnValue({
        firstDayTime: '2023-01-01',
        lastDayTime: '2023-01-31'
      })
      const inputData = { date: '2023-01' }
      const apiRoute = {
        url: 'test-url',
        main: { method: 'get', params: { key: 'value' } }
      }
      const rangeDate = {}

      const result = preparePayload(inputData, apiRoute, rangeDate)

      expect(result.route).toBe('test-url')
      expect(result.method).toBe('get')
      expect(result.data).toHaveProperty('initial_date')
    })

    it('should prepare payload when apiRoute[type] is a string', () => {
      changeMonth.mockReturnValue({
        firstDayTime: '2023-01-01',
        lastDayTime: '2023-01-31'
      })

      const inputData = { date: '2023-01' }
      const apiRoute = {
        url: 'test-url',
        main: 'test-{instance}'
      }
      const rangeDate = { check: false }

      getInstanceType.mockReturnValue('mock-instance')

      const result = preparePayload(inputData, apiRoute, rangeDate)

      expect(result).toHaveProperty('date', '2023-01')
      expect(result).toHaveProperty('initial_date', '2023-01-01')
      expect(result).toHaveProperty('final_date', '2023-01-31')
    })

    it('should fallback to an empty object when apiRoute[type].params is undefined', () => {
      // Mock de changeMonth
      changeMonth.mockReturnValue({
        firstDayTime: '2023-01-01',
        lastDayTime: '2023-01-31'
      })

      const inputData = { date: '2023-01' }
      const apiRoute = {
        url: 'test-url',
        main: { method: 'get' }
      }
      const rangeDate = {}

      const result = preparePayload(inputData, apiRoute, rangeDate)

      expect(result.route).toBe('test-url')
      expect(result.method).toBe('get')
      expect(result.params).toBeUndefined()
      expect(result.data).toHaveProperty('initial_date', '2023-01-01')
      expect(result.data).toHaveProperty('final_date', '2023-01-31')
    })
  })

  describe('formatDateRange function', () => {
    it('should return formatted date range with array of dates', () => {
      const originalPayload = { date: '2023-01' }
      const rangeDate = { date: ['2023-01-01', '2023-01-31'] }
      const expected = {
        date: '2023-01',
        initial_date: formatMoment('2023-01-01'),
        final_date: formatMoment('2023-01-31')
      }
      expect(formatDateRange(originalPayload, rangeDate)).toEqual(expected)
    })
    it('should return formatted date range without array of dates', () => {
      const originalPayload = { date: '2024-01' }
      const rangeDate = {}
      expect(formatDateRange(originalPayload, rangeDate)).toEqual({
        date: '2024-01',
        final_date: '2024-01-31',
        initial_date: '2024-01-01'
      })
    })
    it('should return a value when originalPayload is an empty object', () => {
      const originalPayload = {}
      const rangeDate = { date: ['2023-01-01', '2023-01-31'] }
      const result = formatDateRange(originalPayload, rangeDate)
      expect(result).toBeDefined()
    })
    it('should throw error with invalid rangeDate', () => {
      const originalPayload = { date: '2023-01' }
      const rangeDate = 'invalid'
      const result = formatDateRange(originalPayload)
      expect(result).toEqual({
        date: '2023-01',
        final_date: '2023-01-31',
        initial_date: '2023-01-01'
      })
    })
    it('should throw error with invalid rangeDate array', () => {
      const originalPayload = { date: '2023-01' }
      const rangeDate = { date: ['2023-01-01'] }
      expect(() => formatDateRange(originalPayload, rangeDate)).toThrow(
        'Invalid time value'
      )
    })
  })

  describe('formatMoment function', () => {
    it('should return formatted date in default behavior', () => {
      const date = '2023-01-15'
      const result = formatMoment(date)
      expect(result).toBe('2023-01-15')
    })
    it('should return first day of month when type is startOf', () => {
      const date = '2023-01-15'
      const result = formatMoment(date, 'startOf')
      expect(result).toBe('2023-01-01')
    })
    it('should return last day of month when type is endOf', () => {
      const date = '2023-01-15'
      const result = formatMoment(date, 'endOf')
      expect(result).toBe('2023-01-31')
    })
    it('should return error when date is invalid', () => {
      const date = 'invalid-date'
      expect(() => formatMoment(date)).toThrowError()
    })
    it("should return today's date when date is in the future", () => {
      const futureDate = '2050-01-01'
      const today = new Date().toISOString().substr(0, 10)
      const result = formatMoment(futureDate)
      expect(result).toBe(today)
    })
  })
})
