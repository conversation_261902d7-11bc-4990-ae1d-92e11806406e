import DynamicServiceUtils from './DynamicServiceUtils'
import prepareBooleanToInteger from './prepareBooleanToInteger'

export default class DynamicListCardService {
  static preparePromise(apiRoute, inputData, http) {
    if ('urlParams' in apiRoute) {
      apiRoute = DynamicServiceUtils.getUrlParams({ apiRoute, inputData })
    } else {
      apiRoute = DynamicServiceUtils.replaceInstance({ apiRoute, inputData })
    }

    let params = {}

    if (apiRoute.params) {
      params = {
        ...apiRoute.params,
        id: apiRoute.params.id || inputData.id,
        initial_date: inputData.initial_date,
        final_date: inputData.final_date
      }
    }

    return http[apiRoute.method](apiRoute.url, params)
  }
  static parseResponse(apiResponse, { config, toInteger }) {
    const [result] = [apiResponse]
      .map((el) => {
        return parseBooleanToInteger(el, { toInteger })
      })
      .filter((el) => {
        return Array.isArray(el?.summary) && el?.summary.length
      })
      .map((el) => {
        return el.summary.map((e) => parseData(e, { config }))
      })

    return result ?? []
  }
  static parseStructure(structure) {
    const { cardOptions } = structure

    const { title, type, behavior, contents, parseValues = null } = cardOptions

    return {
      title,
      type,
      behavior,
      parseValues,
      routes: contents.map((el) => ({
        config: el.content,
        apiRoute: el.apiRoute
      }))
    }
  }
}

const parseData = (el, { config }) => {
  return {
    fieldLabel: config.textLabel ? config.textLabel : el[config.fieldLabel],

    fieldValue: config.textValue
      ? config.textValue
      : el.data[config.fieldValue],

    descriptionValue: config.descriptionLabel
      ? config.descriptionLabel
      : el.data[config.descriptionValue],

    unit: config.unit,

    decimal: config?.decimal !== undefined ? config?.decimal : 2,

    entity: el.entity || '',

    entity_name: el.entity_name || '',

    property: el.property || '',

    data: {
      value: el.data.value || '',
      timestamp: el.data.timestamp
    }
  }
}

const parseBooleanToInteger = ({ data }, { toInteger }) => {
  const { series = [], summary = [] } = data ?? {}

  if (toInteger === 'booleanToInteger') {
    return {
      series: series?.map(prepareBooleanToInteger),
      summary: summary?.map(prepareBooleanToInteger)
    }
  }
  return {
    series: series,
    summary: summary
  }
}
