import DynamicDemandService, {
  formatValue,
  formatMaxData,
  formatLastData,
  formatDateRange,
  formatMoment
} from '@/utils/dashboard/DynamicDemandService'

import DateUtilsService from '@/utils/dashboard/DateUtilsService'

jest.mock('@/utils/dashboard/DateUtilsService')

const mockHttp = {
  post: jest.fn()
}

const mockInputData = { timezone: 'GMT-3' }
const mockRangeDate = { date: ['2023-01-01', '2023-01-31'] }
const mockApiResponse = [
  { data: { exceeded: 10, max: 100, used: 90 } },
  { data: { date: '2023-01-15', value: 50 } }
]
const mockRoutes = [
  {
    config: {
      id: '1',
      title: 'Max Title',
      description: 'max',
      value: 'max',
      unit: 'kW'
    }
  },
  {
    config: {
      id: '2',
      title: 'Last Title',
      description: 'invalid_key', // This will cause fallback
      value: 'invalid_key', // This will cause fallback
      unit: '' // Default fallback
    }
  }
]

const mockFooter = {
  title: '',
  fields: {
    titleField: 'invalid_field', // This will cause fallback
    titleFieldSufix: '', // Default fallback
    firstValue: 'invalid_field', // This will cause fallback
    secondValue: 'invalid_field', // This will cause fallback
    secondValueSufix: '%'
  }
}

describe('DynamicDemandService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('preparePromise calls http.post with the correct URL and payload', async () => {
    const route = { apiRoute: '/api/{instance}/data' }
    mockHttp.post.mockResolvedValue({})

    await DynamicDemandService.preparePromise(
      route,
      mockInputData,
      mockRangeDate,
      mockHttp
    )

    expect(mockHttp.post).toHaveBeenCalledWith(
      '/api/company/data',
      expect.objectContaining({
        initial_date: '2023-01-01',
        final_date: '2023-01-31'
      })
    )
  })

  test('parseResponse formats API responses correctly', () => {
    const result = DynamicDemandService.parseResponse(
      mockInputData,
      mockApiResponse,
      {
        routes: mockRoutes,
        footer: mockFooter
      }
    )

    expect(result.values).toHaveLength(2)
    expect(result.values[0]).toEqual(
      expect.objectContaining({
        id: '1',
        title: 'Max Title',
        value: 100,
        description: 100,
        unit: 'kW'
      })
    )

    expect(result.values[1]).toEqual(
      expect.objectContaining({
        id: '2',
        title: 'Last Title',
        description: undefined,
        value: undefined,
        unit: '',
        decimal: undefined
      })
    )

    expect(result.bar).toEqual(
      expect.objectContaining({
        used: expect.objectContaining({
          prefix: 'Usado',
          value: '0,00',
          sufix: ''
        }),
        contract: expect.objectContaining({
          prefix: '0',
          value: '0,00',
          sufix: '%'
        }),
        contractValue: 0,
        exceededValue: 0
      })
    )
  })

  test('parseStructure generates routes and config correctly', () => {
    const structure = {
      cardOptions: {
        title: 'Card Title',
        type: 'Card Type',
        footer: 'Card Footer',
        contents: [
          {
            id: '1',
            last: false,
            unit: 'kW',
            title: 'Max Title',
            value: 'max',
            description: 'max',
            apiRoute: '/api/data/max'
          }
        ]
      }
    }

    const result = DynamicDemandService.parseStructure(structure)

    expect(result.routes).toHaveLength(1)
    expect(result.routes[0].config).toEqual(
      expect.objectContaining({
        id: '1',
        title: 'Max Title'
      })
    )
  })

  test('formatValue formats numbers correctly', () => {
    expect(formatValue('12345.678')).toBe('12.345,68')
    expect(formatValue(null)).toBe('')
  })

  test('formatMaxData returns formatted data', () => {
    DateUtilsService.getDateFormat.mockReturnValue('15/01/2023')

    const result = formatMaxData(mockApiResponse[0], {
      inputData: mockInputData
    })

    expect(result).toEqual(
      expect.objectContaining({
        contract: 0,
        exceeded: 10,
        max: 100,
        used: 90,
        percentage_exceeded: 0,
        time_max: null
      })
    )
  })
  test('formatDateRange returns correct range for given dates', () => {
    const result = formatDateRange(mockInputData, mockRangeDate)

    expect(result).toEqual(
      expect.objectContaining({
        initial_date: '2023-01-01',
        final_date: '2023-01-31'
      })
    )

    const noRangeDate = { date: '2023-01' }
    const resultNoRange = formatDateRange(mockInputData, noRangeDate)

    expect(resultNoRange).toEqual(
      expect.objectContaining({
        final_date: '2001-01-31',
        initial_date: '2001-01-01',
        timezone: 'GMT-3'
      })
    )
  })
  test('formatLastData returns formatted data', () => {
    DateUtilsService.getDateFormat.mockReturnValue('15/01/2023')

    const result = formatLastData(mockApiResponse[1], {
      inputData: mockInputData
    })

    expect(result).toEqual(
      expect.objectContaining({
        date: '15/01/2023',
        value: 50
      })
    )
  })
  test('formatMoment formats dates correctly', () => {
    const today = new Date()
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 15)
      .toISOString()
      .substr(0, 10)
    const resultStart = formatMoment('2023-01-15', 'startOf')
    const resultEnd = formatMoment('2023-01-15', 'endOf')

    expect(resultStart).toBe('2023-01-01')
    expect(resultEnd).toBe('2023-01-31')

    const futureDate = formatMoment(nextMonth)
    expect(futureDate).toBe(today.toISOString().substr(0, 10))
  })
})
