import DynamicListCardService from './DynamicListCardService'
import DynamicServiceUtils from './DynamicServiceUtils'

jest.mock('./DynamicServiceUtils', () => ({
  getUrlParams: jest.fn((args) => args.apiRoute),
  replaceInstance: jest.fn(({ apiRoute }) => ({
    ...apiRoute,
    params: apiRoute.params || {} // Garante que params sempre seja um objeto
  }))
}))

describe('DynamicListCardService', () => {
  describe('preparePromise', () => {
    let mockHttp

    beforeEach(() => {
      mockHttp = { get: jest.fn() }
      jest.clearAllMocks()
      jest.resetAllMocks()
    })
    it('should call getUrlParams when apiRoute has urlParams', async () => {
      const apiRoute = { urlParams: true, method: 'get', url: '/api/test' }
      const inputData = {
        id: 1,
        initial_date: '2025-01-01',
        final_date: '2025-01-10'
      }
      const expectedApiRoute = { method: 'get', url: '/api/modified' }
      DynamicServiceUtils.getUrlParams.mockReturnValue(expectedApiRoute)

      await DynamicListCardService.preparePromise(apiRoute, inputData, mockHttp)

      expect(DynamicServiceUtils.getUrlParams).toHaveBeenCalledWith({
        apiRoute,
        inputData
      })
      expect(mockHttp.get).toHaveBeenCalledWith('/api/modified', {})
    })

    it('should call replaceInstance when apiRoute does not have urlParams', async () => {
      const apiRoute = { method: 'get', url: '/api/test' }
      const inputData = { id: 1 }
      const expectedApiRoute = { method: 'get', url: '/api/instance' }
      DynamicServiceUtils.replaceInstance.mockReturnValue(expectedApiRoute)

      await DynamicListCardService.preparePromise(apiRoute, inputData, mockHttp)

      expect(DynamicServiceUtils.replaceInstance).toHaveBeenCalledWith({
        apiRoute,
        inputData
      })
      expect(mockHttp.get).toHaveBeenCalledWith('/api/instance', {})
    })

    it('should include additional params when apiRoute has params', async () => {
      const apiRoute = {
        method: 'get',
        url: '/api/test',
        id: 2
      }
      const inputData = {
        id: 1,
        initial_date: '2025-01-01',
        final_date: '2025-01-10'
      }

      const expectedApiRoute = {
        method: 'get',
        url: '/api/test', // Garante que a URL não seja alterada
        params: { id: 2 } // Mantém os parâmetros originais
      }

      DynamicServiceUtils.replaceInstance.mockReturnValue(expectedApiRoute)

      await DynamicListCardService.preparePromise(apiRoute, inputData, mockHttp)

      expect(mockHttp.get).toHaveBeenCalledWith('/api/test', {
        id: 2,
        initial_date: '2025-01-01',
        final_date: '2025-01-10'
      })
    })

    it('should use inputData.id if apiRoute.params.id is not present', async () => {
      const apiRoute = {
        method: 'get',
        url: '/api/test',
        params: {}
      }
      const inputData = {
        id: 3,
        initial_date: '2025-01-01',
        final_date: '2025-01-10'
      }

      DynamicServiceUtils.replaceInstance.mockReturnValue({
        ...apiRoute,
        params: {}
      })

      await DynamicListCardService.preparePromise(apiRoute, inputData, mockHttp)

      expect(mockHttp.get).toHaveBeenCalledWith('/api/test', {
        id: 3,
        initial_date: '2025-01-01',
        final_date: '2025-01-10'
      })
    })
  })

  describe('parseResponse', () => {
    it('should parse API response correctly', () => {
      const apiResponse = {
        data: { summary: [{ data: { value: 100, timestamp: '2025-01-01' } }] }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result).toEqual([
        {
          fieldLabel: undefined,
          fieldValue: 100,
          descriptionValue: undefined,
          unit: 'kg',
          decimal: 2,
          entity: '',
          entity_name: '',
          property: '',
          data: { value: 100, timestamp: '2025-01-01' }
        }
      ])
    })

    it('should return an empty array when summary is missing', () => {
      const apiResponse = { data: {} }
      const config = { fieldLabel: 'label', fieldValue: 'value' }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result).toEqual([])
    })

    it('should convert boolean values to integers when toInteger is "booleanToInteger"', () => {
      const apiResponse = {
        data: {
          summary: [{ data: { value: true, timestamp: '2025-01-01' } }],
          series: [{ data: { value: false, timestamp: '2025-01-02' } }]
        }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = 'booleanToInteger'

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result).toEqual([
        {
          fieldLabel: undefined,
          fieldValue: 1,
          descriptionValue: undefined,
          unit: 'kg',
          decimal: 2,
          entity: '',
          entity_name: '',
          property: '',
          data: { value: 1, timestamp: '2025-01-01' }
        }
      ])
    })

    it('should use config.textLabel if present', () => {
      const apiResponse = {
        data: { summary: [{ data: { value: 100, timestamp: '2025-01-01' } }] }
      }
      const config = {
        textLabel: 'Custom Label',
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].fieldLabel).toEqual('Custom Label')
    })

    it('should use el[config.fieldLabel] if config.textLabel is not present', () => {
      const apiResponse = {
        data: {
          summary: [
            {
              label: 'Generated Label',
              data: { value: 100, timestamp: '2025-01-01' }
            }
          ]
        }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].fieldLabel).toEqual('Generated Label')
    })

    it('should use config.textValue if present', () => {
      const apiResponse = {
        data: { summary: [{ data: { value: 100, timestamp: '2025-01-01' } }] }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        textValue: 'Custom Value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].fieldValue).toEqual('Custom Value')
    })
    it('should use el.data[config.fieldValue] if config.textValue is not present', () => {
      const apiResponse = {
        data: { summary: [{ data: { value: 200, timestamp: '2025-01-01' } }] }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].fieldValue).toEqual(200)
    })

    it('should use config.descriptionLabel if present', () => {
      const apiResponse = {
        data: {
          summary: [
            {
              data: {
                value: 300,
                description: 'Generated Description',
                timestamp: '2025-01-01'
              }
            }
          ]
        }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionLabel: 'Custom Description',
        descriptionValue: 'description',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].descriptionValue).toEqual('Custom Description')
    })

    it('should use el.data[config.descriptionValue] if config.descriptionLabel is not present', () => {
      const apiResponse = {
        data: {
          summary: [
            {
              data: {
                value: 400,
                description: 'Generated Description',
                timestamp: '2025-01-01'
              }
            }
          ]
        }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'description',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].descriptionValue).toEqual('Generated Description')
    })
    it('should use config.decimal if present', () => {
      const apiResponse = {
        data: { summary: [{ data: { value: 100, timestamp: '2025-01-01' } }] }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 3
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].decimal).toEqual(3)
    })

    it('should default decimal to 2 if config.decimal is not defined', () => {
      const apiResponse = {
        data: { summary: [{ data: { value: 100, timestamp: '2025-01-01' } }] }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg'
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].decimal).toEqual(2)
    })

    it('should use data value and timestamp correctly', () => {
      const apiResponse = {
        data: { summary: [{ data: { value: 500, timestamp: '2025-01-02' } }] }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].data).toEqual({ value: 500, timestamp: '2025-01-02' })
    })

    it('should default data value to empty string if not present', () => {
      const apiResponse = {
        data: { summary: [{ data: { timestamp: '2025-01-02' } }] }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result[0].data).toEqual({ value: '', timestamp: '2025-01-02' })
    })

    it('should handle missing data object gracefully', () => {
      const apiResponse = {}
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result).toEqual([])
    })

    it('should default series and summary to empty arrays if data is missing', () => {
      const apiResponse = { data: null }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result).toEqual([])
    })

    it('should parse response correctly when series and summary exist', () => {
      const apiResponse = {
        data: {
          series: [{ data: { value: 600, timestamp: '2025-01-03' } }],
          summary: [{ data: { value: 700, timestamp: '2025-01-04' } }]
        }
      }
      const config = {
        fieldLabel: 'label',
        fieldValue: 'value',
        descriptionValue: 'desc',
        unit: 'kg',
        decimal: 2
      }
      const toInteger = false

      const result = DynamicListCardService.parseResponse(apiResponse, {
        config,
        toInteger
      })

      expect(result.length).toBeGreaterThan(0)
      expect(result[0].data.value).toEqual(700)
    })
  })

  describe('parseStructure', () => {
    it('should parse structure correctly', () => {
      const structure = {
        cardOptions: {
          title: 'Test Title',
          type: 'chart',
          behavior: 'static',
          contents: [
            {
              content: { unit: 'kg', decimal: 2 },
              apiRoute: '/api/test'
            }
          ]
        }
      }

      const expectedOutput = {
        title: 'Test Title',
        type: 'chart',
        behavior: 'static',
        parseValues: null,
        routes: [
          {
            config: { unit: 'kg', decimal: 2 },
            apiRoute: '/api/test'
          }
        ]
      }

      expect(DynamicListCardService.parseStructure(structure)).toEqual(
        expectedOutput
      )
    })
  })
})
