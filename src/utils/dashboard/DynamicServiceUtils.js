import getValueByString from './getValueByString'

export default class DynamicServiceUtils {
  /** legacy */
  static getUrlParams({ apiRoute, inputData }) {
    Object.keys(apiRoute.urlParams).forEach((key) => {
      if (apiRoute.urlParams[key] && apiRoute.urlParams[key].path) {
        apiRoute.urlParams[key] = getValueByString(
          inputData,
          apiRoute.urlParams[key].path
        )
        if (apiRoute.url) {
          apiRoute.url = apiRoute.url.replace(
            `{${key}}`,
            apiRoute.urlParams[key]
          )
        } else if (apiRoute.apiRoute) {
          apiRoute.apiRoute = apiRoute.apiRoute.replace(
            `{${key}}`,
            apiRoute.urlParams[key]
          )
        }
      }
    })

    return apiRoute
  }

  /** new methods */
  static replaceInstance({ apiRoute, inputData }) {
    apiRoute.url = apiRoute.url.replace('{instance}', inputData.instance)

    return apiRoute
  }
}
