import { Http } from '@/@core/infra/http'

import DynamicDonutCardService from './DynamicDonutCardService'

describe('src/utils/dashboard/DynamicDonutCardService', () => {
  let payload = {}
  let apiRoute = {}
  let apiRoutes = {}
  let inputData = {}
  let rangeDate = null
  let apiResponse = {}

  let http = new Http()
  http.get = jest.fn().mockResolvedValueOnce()

  beforeEach(() => {
    payload = {}

    apiRoute = {
      method: 'get',
      url: 'api/dashboard/{instance}'
    }

    apiRoutes = {}

    inputData = {
      id: 119,
      initial_date: '2025-02-21',
      final_date: '2025-02-21',
      instance: 'company'
    }

    apiResponse = {}

    http.get = jest.fn().mockResolvedValueOnce()
  })

  it('execute function preparePromise with apiRoutes not being of type Array', () => {
    /** with wrong type */
    apiRoutes = {}

    DynamicDonutCardService.preparePromise(
      apiRoutes,
      inputData,
      rangeDate,
      http
    )
    expect(http.get).toHaveBeenCalledTimes(0)

    /** with correct type */
    apiRoutes = []

    DynamicDonutCardService.preparePromise(
      apiRoutes,
      inputData,
      rangeDate,
      http
    )
    expect(http.get).toHaveBeenCalledTimes(0)
  })

  it('execute function preparePromise with apiRoutes being of type Array', () => {
    apiRoutes = [{ apiRoute }]

    DynamicDonutCardService.preparePromise(
      apiRoutes,
      inputData,
      rangeDate,
      http
    )

    expect(http.get).toHaveBeenCalledTimes(1)
    expect(http.get).toHaveBeenCalledWith(apiRoute.url, payload)
  })

  it('validation of keys without urlParams and params without values ​​in the preparePromise method call', () => {
    apiRoutes = [{ apiRoute }]

    http.get = jest.fn().mockResolvedValueOnce()

    DynamicDonutCardService.preparePromise(
      apiRoutes,
      inputData,
      rangeDate,
      http
    )

    expect(http.get).toHaveBeenCalledTimes(1)
    expect(http.get).toHaveBeenCalledWith(apiRoute.url, payload)
  })

  it('validation of keys without urlParams and params with values ​​in the preparePromise method call', () => {
    payload = {
      id: 50,
      final_date: '2025-02-21',
      initial_date: '2025-02-21'
    }

    apiRoute = {
      ...apiRoute,
      params: { id: 50 }
    }

    apiRoutes = [{ apiRoute }]

    http.get = jest.fn().mockResolvedValueOnce()

    DynamicDonutCardService.preparePromise(
      apiRoutes,
      inputData,
      rangeDate,
      http
    )

    expect(http.get).toHaveBeenCalledTimes(1)
    expect(http.get).toHaveBeenCalledWith(apiRoute.url, payload)

    inputData = {
      ...inputData,
      id: 19
    }

    payload = {
      id: 19,
      final_date: '2025-02-21',
      initial_date: '2025-02-21'
    }

    apiRoute = {
      ...apiRoute,
      params: {}
    }

    apiRoutes = [{ apiRoute }]

    http.get = jest.fn().mockResolvedValueOnce()

    DynamicDonutCardService.preparePromise(
      apiRoutes,
      inputData,
      rangeDate,
      http
    )

    expect(http.get).toHaveBeenCalledTimes(1)
    expect(http.get).toHaveBeenCalledWith(apiRoute.url, payload)
  })

  it('validation of keys with urlParams in the preparePromise method call', () => {
    apiRoute = {
      ...apiRoute,
      url: apiRoute.url + '?final_date={final_date}',
      urlParams: {
        final_date: { path: 'final_date' }
      }
    }

    apiRoutes = [{ apiRoute }]

    http.get = jest.fn().mockResolvedValueOnce()

    DynamicDonutCardService.preparePromise(
      apiRoutes,
      inputData,
      rangeDate,
      http
    )

    expect(http.get).toHaveBeenCalledTimes(1)
    expect(http.get).toHaveBeenCalledWith(apiRoute.url, payload)
  })

  it('checks the return of the function parseResponse with values incorrect', () => {
    apiResponse = {}

    const result = DynamicDonutCardService.parseResponse(apiResponse, {
      inputData,
      config: {}
    })

    expect(result).toHaveLength(0)
    expect(result).toEqual([])
  })

  it('checks the return of the function parseResponse', () => {
    apiResponse = {
      data: {
        summary: [
          {
            entity_name: 'entity_name_teste',
            data: { value: 1 }
          }
        ]
      }
    }

    const result = DynamicDonutCardService.parseResponse(apiResponse, {
      inputData,
      config: {}
    })

    expect(result).toHaveLength(1)
    expect(result).toEqual([{ name: 'entity_name_teste', y: 1 }])
  })

  it('check the return of the parse Structure function', () => {
    const structure = {
      type: 'DynamicChartNew',
      cardOptions: {
        title: 'Demanda Ponta',
        contents: []
      },
      chartOptions: {
        chart: 'line'
      }
    }

    const result = DynamicDonutCardService.parseStructure(structure)

    expect(result).toEqual({
      title: 'Demanda Ponta',
      type: 'DynamicChartNew',
      apiRoutes: [],
      chartOptions: {
        chart: 'line'
      }
    })
  })
})
