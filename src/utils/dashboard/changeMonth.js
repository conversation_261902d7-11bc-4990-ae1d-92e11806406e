import dayjs from 'dayjs'

export default function changeMonth(date) {
  const dateContext = date ? `${date}-01` : undefined

  return {
    today: dayjs().startOf('day').unix(),
    firstDayTime: dayjs(dateContext)
      .startOf('month')
      .set('hour', 0)
      .set('minute', 1)
      .set('second', 0)
      .unix(),
    lastDayTime: dayjs(dateContext)
      .endOf('month')
      .set('hour', 0)
      .set('minute', 0)
      .set('second', 59)
      .add(1, 'day')
      .unix()
  }
}
