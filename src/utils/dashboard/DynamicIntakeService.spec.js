import DynamicIntakeService from './DynamicIntakeService'
import { getInstanceType } from '.'
import DateUtilsService from './DateUtilsService'

jest.mock('./DateUtilsService', () => ({
  getDateFormat: jest.fn()
}))

jest.mock('.', () => ({
  getInstanceType: jest.fn()
}))

describe('preparePromise', () => {
  it('should replace {instance} in apiRoute with instance type and call http.post with correct parameters', async () => {
    const mockHttp = { post: jest.fn() }
    const inputData = { key: 'value' }
    const apiRoute = '/api/{instance}/data'
    const expectedInstanceType = 'testInstance'
    getInstanceType.mockReturnValue(expectedInstanceType)

    await DynamicIntakeService.preparePromise(apiRoute, inputData, mockHttp)

    expect(getInstanceType).toHaveBeenCalledWith(inputData)
    expect(mockHttp.post).toHaveBeenCalledWith(
      '/api/testInstance/data',
      inputData
    )
  })
})

describe('parseResponse', () => {
  it('should parse API response correctly', () => {
    const apiResponse = { data: { endDate: '2025-01-01', value: 100 } }
    const inputData = { timezone: 'GMT' }
    const config = { unit: 'kg', decimal: 2 }
    const expectedDate = 'formatted-date'
    DateUtilsService.getDateFormat.mockReturnValue(expectedDate)

    const result = DynamicIntakeService.parseResponse(apiResponse, {
      inputData,
      config
    })

    expect(DateUtilsService.getDateFormat).toHaveBeenCalledWith({
      value: '2025-01-01',
      gmt: 'GMT'
    })
    expect(result).toEqual({
      date: expectedDate,
      value: 100,
      unit: 'kg',
      decimal: 2
    })
  })

  it('should parse API with no data in response ', () => {
    const apiResponse = { data: '' }
    const inputData = { timezone: 'GMT' }
    const config = { unit: 'kg', decimal: 2 }
    const expectedDate = 'formatted-date'
    DateUtilsService.getDateFormat.mockReturnValue(expectedDate)

    const result = DynamicIntakeService.parseResponse(apiResponse, {
      inputData,
      config
    })

    expect(DateUtilsService.getDateFormat).toHaveBeenCalledWith({
      value: '2025-01-01',
      gmt: 'GMT'
    })
    expect(result).toEqual({
      date: '',
      value: '',
      unit: 'kg',
      decimal: 2
    })
  })
})

describe('parseStructure', () => {
  it('should parse structure correctly', () => {
    const structure = {
      cardOptions: {
        title: 'Test Title',
        type: 'chart',
        style: { classType: 'highlight' },
        apiRoute: '/api/test',
        content: {
          description: 'Test Description',
          peakTime: '12:00',
          title: 'Content Title',
          unit: 'kg',
          value: 50
        }
      }
    }

    const expectedOutput = {
      title: 'Test Title',
      type: 'chart',
      peak: 'highlight',
      routes: [
        {
          method: 'post',
          apiRoute: '/api/test',
          config: {
            description: 'Test Description',
            peakTime: '12:00',
            title: 'Content Title',
            unit: 'kg',
            value: 50
          }
        }
      ]
    }

    expect(DynamicIntakeService.parseStructure(structure)).toEqual(
      expectedOutput
    )
  })

  it('should return an empty string for peak when style.classType is undefined', () => {
    const structure = {
      cardOptions: {
        title: 'Test Title',
        type: 'chart',
        style: {},
        apiRoute: '/api/test',
        content: {
          description: 'Test Description',
          peakTime: '12:00',
          title: 'Content Title',
          unit: 'kg',
          value: 50
        }
      }
    }

    const result = DynamicIntakeService.parseStructure(structure)
    expect(result.peak).toBe('')
  })
})
