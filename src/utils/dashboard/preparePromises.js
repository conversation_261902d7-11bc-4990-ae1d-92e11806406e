import DynamicServiceUtils from './DynamicServiceUtils'

export default function preparePromise(apiRoute, inputData, http) {
  //   const promises = []

  //   dataStructure.cardOptions.contents.forEach((contentItem) => {
  //     const apiRoute = contentItem.apiRoute
  if (apiRoute.urlParams) {
    apiRoute = DynamicServiceUtils.getUrlParams({ apiRoute, inputData })
  } else {
    apiRoute = DynamicServiceUtils.replaceInstance({ apiRoute, inputData })
  }

  let params = null

  if (apiRoute.params) {
    // apiRoute.params.id = apiRoute.params.id || inputData.id
    // apiRoute.params.initial_date = inputData.initial_date
    // apiRoute.params.final_date = inputData.final_date
    // params.timezone = inputData.timezone

    params = {
      ...apiRoute.params,
      id: apiRoute.params.id || inputData.id,
      initial_date: inputData.initial_date,
      final_date: inputData.final_date
    }
  }

  return http[apiRoute.method](apiRoute.url, params)

  // const apiZordonService = $apiZordon.service
  // promises.push(apiZordonService[apiRoute.method](apiRoute.url, params))
  //   })

  //   return promises
}
