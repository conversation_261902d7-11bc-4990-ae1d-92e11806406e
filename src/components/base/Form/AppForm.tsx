import { DetailedHTMLProps, FormHTMLAttributes } from 'react'

type AppFormProps = DetailedHTMLProps<
  FormHTMLAttributes<HTMLFormElement>,
  HTMLFormElement
> & {
  handleSubmit: () => void
}

export const AppForm = function (props: AppFormProps) {
  const { handleSubmit, children, ...rest } = props

  const onSubmitLocal = (event: React.FormEvent) => {
    event.preventDefault()

    handleSubmit()
  }

  return (
    <form
      data-testid="app-form"
      autoComplete="off"
      onSubmit={onSubmitLocal}
      {...rest}
    >
      {children}
    </form>
  )
}
