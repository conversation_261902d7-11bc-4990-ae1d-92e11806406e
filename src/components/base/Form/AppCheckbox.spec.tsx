import { renderWithRedux } from '@/utils/setupTest'
import { cleanup } from '@testing-library/react'
import { AppCheckbox as Component } from './AppCheckbox'

cleanup()

describe('/src/components/base/Form/AppCheckbox', () => {
  it('Monta Component', async () => {
    const { findByTestId } = renderWithRedux(<Component label="teste" />)
    expect(await findByTestId('app-checkbox')).toBeTruthy()
  })
})
