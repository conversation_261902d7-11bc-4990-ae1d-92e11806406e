import React from 'react'

type AppFormLabelProps = {
  'data-testid'?: string
  idFor?: string
  label?: string
  labelStart?: React.ReactNode
  labelEnd?: React.ReactNode
  className?: string
}

export const AppFormLabel: React.FC<AppFormLabelProps> = function ({
  idFor,
  label,
  labelEnd,
  labelStart,
  ...rest
}) {
  const dataTestId = rest['data-testid']
  return (
    <label
      {...rest}
      id={dataTestId}
      data-testid={dataTestId}
      htmlFor={idFor}
      className={`text-input ${rest.className || ''}`}
    >
      {labelStart && <div className="pr-1 mr-auto">{labelStart}</div>}

      {label}

      {labelEnd && <div className="pl-1 ml-auto">{labelEnd}</div>}
    </label>
  )
}
