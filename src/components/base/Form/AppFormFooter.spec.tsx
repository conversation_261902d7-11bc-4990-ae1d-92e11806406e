import { render } from '@/utils/setupTest'
import { cleanup, screen } from '@testing-library/react'
import { AppFormFooter } from './AppFormFooter'

cleanup()

describe('/components/base/Form/AppForm', () => {
  test('render Component', async () => {
    render(
      <AppFormFooter>
        <div data-testid="app-form">Form</div>
      </AppFormFooter>
    )

    const el = await screen.findByTestId('app-form')

    expect(el).toBeInTheDocument()
  })
})
