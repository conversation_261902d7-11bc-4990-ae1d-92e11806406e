import { render } from '@/utils/setupTest'
import { cleanup } from '@testing-library/react'
import { AppForm as Component } from './AppForm'

let count = 0
const handleSubmit = () => {
  count++
}

cleanup()

describe('/components/base/Form/AppForm', () => {
  it('Monta Component', async () => {
    const { findByTestId } = render(
      <Component handleSubmit={handleSubmit}>
        <h1>Form</h1>
      </Component>
    )
    expect(await findByTestId('app-form')).toBeTruthy()
  })

  it('props children', async () => {
    const { findByTestId } = render(
      <Component handleSubmit={handleSubmit}>
        <h1>Form</h1>
      </Component>
    )

    expect(await findByTestId('app-form')).toBeTruthy()
  })

  it('props handleSubmit', async () => {
    const { findByTestId } = render(
      <Component handleSubmit={handleSubmit}>
        <>
          <h1>Form</h1>
          <button data-testid="test-btn-submit">btn</button>
        </>
      </Component>
    )

    const el = await findByTestId('test-btn-submit')
    el.click()

    expect(count).toEqual(1)
  })
})
