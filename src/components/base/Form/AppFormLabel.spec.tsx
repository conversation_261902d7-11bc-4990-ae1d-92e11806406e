import { renderWithRedux } from '@/utils/setupTest'
import { cleanup } from '@testing-library/react'
import { AppFormLabel } from './AppFormLabel'

cleanup()

describe('/components/base/Form/AppFormLabel', () => {
  it('Monta componente', async () => {
    const { findByTestId } = renderWithRedux(
      <AppFormLabel data-testid="app-form-label" idFor="123" />
    )
    expect(await findByTestId('app-form-label')).toBeTruthy()
  })

  it('prop label', () => {
    renderWithRedux(
      <AppFormLabel data-testid="123" idFor="123" label="label text" />
    )
  })

  it('prop labelStart', () => {
    renderWithRedux(
      <AppFormLabel data-testid="123" idFor="123" labelStart="text-start" />
    )
  })

  it('prop labelEnd', () => {
    renderWithRedux(
      <AppFormLabel data-testid="123" idFor="123" labelEnd="text-end" />
    )
  })
})
