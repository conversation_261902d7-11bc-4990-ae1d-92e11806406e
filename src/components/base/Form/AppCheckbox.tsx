import { Checkbox, FormControlLabel, CheckboxProps } from '@mui/material'

type AppCheckboxProps = CheckboxProps & {
  wrapper?: { className?: string }
  label: string
}
export const AppCheckbox = function ({
  label,
  wrapper,
  ...rest
}: AppCheckboxProps) {
  return (
    <FormControlLabel
      data-testid="app-checkbox"
      control={<Checkbox {...rest} />}
      label={label}
      {...wrapper}
    />
  )
}
