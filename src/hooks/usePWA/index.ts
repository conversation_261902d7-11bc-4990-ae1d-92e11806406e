import { systemCookie } from '@/@core/infra/memory/cookie'
import { languageByMode } from '@/@core/language'
import { useMemo } from 'react'

export const usePWA = () => {
  const language = languageByMode(systemCookie.get().language)

  const handleInstallButtonClick = async () => {
    if (window.pwa) {
      window.pwa.prompt()
      const { outcome } = await window.pwa.userChoice
      if (outcome === 'accepted') {
        window.pwa = null
      }
    }
  }

  const pwaIsInstalled = !!window.pwa

  const textInstallPWA = useMemo(
    () => (pwaIsInstalled ? language.pwa.install : language.pwa.installed),
    [pwaIsInstalled]
  )

  return {
    handleInstallButtonClick,
    textInstallPWA,
    pwaIsInstalled
  }
}
