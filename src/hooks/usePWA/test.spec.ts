import { cleanup, renderHook } from '@testing-library/react'

import { appCookie } from '@/@core/infra/memory/cookie'

import { usePWA } from './index'

cleanup()

describe('src/hooks/usePWA/index.ts', () => {
  beforeEach(() => {
    appCookie.init()
    jest.clearAllMocks()
  })

  it('retorna os valores corretos quando o pwa é instalado', () => {
    const promptSpy = jest.fn()
    const userChoice = Promise.resolve({ outcome: 'dismissed', platform: '' })
    const pwa = {
      prompt: promptSpy,
      userChoice
    }

    // @ts-ignore: mock the window object
    window.pwa = pwa

    const { result } = renderHook(() => usePWA())

    expect(result.current.pwaIsInstalled).toBe(true)
    expect(result.current.textInstallPWA).toBe('Instalar APP')

    result.current.handleInstallButtonClick()

    expect(window.pwa).not.toBeNull()
  })

  it('retorna os valores corretos quando o pwa não está instalado', () => {
    // @ts-ignore: mock the window object
    window.pwa = null

    const { result } = renderHook(() => usePWA())

    expect(result.current.pwaIsInstalled).toBe(false)
    expect(result.current.textInstallPWA).toBe('APP já instalado')

    result.current.handleInstallButtonClick()

    expect(window.pwa).toBe(null)
  })
})
