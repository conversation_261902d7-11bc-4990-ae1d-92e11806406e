import React from 'react'

interface HookOptions {
  delay: number
  deps?: React.DependencyList
}
export const useDebounceFunction = (
  handler: Function,
  props: HookOptions | number
) => {
  const { delay, deps } = (
    Number.isInteger(props) ? { delay: props } : props
  ) as HookOptions

  const time = React.useRef<NodeJS.Timeout>()

  const debounceHandler = React.useCallback(
    (args?: unknown) => {
      clearTimeout(time.current)

      time.current = setTimeout(() => {
        handler(args)
      }, delay)
    },
    [deps]
  )

  return debounceHandler
}
