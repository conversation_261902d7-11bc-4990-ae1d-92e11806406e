import { act, cleanup, createEvent, fireEvent } from '@testing-library/react'

import { renderWithRedux } from '@/utils/setupTest'
import { usePrepareInstallPWA } from './index'

cleanup()

describe('src/hooks/usePreparePWA/index.ts', () => {
  const ComponentTest = () => {
    usePrepareInstallPWA()

    return null
  }

  it('render usePrepareInstallPWA', async () => {
    const event = createEvent('beforeinstallprompt', window)

    const rend = renderWithRedux(<ComponentTest />)

    await act(async () => {
      fireEvent(window, event)
    })

    rend.unmount()
  })
})
