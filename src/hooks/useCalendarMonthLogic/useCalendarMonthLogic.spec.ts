import { act, renderHook } from '@testing-library/react'
import { parseDate, useCalendarMonthLogic } from './useCalendarMonthLogic'

describe('useCalendarMonthLogic', () => {
  const allowedDates = ['2024-05-01', '2025-01-01', '2025-05-01', '2025-08-01']
  const blockedDates = ['2025-03-01']

  it('inicia com ano e mês corretos no modo não controlado', () => {
    const { result } = renderHook(() =>
      useCalendarMonthLogic({ initialDate: '2025-05-01' })
    )
    expect(result.current.year).toBe(2025)
    expect(result.current.selectedMonth).toBeNull()
  })

  it('inicia no modo controlado com value', () => {
    const { result } = renderHook(() =>
      useCalendarMonthLogic({ value: '2025-03-01', onChange: jest.fn() })
    )
    expect(result.current.year).toBe(2025)
    expect(result.current.selectedMonth).toBe(2)
  })

  it('inicia sem initialDate nem value', () => {
    const { result } = renderHook(() => useCalendarMonthLogic({}))
    expect(result.current.selectedMonth).toBeNull()
  })

  it('inicia ano igual ao primeiro availableYear quando defaultEnabled=false e sem initialDate', () => {
    const { result } = renderHook(() =>
      useCalendarMonthLogic({ defaultEnabled: false, allowedDates })
    )
    expect(result.current.year).toBe(2024)
    expect(result.current.selectedMonth).toBeNull()
  })

  it('aplica fallback para initialDate quando presente', () => {
    const { result } = renderHook(() =>
      useCalendarMonthLogic({ initialDate: '2011-06-01' })
    )
    expect(result.current.year).toBe(2011)
  })

  it('maneja fallback para defaultEnabled=false e allowedDates vazio', () => {
    const fakeDate = '2023-11-20'
    const { result } = renderHook(() =>
      useCalendarMonthLogic({
        defaultEnabled: false,
        allowedDates: [],
        initialDate: fakeDate
      })
    )
    expect(result.current.year).toBe(2023)
    expect(result.current.selectedMonth).toBeNull()
  })

  it('handleNextYear avança para próximo availableYear quando defaultEnabled=false', () => {
    const dates = ['2022-01-01', '2023-01-01', '2025-01-01']
    const { result } = renderHook(() =>
      useCalendarMonthLogic({
        defaultEnabled: false,
        allowedDates: dates,
        initialDate: dates[0]
      })
    )
    expect(result.current.year).toBe(2022)
    act(() => result.current.handleNextYear())
    expect(result.current.year).toBe(2023)
  })

  it('handleNextYear mantém o ano quando não há próximo availableYear', () => {
    const single = ['2024-01-01']
    const { result } = renderHook(() =>
      useCalendarMonthLogic({
        defaultEnabled: false,
        allowedDates: single,
        initialDate: single[0]
      })
    )
    expect(result.current.year).toBe(2024)
    act(() => result.current.handleNextYear())
    expect(result.current.year).toBe(2024)
  })

  it('desabilita prev/next corretamente com defaultEnabled=false e intervalo limitado', () => {
    const { result } = renderHook(() =>
      useCalendarMonthLogic({
        defaultEnabled: false,
        allowedDates: ['2024-05-01', '2025-05-01'],
        initialDate: '2024-05-01'
      })
    )
    expect(result.current.isPrevDisabled).toBe(true)
    expect(result.current.isNextDisabled).toBe(false)

    act(() => result.current.handleNextYear())
    expect(result.current.isPrevDisabled).toBe(true)
    expect(result.current.isNextDisabled).toBe(false)
  })

  it('reseta ano e mês ao mudar availableDates (defaultEnabled=false)', () => {
    const { result, rerender } = renderHook(
      ({ allowedDates }) =>
        useCalendarMonthLogic({
          defaultEnabled: false,
          allowedDates,
          initialDate: '2025-01-01'
        }),
      { initialProps: { allowedDates: ['2025-01-01'] } }
    )
    expect(result.current.year).toBe(2025)
    expect(result.current.selectedMonth).toBeNull()

    rerender({ allowedDates: ['2024-05-01', '2025-01-01'] })
    expect(result.current.year).toBe(2024)
    expect(result.current.selectedMonth).toBeNull()
  })

  it('reseta ano ao mudar defaultEnabled de true para false e availableDates não vazio', () => {
    const dates = ['2022-01-01', '2023-01-01']
    const { result, rerender } = renderHook(
      ({ defaultEnabled, allowedDates }) =>
        useCalendarMonthLogic({ defaultEnabled, allowedDates }),
      { initialProps: { defaultEnabled: true, allowedDates: [] } }
    )
    const currentYear = new Date().getFullYear()
    expect(result.current.year).toBe(currentYear)

    rerender({ defaultEnabled: false, allowedDates: dates as never[] })
    expect(result.current.year).toBe(2022)
    expect(result.current.selectedMonth).toBeNull()
  })

  it('reseta ano ao mudar defaultEnabled de true para false e availableDates vazio', () => {
    const { result, rerender } = renderHook(
      ({ defaultEnabled }) =>
        useCalendarMonthLogic({ defaultEnabled, allowedDates: [] }),
      { initialProps: { defaultEnabled: true } }
    )
    const currentYear = new Date().getFullYear()
    expect(result.current.year).toBe(currentYear)

    rerender({ defaultEnabled: false })
    expect(result.current.year).toBe(currentYear)
    expect(result.current.selectedMonth).toBeNull()
  })

  it('navega com defaultEnabled=true sem initialDate (não altera o ano)', () => {
    const currentYear = new Date().getFullYear()
    const { result } = renderHook(() =>
      useCalendarMonthLogic({ defaultEnabled: true })
    )
    act(() => result.current.handleNextYear())
    expect(result.current.year).toBe(2026)
  })

  it('filtra meses permitidos quando defaultEnabled=false', () => {
    const { result } = renderHook(() =>
      useCalendarMonthLogic({
        defaultEnabled: false,
        allowedDates,
        initialDate: '2025-01-01'
      })
    )
    const months = result.current.monthEntries.map((m) => m.idx)
    expect(months).toEqual([0, 4, 7])
  })

  it('bloqueia meses corretamente com blockedDates', () => {
    const { result } = renderHook(() =>
      useCalendarMonthLogic({ blockedDates, initialDate: '2025-01-01' })
    )
    const march = result.current.monthEntries.find((m) => m.idx === 2)
    expect(march?.isBlocked).toBe(true)
  })

  it('chama onChange ao clicar mês', () => {
    const onChange = jest.fn()
    const { result } = renderHook(() =>
      useCalendarMonthLogic({
        onChange,
        initialDate: '2025-01-01',
        allowedDates,
        defaultEnabled: false
      })
    )
    act(() => result.current.handleMonthClick(7))
    expect(result.current.selectedMonth).toBe(7)
    expect(onChange).toHaveBeenCalledWith('2025-08-01')
  })
})

describe('parseISODate', () => {
  it('retorna Date válido para string ISO válida', () => {
    const date = parseDate('2025-12-25')
    expect(date.getFullYear()).toBe(2025)
    expect(date.getMonth()).toBe(11)
    expect(date.getDate()).toBe(25)
  })

  it('retorna hoje para string inválida', () => {
    const before = new Date()
    const result = parseDate('invalid-date')
    const after = new Date()
    expect(result.getTime()).toBeGreaterThanOrEqual(before.getTime())
    expect(result.getTime()).toBeLessThanOrEqual(after.getTime())
  })
})
