import { format, isValid, parse } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

export interface MonthEntry {
  name: string
  idx: number
  isBlocked: boolean
  isAllowed: boolean
  isSelected?: boolean
}
type InitialYearMonth = { initialYear: number; initialMonth: number }

// Lista dos nomes dos meses em Português (PT-BR)
const MONTH_NAMES_PT_BR: string[] = Array.from({ length: 12 }).map((_, idx) => {
  const raw = format(new Date(2000, idx, 1), 'LLLL', { locale: ptBR })
  return raw.charAt(0).toUpperCase() + raw.slice(1)
})

/* Converte string (yyyy-MM-dd) em Date*/
export const parseDate = (date: string): Date => {
  const dt = parse(date, 'yyyy-MM-dd', new Date())
  if (!isValid(dt)) {
    return new Date()
  }
  return dt
}

/* Função auxiliar: obtém ano e mês iniciais a partir de uma data opcional*/
function getInitialYearMonth(dateString?: string): InitialYearMonth {
  const date = dateString ? parseDate(dateString) : new Date()
  return { initialYear: date.getFullYear(), initialMonth: date.getMonth() }
}

/*Constrói um mapa de anos para conjuntos de índices de meses a partir de um array de datas*/
function buildYearMonthMap(dates: string[]): Map<number, Set<number>> {
  return dates.reduce((map, d) => {
    const date = parseDate(d)
    const y = date.getFullYear()
    const m = date.getMonth()
    const set = map.get(y) ?? new Set<number>()
    set.add(m)
    map.set(y, set)
    return map
  }, new Map<number, Set<number>>())
}

interface CalendarMonthLogicProps {
  value?: string
  initialDate?: string
  defaultEnabled?: boolean
  blockedDates?: string[]
  allowedDates?: string[]
  onChange?: (dateISO: string) => void
}

/*Hook principal: gerencia estado e lógica de navegação de meses/anos*/
export function useCalendarMonthLogic({
  value,
  initialDate,
  defaultEnabled = true,
  blockedDates = [],
  allowedDates = [],
  onChange
}: CalendarMonthLogicProps) {
  /* Determina se é modo controlado (value definido) */
  const isControlled = value !== undefined

  const initialDateRef = useRef(getInitialYearMonth(initialDate))
  const hasMountedRef = useRef(false)

  const blockedMap = useMemo(
    () => buildYearMonthMap(blockedDates),
    [blockedDates]
  )
  const allowedMap = useMemo(
    () => buildYearMonthMap(allowedDates),
    [allowedDates]
  )

  const currentYear = useMemo(() => new Date().getFullYear(), [])

  // calcula estados iniciais já considerando allowedDates
  const initialAvailableYears = defaultEnabled
    ? [currentYear]
    : Array.from(allowedMap.keys()).sort((a, b) => a - b)

  const defaultYear = (() => {
    let year = currentYear

    // se for controlled ou tiver initialDate, usa ele
    if (isControlled || initialDate) {
      year = initialDateRef.current.initialYear
    }

    // se não for controlled/initial e defaultEnabled=false, pega o primeiro allowed
    if (
      !isControlled &&
      !initialDate &&
      !defaultEnabled &&
      initialAvailableYears.length > 0
    ) {
      year = initialAvailableYears[0]
    }

    return year
  })()

  const [year, setYear] = useState<number>(defaultYear)
  const [selectedMonth, setSelectedMonth] = useState<number | null>(
    isControlled ? initialDateRef.current.initialMonth : null
  )
  /*Lista de anos disponíveis para navegar*/
  const availableYears = useMemo<number[]>(() => {
    if (!defaultEnabled)
      return Array.from(allowedMap.keys()).sort((a, b) => a - b)
    return [currentYear]
  }, [defaultEnabled, allowedMap, currentYear])

  /*useEffect para sincronizar estado no modo controlado */
  useEffect(() => {
    if (isControlled && value) {
      const { initialYear: y, initialMonth: m } = getInitialYearMonth(value)
      setYear(y)
      setSelectedMonth(m)
    }
  }, [value])

  /*useEffect para resetar seleção após mudanças em availableYears ou defaultEnabled */
  useEffect(() => {
    if (!hasMountedRef.current) {
      hasMountedRef.current = true
      return
    }

    if (defaultEnabled) return
    const firstYear = availableYears[0] ?? currentYear
    setYear(firstYear)
    setSelectedMonth(null)
  }, [availableYears, defaultEnabled, currentYear])

  /*Índice do ano atual na lista de anos disponíveis */
  const yearIndex = useMemo(
    () => availableYears.indexOf(year),
    [availableYears, year]
  )

  /*Controle de habilitação dos botões de navegar ano */
  const isPrevDisabled = useMemo(
    () => !defaultEnabled && yearIndex <= 0,
    [defaultEnabled, yearIndex]
  )
  const isNextDisabled = useMemo(
    () =>
      (!defaultEnabled && yearIndex >= availableYears.length - 1) ||
      (defaultEnabled && year >= currentYear),
    [defaultEnabled, yearIndex, availableYears.length, year, currentYear]
  )

  /*Atualiza o ano com offset e limpa seleção de mês */
  const updateYear = useCallback(
    (offset: number) => {
      const nextYear = defaultEnabled
        ? year + offset
        : availableYears[yearIndex + offset] ?? year
      setYear(nextYear)
      setSelectedMonth(null)
    },
    [defaultEnabled, year, availableYears, yearIndex]
  )

  const handlePrevYear = useCallback(() => updateYear(-1), [updateYear])
  const handleNextYear = useCallback(() => updateYear(1), [updateYear])

  /* Gera lista de MonthEntry para renderizar meses*/
  const monthEntries = useMemo<MonthEntry[]>(() => {
    return MONTH_NAMES_PT_BR.map((name, idx) => ({
      name,
      idx,
      isBlocked: blockedMap.get(year)?.has(idx) ?? false,
      isAllowed: allowedMap.get(year)?.has(idx) ?? false,
      isSelected: idx === selectedMonth
    }))
      .filter((m) => defaultEnabled || m.isAllowed)
      .sort((a, b) => a.idx - b.idx)
  }, [year, blockedMap, allowedMap, defaultEnabled, selectedMonth])

  const handleMonthClick = useCallback(
    (idx: number) => {
      const iso = format(new Date(year, idx, 1), 'yyyy-MM-dd')
      setSelectedMonth(idx)
      onChange?.(iso)
    },
    [year, onChange]
  )

  return {
    year,
    selectedMonth,
    isPrevDisabled,
    isNextDisabled,
    handlePrevYear,
    handleNextYear,
    monthEntries,
    handleMonthClick
  }
}
