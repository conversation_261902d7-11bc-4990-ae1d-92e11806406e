import { useState } from 'react'

interface IProgressModalCoreProps {
  progressLength: number
}
const defaultProps: IProgressModalCoreProps = {
  progressLength: 1
}
export const useProgressModalCore = (
  props: IProgressModalCoreProps = defaultProps
) => {
  const { progressLength } = props

  const [progress, setProgress] = useState<number>(1)

  const reset = () => {
    setProgress(1)
  }

  const next = () => {
    setProgress((p) => p + 1)
  }

  const previous = () => {
    setProgress((p) => p - 1)
  }

  const handleProgress = () => {
    if (progress < progressLength) {
      next()
      return false
    }
    return true
  }

  return {
    progress,
    reset,
    next,
    previous,
    handleProgress
  }
}
