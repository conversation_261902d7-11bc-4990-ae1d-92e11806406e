import { cleanup, waitFor } from '@testing-library/react'

import { renderWithRedux } from '@/utils/setupTest'

import { useProgressModalCore } from '.'

let core: {
  progress: number
  reset: () => void
  next: () => void
  previous: () => void
  handleProgress: () => boolean
}

cleanup()

describe('src/utils/core/progressModal', () => {
  it('Monta core', async () => {
    const ComponentTest = () => {
      core = useProgressModalCore({
        progressLength: 3
      })
      return null
    }

    renderWithRedux(<ComponentTest />)

    waitFor(() => {
      core.previous()
    })

    expect(core.progress).toBe(1)

    waitFor(() => {
      core.next()
      core.next()
      core.handleProgress()
    })

    expect(core.progress).toBe(1)

    waitFor(() => {
      core.reset()
    })

    expect(core.progress).toBe(1)
  })

  it('Monta core', async () => {
    const ComponentTest = () => {
      core = useProgressModalCore()
      return null
    }
    renderWithRedux(<ComponentTest />)
  })
})
