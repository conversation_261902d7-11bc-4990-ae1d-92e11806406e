import Snackbar, { SnackbarCloseReason } from '@mui/material/Snackbar'
import <PERSON>i<PERSON><PERSON><PERSON>, { AlertProps, AlertColor } from '@mui/material/Alert'
import { forwardRef, SyntheticEvent, useMemo, useRef, useState } from 'react'

type AAA = {
  open?: boolean
  uniq?: number
  message: string
  type: 'success' | 'error' | 'danger'
  vertical?: string
  horizontal?: string
  width?: string
  autoHideDuration?: number
}

type ToastProps = {
  open: boolean
  uniq: number
  message: string
  type: AlertColor
  vertical: 'top' | 'bottom'
  horizontal: 'center' | 'left' | 'right'
  width: string
  autoHideDuration: number
  onClose: (n: number) => void
  handleClose:
    | ((
        event: Event | SyntheticEvent<null, Event>,
        reason: SnackbarCloseReason
      ) => void)
    | undefined
}
const Toast = (props: ToastProps) => {
  const Alert = forwardRef<HTMLDivElement, AlertProps>(function Alert(
    props,
    ref
  ) {
    return <MuiAlert ref={ref} variant="filled" {...props} />
  })

  return (
    <div onClick={() => props.onClose(props.uniq)}>
      <Snackbar
        open={props.open}
        onClose={props.handleClose}
        anchorOrigin={{
          vertical: props.vertical,
          horizontal: props.horizontal
        }}
      >
        <Alert
          sx={{ width: props.width }}
          // onClose={props.handleClose}
          severity={props.type}
        >
          {props.message}
        </Alert>
      </Snackbar>
    </div>
  )
}

let index = 0

export const useToast = () => {
  const [items, setItems] = useState<AAA[]>([])
  const time = useRef<NodeJS.Timeout>()

  function handleClose(uniq: number) {
    setItems((old) => old.filter((item) => item.uniq !== uniq))
  }

  const toast = useMemo(() => {
    return items.length
      ? items.map((item) => (
          <Toast key={item.uniq} {...item} onClose={handleClose} />
        ))
      : null
  }, [items])

  // const autoCloseToast = (item: AAA) => {
  //   !!item.autoHideDuration
  //     ? setTimeout(() => handleClose(item.uniq), item.autoHideDuration)
  //     : null;
  // };

  const removeFirstToast = () => {
    console.log(items)
    // const uniq = items[0].uniq

    // handleClose(Number(uniq))
  }

  const stopRemove = () => {
    clearTimeout(time.current)
  }

  const autoRemove = (duration: number) => {
    console.log('duration', duration)

    time.current = setTimeout(() => {
      const hasToast = !!items.length

      if (!hasToast) {
        stopRemove()
      }

      removeFirstToast()
    }, duration)
  }

  const addToast = ({
    message,
    type,
    vertical = 'bottom',
    horizontal = 'center',
    width = '800%',
    autoHideDuration = 2000
  }: AAA) => {
    const newItem = {
      open: true,
      uniq: index,
      message,
      type,
      vertical,
      horizontal,
      width,
      autoHideDuration
    }
    // autoCloseToast(newItem);

    setItems((old) => [...old, newItem])

    autoRemove(autoHideDuration)

    index++
  }

  // useEffect(() => {
  //   autoRemove()
  // }, [])

  return {
    toast,
    addToast
  }
}
