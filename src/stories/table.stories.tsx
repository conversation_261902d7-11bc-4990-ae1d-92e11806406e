import React from 'react'
import { Meta, StoryObj } from '@storybook/react'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'

interface RenderTableProps {
  isMobile?: boolean
  items?: {
    id: number
    name: string
    accountName: string
    parentName: string
    stateName: string
  }[]
}

const meta: Meta = {
  title: 'UI/Table',
  decorators: [
    (Story) => (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          height: '100vh'
        }}
      >
        <Story />
      </div>
    )
  ],
  argTypes: {
    isMobile: {
      name: 'Mobile View',
      description: 'Alternar entre Table e TableMobile',
      control: 'boolean',
      defaultValue: false
    }
  }
}

export default meta

type Story = StoryObj<typeof meta>

const RenderTable = ({ isMobile }: RenderTableProps) => {
  const items = [
    {
      id: 1,
      name: 'Empresa 1',
      accountName: 'Conta 1',
      parentName: 'Pai 1',
      stateName: 'Estado 1'
    },
    {
      id: 2,
      name: 'Empresa 2',
      accountName: 'Conta 2',
      parentName: 'Pai 2',
      stateName: 'Estado 2'
    }
  ]

  return isMobile ? (
    <TableMobile.Root>
      {items.map((company) => (
        <TableMobile.Item key={company.id}>
          <TableMobile.Head />
          <TableMobile.Row>
            <TableMobile.Cell>Name</TableMobile.Cell>
            <TableMobile.Cell>{company.name}</TableMobile.Cell>
          </TableMobile.Row>
          <TableMobile.Row>
            <TableMobile.Cell>Account</TableMobile.Cell>
            <TableMobile.Cell>{company.accountName}</TableMobile.Cell>
          </TableMobile.Row>
          <TableMobile.Row>
            <TableMobile.Cell>Parent</TableMobile.Cell>
            <TableMobile.Cell>{company.parentName}</TableMobile.Cell>
          </TableMobile.Row>
          <TableMobile.Row>
            <TableMobile.Cell>State</TableMobile.Cell>
            <TableMobile.Cell>{company.stateName}</TableMobile.Cell>
          </TableMobile.Row>
          <TableMobile.Footer />
        </TableMobile.Item>
      ))}
    </TableMobile.Root>
  ) : (
    <div className="w-full">
      <Table.Root>
        <Table.Header>
          <Table.Row>
            <Table.Head>ID</Table.Head>
            <Table.Head>Name</Table.Head>
            <Table.Head>Account</Table.Head>
            <Table.Head>Parent</Table.Head>
            <Table.Head>State</Table.Head>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {items.map((company) => (
            <Table.Row key={company.id}>
              <Table.Cell>{company.id}</Table.Cell>
              <Table.Cell>{company.name}</Table.Cell>
              <Table.Cell>{company.accountName}</Table.Cell>
              <Table.Cell>{company.parentName}</Table.Cell>
              <Table.Cell>{company.stateName}</Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
    </div>
  )
}

export const Base: Story = {
  args: {
    isMobile: false
  },
  render: (args) => <RenderTable {...args} />
}
