import React from 'react'
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { fn } from '@storybook/test'

import { Button, ButtonProps } from '@/@core/presentation/shared/ui/button'
import { Icon as IconOld, faCog } from '@/@core/presentation/shared/ui/icon'

import '@/assets/css/app.scss'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  component: Button,
  title: 'UI/Button',
  parameters: {
    // layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    children: {
      name: 'children',
      type: 'string'
    },
    disabled: {
      name: 'disabled',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: { summary: 'false' }
      }
    },
    variant: {
      name: 'variant',
      control: { type: 'select' },
      type: 'string',
      options: [
        'primary',
        'secondary-gray',
        'secondary-color',
        'tertiary-gray',
        'tertiary-color',
        'link-gray',
        'link-color',
        'gradient-color',
        'gradient-stroke',
        'error-primary',
        'error-secondary',
        'error-tertiary',
        'error-link'
      ],
      table: {
        defaultValue: { summary: 'secondary' }
      }
    },
    size: {
      name: 'size',
      control: 'select',
      type: 'string',
      options: ['2xs', 'xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl'],
      table: {
        defaultValue: { summary: 'sm' }
      }
    }
  },
  args: {
    children: 'Button',
    size: 'sm',
    variant: 'primary',
    disabled: false,
    onClick: fn()
  },
  render: Render
} satisfies Meta<typeof Button>

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {}

interface RenderProps
  extends ButtonProps,
    React.RefAttributes<
      HTMLButtonElement & {
        Icon: React.ReactNode
      }
    > {}

function Render(args: RenderProps) {
  return (
    <div className="flex flex-wrap gap-2">
      <Button {...args}>{args.children}</Button>

      <Button {...args}>
        <Button.Icon>
          <IconOld icon={faCog} />
        </Button.Icon>
        {args.children}
      </Button>

      <Button {...args}>
        {args.children}
        <Button.Icon>
          <IconOld icon={faCog} />
        </Button.Icon>
      </Button>
    </div>
  )
}
