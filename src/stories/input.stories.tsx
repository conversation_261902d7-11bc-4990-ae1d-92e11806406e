import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import { fn } from '@storybook/test'

import '@/assets/css/app.scss'
import { InputProps } from '@/@core/presentation/shared/ui/input/InputContent'
import {
  Icon as IconOld,
  faArrowsRotate,
  faTrash
} from '@/@core/presentation/shared/ui/icon'
import { Input } from '@/@core/presentation/shared/ui/input'

const meta = {
  component: Input.Content,
  title: 'UI/Input',
  parameters: {},
  tags: ['autodocs'],
  argTypes: {
    helperText: {
      name: 'helperText',
      type: 'string'
    },
    status: {
      name: 'status',
      control: { type: 'select' },
      type: 'string',
      options: ['', 'isValid', 'isInvalid', 'isError'],
      table: {
        defaultValue: { summary: '' }
      }
    },
    size: {
      name: 'size',
      control: 'select',
      type: 'string',
      options: ['sm', 'md'],
      table: {
        defaultValue: { summary: 'sm' }
      }
    },
    slotStart: {
      name: 'slotStart',
      control: { type: 'text' },
      type: 'string'
    },
    slotEnd: {
      name: 'slotEnd',
      control: { type: 'text' },
      type: 'string'
    }
  },
  args: {
    helperText: '',
    status: '',
    size: 'sm',
    slotStart: null,
    slotEnd: null,
    onFocus: fn()
  },
  render: Render
} satisfies Meta<typeof Input.Content>

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {}

interface RenderProps
  extends InputProps,
    React.RefAttributes<HTMLInputElement> {}

function Render(args: RenderProps) {
  return (
    <div className="flex flex-col gap-4">
      <Input.Content {...args} />

      <Input.Content
        {...args}
        slotStart={<IconOld icon={faTrash} />}
        placeholder="Search..."
      />

      <Input.Content
        {...args}
        slotEnd={<IconOld icon={faArrowsRotate} />}
        placeholder="With end icon"
        helperText="Example of helper text"
      />

      <Input.Content
        {...args}
        status="isError"
        placeholder="Error status"
        helperText="There is an error"
      />
    </div>
  )
}
