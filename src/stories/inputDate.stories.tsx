import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'

import '@/assets/css/app.scss'
import 'react-datepicker/dist/react-datepicker.css'

import { InputDate } from '@/@core/presentation/shared/ui/inputData'
import { InputContentDateProps } from '@/@core/presentation/shared/ui/inputData/InputContentDate'
import { AppProvider } from '@/provider'

const meta: Meta<typeof InputDate.Date> = {
  component: InputDate.Date,
  title: 'UI/InputDate',
  tags: ['autodocs'],
  argTypes: {
    disabled: {
      description: 'Desativa o input',
      control: 'boolean',
      table: {
        defaultValue: { summary: 'false' },
        type: { summary: 'boolean' }
      }
    },
    format: {
      description: 'Formato de apresentação do valor no input',
      control: 'text',
      defaultValue: { summary: 'dd/MM/YYYY' },
      table: { type: { summary: 'string' } }
    },
    onChange: {
      description: 'Função chamada ao alterar a data',
      table: { disable: false }
    },
    showTimeSelect: { table: { disable: false } },
    selected: { table: { disable: false } }
  },
  args: {
    disabled: false,
    format: 'dd/MM/YYYY',
    onChange: () => {}
  },
  render: Render
}

function Render(props: InputContentDateProps) {
  const [value, setValue] = React.useState<string>('')

  return (
    <div className="min-h-[500px]">
      <AppProvider>
        <InputDate.Root>
          <InputDate.Date
            {...props}
            selected={value}
            onChange={(value) => setValue(value.dateFormated)}
          />
        </InputDate.Root>
      </AppProvider>
    </div>
  )
}

export default meta

type Story = StoryObj<typeof meta>

export const ModeloA: Story = {
  name: 'Date',
  args: {
    onChange: () => {}
  }
}

export const ModeloB: Story = {
  name: 'DateTime',
  args: {
    onChange: () => {},
    format: 'dd/MM/YYYY HH:mm',
    dateFormatCalendar: 'dd/MM/YYYY HH:mm',
    dateFormated: 'YYYY/MM/DD HH:mm',
    showTimeSelect: true,
    timeIntervals: 1
  }
}

export const ModeloC: Story = {
  name: 'Time',
  args: {
    onChange: () => {},
    showTimeSelect: true,
    timeIntervals: 1,
    format: 'HH:mm',
    dateFormatCalendar: 'HH:mm',
    dateFormated: 'YYYY/MM/DD HH:mm'
  }
}
