import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import { fn } from '@storybook/test'

import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Button } from '@/@core/presentation/shared/ui/button'

import '@/assets/css/app.scss'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  component: Dialog.Root,
  title: 'UI/Dialog',
  parameters: {
    // layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    onInteractOutside: {
      name: 'Fechar clique fora',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: { summary: true }
      }
    },
    overlay: {
      name: 'overlay',
      control: { type: 'boolean' },
      type: 'boolean',
      table: {
        defaultValue: { summary: true }
      }
    },
    size: {
      name: 'size',
      control: 'select',
      type: 'string',
      options: ['sm', 'md', 'lg', 'lg2'],
      table: {
        defaultValue: { summary: 'sm' }
      }
    }
  },
  args: {
    onInteractOutside: true,
    overlay: true,
    size: 'sm'
  },
  render: Render
}

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {}

interface RenderProps {
  onInteractOutside: boolean
  overlay: boolean
  size?: 'sm' | 'md' | 'lg' | 'lg2'
}

function Render({ onInteractOutside, ...rest }: RenderProps) {
  return (
    <div className="flex flex-wrap gap-2">
      <Dialog.Root>
        <Dialog.Trigger asChild>
          <Button type="button">Abrir</Button>
        </Dialog.Trigger>
        <Dialog.Content
          onInteractOutside={(e) => {
            if (!onInteractOutside) e.preventDefault()
          }}
          {...rest}
        >
          <Dialog.Header>
            <Dialog.Title>Título</Dialog.Title>
          </Dialog.Header>
          <Dialog.Description>descrição</Dialog.Description>
          conteúdo
          <Dialog.Footer>
            <Dialog.Close asChild>
              <Button type="button">FECHAR</Button>
            </Dialog.Close>
          </Dialog.Footer>
        </Dialog.Content>
      </Dialog.Root>
    </div>
  )
}
