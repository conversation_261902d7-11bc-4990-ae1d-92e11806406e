import type { Meta, StoryObj } from '@storybook/react'
import { Checkbox } from '@/@core/presentation/shared/ui/checkbox'

import '@/assets/css/app.scss'
import React from 'react'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  component: Checkbox.Content,
  title: 'UI/Checkbox',
  parameters: {
    // layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    disabled: {
      name: 'disabled',
      control: { type: 'boolean' },
      type: 'boolean'
    },
    variant: {
      name: 'variant',
      control: { type: 'select' },
      type: 'string',
      description: 'string',
      options: ['primary', 'secondary', 'error']
    },
    size: {
      name: 'size',
      control: { type: 'select' },
      type: 'string',
      options: ['sm', 'md']
    }
  },
  args: {
    variant: 'primary',
    size: 'sm',
    disabled: false
  },
  render: ({ variant, size, disabled }) => (
    <Checkbox.Root variant={variant ?? 'error'} size={size ?? 'sm'}>
      <Checkbox.Content
        id="checkbox-exemplo"
        {...{ disabled }}
        onCheckedChange={(value) => {
          console.log('... nn | value:', value)
        }}
      />
      <Checkbox.Label htmlFor="checkbox-exemplo">Checkbox</Checkbox.Label>
    </Checkbox.Root>
  )
} satisfies Meta<typeof Checkbox.Content>

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {}
