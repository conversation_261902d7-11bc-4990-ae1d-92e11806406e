import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import { fn } from '@storybook/test'

import { Tabs, TabsRootProps } from '@/@core/presentation/shared/ui/tabs'

import '@/assets/css/app.scss'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  component: Tabs.Root,
  title: 'UI/Tabs',
  parameters: {
    // layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      name: 'variant',
      control: { type: 'select' },
      type: 'string',
      options: ['primary', 'secondary', 'underline', 'underline-filled'],
      table: {
        defaultValue: { summary: 'primary' }
      }
    },
    orientation: {
      name: 'Orientação',
      control: { type: 'select' },
      type: 'string',
      options: ['vertical', 'horizontal'],
      table: {
        defaultValue: { summary: 'vertical' }
      }
    },
    filled: {
      name: 'width trigger',
      control: { type: 'select' },
      type: 'string',
      options: ['min-content', 'full-content'],
      table: {
        defaultValue: { summary: 'min-content' }
      }
    }
  },
  args: {
    variant: 'primary',
    orientation: 'vertical',
    filled: 'min-content'
  },
  render: Render
} //  satisfies Meta<typeof Tabs.Root>

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {}

interface RenderProps extends TabsRootProps {
  filled: string
}

function Render(args: RenderProps) {
  const filledCss = args.filled === 'full-content' ? 'flex' : ''

  return (
    <Tabs.Root
      defaultValue="data"
      variant={args.variant}
      className={args.orientation === 'vertical' ? 'flex gap-1' : ''}
    >
      <Tabs.List
        className={
          args.orientation === 'vertical' ? 'flex flex-col' : filledCss
        }
      >
        <Tabs.Trigger value="data" className="w-full">
          Meus dados
        </Tabs.Trigger>
        <Tabs.Trigger value="profile" className="w-full">
          Perfil
        </Tabs.Trigger>
        <Tabs.Trigger value="password" className="w-full">
          Senha
        </Tabs.Trigger>
      </Tabs.List>

      <Tabs.Content value="data">conteúdo 'Meus dados'</Tabs.Content>
      <Tabs.Content value="profile">conteúdo 'Perfil'</Tabs.Content>
      <Tabs.Content value="password">conteúdo 'Senha'</Tabs.Content>
    </Tabs.Root>
  )
}
