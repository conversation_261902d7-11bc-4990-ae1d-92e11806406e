import React from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import { fn } from '@storybook/test'

import { Badge, BadgeRootProps } from '@/@core/presentation/shared/ui/badge'
import { Icon as IconOld, faCog } from '@/@core/presentation/shared/ui/icon'

import '@/assets/css/app.scss'

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  component: Badge.Root,
  title: 'UI/Badge',
  parameters: {
    // layout: 'centered'
  },
  tags: ['autodocs'],
  argTypes: {
    children: {
      name: 'children',
      type: 'string'
    },
    variant: {
      name: 'variant',
      control: { type: 'select' },
      type: 'string',
      options: ['default', 'success', 'error', 'alert', 'processing']
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg']
    }
  },
  args: {
    children: 'Badge',
    variant: 'default',
    size: 'md'
  },
  render: Render
} satisfies Meta<typeof Badge.Root>

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {}
interface RenderProps
  extends BadgeRootProps,
    React.RefAttributes<
      HTMLButtonElement & {
        Icon: React.ReactNode
      }
    > {}

function Render({ children, variant, ...args }: RenderProps) {
  return (
    <div className="flex flex-wrap gap-2">
      <Badge.Root {...args} variant={variant}>
        <Badge.Content>{children}</Badge.Content>
      </Badge.Root>

      <Badge.Root
        {...args}
        variant={`${variant}-outline` as BadgeRootProps['variant']}
      >
        <Badge.Icon>
          <IconOld icon={faCog} />
        </Badge.Icon>
        <Badge.Content>{children}</Badge.Content>
      </Badge.Root>

      <Badge.Root
        {...args}
        variant={`${variant}-filled` as BadgeRootProps['variant']}
      >
        <Badge.Content>{children}</Badge.Content>
        <Badge.Icon>
          <IconOld icon={faCog} />
        </Badge.Icon>
      </Badge.Root>
    </div>
  )
}
