import { menuLayoutContent } from './menu.layout.content'

describe('menuLayoutContent', () => {
  test('should contain specific keys with expected properties', () => {
    expect(menuLayoutContent).toHaveProperty('dashboard')
    expect(menuLayoutContent.dashboard).toEqual({ order: 1, to: '/dashboard' })

    expect(menuLayoutContent).toHaveProperty('acl')
    expect(menuLayoutContent.acl).toEqual({ order: 2, to: '' })
  })

  test('should have only valid order values (number or null)', () => {
    Object.values(menuLayoutContent).forEach((item) => {
      expect(typeof item.order === 'number' || item.order === null).toBe(true)
    })
  })

  test('should have valid `to` properties as non-empty strings', () => {
    Object.values(menuLayoutContent).forEach((item) => {
      expect(typeof item.to).toBe('string')
      expect(item.to).toBeDefined()
    })
  })

  test('should contain optional properties correctly', () => {
    Object.values(menuLayoutContent).forEach((item) => {
      if (item.beta !== undefined) {
        expect(typeof item.beta).toBe('boolean')
      }
      if (item.slug !== undefined) {
        expect(typeof item.slug).toBe('string')
      }
    })
  })

  test('should have unique keys', () => {
    const keys = Object.keys(menuLayoutContent)
    const uniqueKeys = new Set(keys)
    expect(uniqueKeys.size).toBe(keys.length)
  })
})
