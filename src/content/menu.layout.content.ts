type MenuItem = {
  order: number | null
  to: string
  beta?: boolean
  slug?: string
  onlyDesktop?: boolean
}
type Menu = Record<string, MenuItem>

export const menuLayoutContent: Menu = {
  dashboard: {
    order: 1,
    to: '/dashboard'
  },
  // dashboardBeta: {
  //   order: 1,
  //   to: '/dashboard/beta',
  //   icon: 'fal fa-chart-bar',
  // },
  acl: {
    order: 2,
    to: ''
  },
  contracts: {
    to: '/contracts',
    order: null
  },
  'physical-assets': {
    to: '/physical-assets',
    order: null
  },
  'agents-ccee': {
    to: '/agents-ccee',
    order: null
  },
  'energetic-statement': {
    to: '/energetic-statement',
    order: null
  },
  'financial-statement': {
    to: '/financial-statement',
    order: null
  },
  'installation-document': {
    to: '/installation-documents',
    order: null
  },
  apportionments: {
    order: 3,
    to: '/apportionments'
  },
  operation: {
    order: 4,
    to: ''
  },
  'monitor-alarms': {
    to: '/monitor-alarms',
    order: null
  },
  'probes-map': {
    to: '',
    order: null
  },
  map: {
    to: '/probes-map',
    order: null
  },
  properties: {
    to: '/properties',
    order: null
  },
  statistics: {
    to: '/statistics/productivity',
    order: null
  },
  monitor: {
    to: '/monitoring',
    order: null,
    beta: false
  },
  reports: {
    order: 6,
    to: '/reports',
    onlyDesktop: true
  },
  register: {
    order: 7,
    to: ''
  },
  alarms: {
    to: '/alarms',
    order: 5
  },
  'register-company': {
    to: '/companies',
    order: null
  },
  'register-equipment': {
    to: '/equipments',
    order: null
  },
  'register-sector': {
    to: '/sectors',
    order: null
  },
  'register-device': {
    to: '/devices',
    order: null
  },
  users: {
    to: '/users',
    order: null
  },
  management: {
    order: 9,
    to: ''
  },
  accounts: {
    to: '/accounts',
    order: null
  },
  distributors: {
    to: '/distributors',
    order: null
  },

  // PARSE ATÉ OBTER O ENDPOINT DO MENU
  ACL: {
    to: '',
    order: null,
    slug: 'acl'
  },
  'Balanço Financeiro': {
    to: '',
    order: null,
    slug: 'financial-statement'
  },
  Cadastros: {
    to: '',
    order: null,
    slug: 'register'
  },
  Contratos: {
    to: '',
    order: null,
    slug: 'contracts'
  },
  Dashboard: {
    to: '',
    order: null,
    slug: 'dashboard'
  },
  Gestão: {
    to: '',
    order: null,
    slug: 'management'
  },
  Operação: {
    to: '',
    order: null,
    slug: 'operation'
  },
  Unidades: {
    to: '',
    order: null,
    slug: 'physical-assets'
  },
  Dispositivo: {
    to: '',
    order: null,
    slug: 'register-device'
  },
  Equipamento: {
    to: '',
    order: null,
    slug: 'register-equipment'
  },
  Empresa: {
    to: '',
    order: null,
    slug: 'register-company'
  },
  Usuários: {
    to: '',
    order: null,
    slug: 'users'
  },
  Contas: {
    to: '',
    order: null,
    slug: 'accounts'
  },
  Rateios: {
    to: '',
    order: null,
    slug: 'apportionments'
  },
  Relatórios: {
    to: '',
    order: null,
    slug: 'reports'
  },
  Alarme: {
    to: '',
    order: null,
    slug: 'alarms'
  },
  Mapa: {
    to: '',
    order: null,
    slug: 'map'
  },
  Monitoramento: {
    to: '',
    order: null,
    slug: 'monitoring'
  },
  'Agentes CCEE': {
    to: '',
    order: null,
    slug: 'agents-ccee'
  },
  'Balanço Energético': {
    to: '',
    order: null,
    slug: 'energetic-statement'
  },
  integrations: {
    to: '/integrations',
    order: null,
    slug: 'integrations'
  }
}
