type IMapMenuGlobalGroup =
  | 'zordon'
  | 'powerview'
  | 'dmc-view'
  | 'api-contract-management'
  | 'app-assinatura-solar'
  | 'hub-de-dados'

type IMapMenuGlobalLinks = Record<string, string>

export type IMapMenuGlobal = {
  name: string
  group: IMapMenuGlobalGroup
  links: IMapMenuGlobalLinks
}[]

export const mapMenuGlobal: IMapMenuGlobal = [
  {
    name: 'Telemetria',
    group: 'zordon',
    links: {
      hmg: 'https://hmg.zordon.app/',
      prod: 'https://zordon.app/'
    }
  },
  {
    name: 'Mercado Livre de Energia',
    group: 'powerview',
    links: {
      hmg: 'https://powerview-poc-cognito.comerc.com.br/',
      prod: 'https://powerview.comerc.com.br/'
    }
  },
  {
    name: 'Assinatura Solar - DMC View',
    group: 'dmc-view',
    links: {
      hmg: 'https://hmg-souvagalume-engine.dmcview.com.br',
      prod: 'https://souvagalume-engine.dmcview.com.br'
    }
  },
  {
    name: 'Cadastros Internos - Mercado livre',
    group: 'api-contract-management',
    links: {
      hmg: 'https://cadastrosinternos-dev.comerc.com.br/',
      prod: ''
    }
  },
  {
    name: 'Assinatura Solar - APP',
    group: 'app-assinatura-solar',
    links: {
      hmg: 'https://hmg-app.assinaturasolar.comerc.com.br',
      prod: 'https://app.assinaturasolar.comerc.com.br'
    }
  },
  {
    name: 'Hub de dados',
    group: 'hub-de-dados',
    links: {
      hmg: 'https://hubdedados.comerc.com.br',
      prod: 'https://hubdedados.comerc.com.br'
    }
  }
]
