import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listCompanyCategories'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListCompanyCategoriesStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listCompanyCategories)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
