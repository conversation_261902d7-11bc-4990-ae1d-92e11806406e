import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useAuthStore from './useAuthStore'

it('should call method setMe', () => {
  const { result } = renderHook(() => useAuthStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.me.user.id).toBeNull()

  act(() => {
    result.current.setMe({
      admin: false,
      user: {
        id: 1,
        name: '<PERSON><PERSON><PERSON><PERSON>',
        avatar: '',
        cellphone: '',
        email: '',
        active: false,
        admin: false
      },
      accountId: null,
      accountName: '',
      accountUserId: null,
      accountUserAdmin: false
    })
  })

  expect(result.current.state.me.user.id).toBe(1)
})
