import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/pageAccounts'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

import { pageAccountsCookie } from '@/@core/infra/memory/cookie'

export default function usePageAccountsStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.pageAccounts)

  function setLoading(value: ReducerPayloadProps['loading']) {
    dispatch(actions.setLoading(value))
  }

  function setFormSearch(value: Partial<ReducerPayloadProps['setFormSearch']>) {
    const newSearch: ReducerPayloadProps['setFormSearch'] = {
      ...state.formSearch,
      ...value
    }

    pageAccountsCookie.set(newSearch)

    dispatch(actions.setFormSearch(newSearch))
  }

  function setTable(values: ReducerPayloadProps['setTable']) {
    pageAccountsCookie.set({
      limit: values.limit,
      page: values.page
    })

    dispatch(
      actions.setTable({
        items: values.items,
        total: values.total,
        page: values.page,
        limit: values.limit,
        lastPage: values.lastPage
      })
    )
  }

  function setTablePage(value: ReducerPayloadProps['setTable']['page']) {
    pageAccountsCookie.set({
      limit: state.table.limit,
      page: value
    })

    dispatch(actions.setTablePage(value))
  }

  function setTableLimit(value: ReducerPayloadProps['setTable']['limit']) {
    pageAccountsCookie.set({
      limit: value,
      page: state.table.page
    })

    dispatch(actions.setTableLimit(value))
  }

  return {
    state,
    setLoading,
    setFormSearch,
    setTable,
    setTablePage,
    setTableLimit
  }
}
