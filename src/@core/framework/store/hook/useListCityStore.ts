import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listCities'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListCityStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listCity)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
