import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListApportionmentTariffTypes from './useListApportionmentTariffTypes'

it('should call method set', () => {
  const { result } = renderHook(() => useListApportionmentTariffTypes(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [
        {
          id: 1,
          name: 'Cat<PERSON>'
        }
      ]
    })
  })

  expect(result.current.state.list).toHaveLength(1)
})
