import { renderHook } from '@testing-library/react'

import { act } from 'react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListCompanyCategoryStore from './useListCompanyCategoryStore'

it('should call method set', () => {
  const { result } = renderHook(() => useListCompanyCategoryStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [
        {
          id: 1,
          name: 'name 1'
        }
      ]
    })
  })

  expect(result.current.state.list).toHaveLength(1)
})
