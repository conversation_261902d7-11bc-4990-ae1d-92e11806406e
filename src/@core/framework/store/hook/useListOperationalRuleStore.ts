import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listOperationalRule'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListOperationalRuleStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listOperationalRule)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
