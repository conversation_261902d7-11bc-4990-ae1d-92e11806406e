import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListApportionmentsFeeDivisionTypes from './useListApportionmentsFeeDivisionTypes'

it('should call method set', () => {
  const { result } = renderHook(() => useListApportionmentsFeeDivisionTypes(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [
        {
          id: 1,
          name: 'Proporcional pelo consumo'
        }
      ]
    })
  })

  expect(result.current.state.list).toHaveLength(1)
})
