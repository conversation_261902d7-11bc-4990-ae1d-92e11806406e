import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import usePageAccountsIdStore from './usePageAccountsIdStore'

it('should call methods', () => {
  const { result } = renderHook(() => usePageAccountsIdStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.form.name).toBe('')
  expect(result.current.state.tabs.index).toBe(0)

  act(() => {
    result.current.setForm({
      name: 'name test'
    })
    result.current.setTabActive(1)
  })

  expect(result.current.state.form.name).toBe('name test')
  expect(result.current.state.tabs.index).toBe(1)
})
