import { act, renderHook } from '@testing-library/react'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useSystemToastStore from './useSystemToastStore'
import { Toast } from '@/types/system/toast'

it('should call methods', () => {
  const { result } = renderHook(() => useSystemToastStore(), {
    wrapper: AppStoreProvider
  })

  let toast: Toast

  // count toasts if iqual 0
  expect(result.current.state.toasts).toHaveLength(0)

  // add toast
  act(() => {
    result.current.addToast({
      message: 'message success'
    })
  })
  act(() => {
    toast = result.current.addToast({
      message: 'message error'
    })
  })

  // count toasts if iqual length 2
  expect(result.current.state.toasts).toHaveLength(2)

  // set hidden to toast created
  act(() => {
    result.current.hiddenToast(toast.uuid)
  })

  // remove to toast created
  act(() => {
    result.current.removeToast(toast.uuid)
  })

  // count toasts if iqual length 0 after remove
  expect(result.current.state.toasts).toHaveLength(1)

  act(() => {
    result.current.reset()
  })

  expect(result.current.state.toasts).toHaveLength(0)
})
