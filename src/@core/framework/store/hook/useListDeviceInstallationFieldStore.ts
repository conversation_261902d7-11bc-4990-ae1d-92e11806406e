import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listDeviceInstallationFields'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListDeviceInstallationFieldStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listDeviceInstallationFields)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
