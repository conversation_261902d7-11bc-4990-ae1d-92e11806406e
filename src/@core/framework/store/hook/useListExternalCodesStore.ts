import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listExternalCodes'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListExternalCodesStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listExternalCodes)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
