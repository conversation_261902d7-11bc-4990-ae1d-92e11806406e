import { act, renderHook } from '@testing-library/react'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useSystemLoadingStore from './useSystemLoadingStore'

it('should call method setLoading', () => {
  const { result } = renderHook(() => useSystemLoadingStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.loading).toBe(false)

  act(() => {
    result.current.setLoading(true)
  })

  expect(result.current.state.loading).toBe(true)

  act(() => {
    result.current.setData({ pageLoading: true, loading: false })
  })

  expect(result.current.state.pageLoading).toBe(true)
  expect(result.current.state.loading).toBe(false)
})
