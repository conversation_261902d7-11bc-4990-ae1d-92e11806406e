import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListTypeSubTypeGroupedStore from './useListTypeSubTypeGroupedStore'

it('should call method set', () => {
  const { result } = renderHook(() => useListTypeSubTypeGroupedStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [
        {
          label: 'teste',
          value: '1',
          options: [
            {
              label: 'teste',
              value: '1'
            }
          ]
        }
      ]
    })
  })

  expect(result.current.state.list).toHaveLength(1)
})
