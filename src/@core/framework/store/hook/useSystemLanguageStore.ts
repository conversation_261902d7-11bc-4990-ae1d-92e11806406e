import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/systemLanguage'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useSystemLanguageStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.systemLanguage)

  const setLanguage = (lang: ReducerPayloadProps['setLanguage']) => {
    let newValue = lang

    if (String(lang) === 'pt-br') {
      newValue = 'pt-BR'
    }
    dispatch(actions.setLanguage(newValue))
  }

  return {
    state,
    setLanguage
  }
}
