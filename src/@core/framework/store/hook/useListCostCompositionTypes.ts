import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listCostCompositionTypes'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListCostCompositionTypes() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listCostCompositionTypes)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
