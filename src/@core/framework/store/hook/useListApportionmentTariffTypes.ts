import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listApportionmentTariffTypes'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListApportionmentTariffTypes() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listApportionmentTariffTypes)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
