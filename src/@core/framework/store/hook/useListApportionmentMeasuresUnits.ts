import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listApportionmenttMeasuresUnits'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListApportionmentMeasuresUnits() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listApportionmenttMeasuresUnits)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
