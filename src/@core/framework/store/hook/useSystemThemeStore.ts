import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/systemTheme'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useSystemThemeStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.systemTheme)

  const setTheme = (theme: ReducerPayloadProps['setTheme']) => {
    dispatch(actions.setTheme(theme))
  }

  return { state, setTheme }
}
