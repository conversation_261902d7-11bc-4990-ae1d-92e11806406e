import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listIntegrationsFrequencies'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListIntegrationsFrequenciesStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listIntegrationsFrequencies)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
