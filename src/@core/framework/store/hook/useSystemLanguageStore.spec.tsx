import { act, renderHook } from '@testing-library/react'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useSystemLanguageStore from './useSystemLanguageStore'

it('should call method setLanguage', () => {
  const { result } = renderHook(() => useSystemLanguageStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.lang).toBe('pt-BR')

  act(() => {
    result.current.setLanguage('en')
  })

  expect(result.current.state.lang).toBe('en')
})
