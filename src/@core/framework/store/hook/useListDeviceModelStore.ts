import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listDeviceModel'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListDeviceModelStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listDeviceModel)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
