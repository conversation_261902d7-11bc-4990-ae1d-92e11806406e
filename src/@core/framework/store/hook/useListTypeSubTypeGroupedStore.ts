import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listTypeSubtypeGrouped'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListTypeSubTypeGroupedStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listTypeSubTypeGrouped)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
