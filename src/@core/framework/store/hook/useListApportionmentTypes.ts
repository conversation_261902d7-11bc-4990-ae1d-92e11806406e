import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listApportionmentTypes'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListApportionmentTypes() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listApportionmentTypes)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
