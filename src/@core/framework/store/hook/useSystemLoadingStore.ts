import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/systemLoading'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useSystemLoadingStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.systemLoading)

  function setLoading(payload: ReducerPayloadProps['setLoading']) {
    dispatch(actions.setLoading(payload))
  }

  function setData(payload: ReducerPayloadProps['setData']) {
    dispatch(actions.setData(payload))
  }

  return {
    state,
    setLoading,
    setData
  }
}
