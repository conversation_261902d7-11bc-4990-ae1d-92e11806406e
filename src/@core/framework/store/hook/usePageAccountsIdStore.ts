import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/pageAccountsId'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function usePageAccountIdStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.pageAccountsId)

  function setForm(values: Partial<ReducerPayloadProps['setForm']>) {
    dispatch(
      actions.setForm({
        ...state.form,
        ...values
      })
    )
  }
  function setTabActive(value: ReducerPayloadProps['setTabActive']) {
    dispatch(actions.setTabActive(value))
  }

  return {
    state,
    setForm,
    setTabActive
  }
}
