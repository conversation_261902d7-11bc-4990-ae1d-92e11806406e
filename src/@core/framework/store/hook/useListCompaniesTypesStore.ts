import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listCompaniesTypes'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListCompaniesTypesStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listCompaniesTypes)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
