import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listSubType'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListSubtypeStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listSubType)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
