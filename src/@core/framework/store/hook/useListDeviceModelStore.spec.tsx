import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListDeviceModelStore from './useListDeviceModelStore'

it('should call method set', () => {
  const { result } = renderHook(() => useListDeviceModelStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [
        {
          id: 1,
          model: 'model 1'
        }
      ]
    })
  })

  expect(result.current.state.list).toHaveLength(1)
})
