import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListSubmarketStore from './useListSubmarketStore'

it('should call method set', () => {
  const { result } = renderHook(() => useListSubmarketStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [
        {
          id: 1,
          name: 'Sul',
          acronyms: 'S'
        }
      ]
    })
  })

  expect(result.current.state.list).toHaveLength(1)
})
