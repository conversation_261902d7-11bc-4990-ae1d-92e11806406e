import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listSubmarket'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListSubmarketStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listSubmarket)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
