import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/systemBreadcrumbs'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'
export default function useSystemBreadcrumbStore() {
  const dispatch = useAppDispatch()
  const state = useAppSelector((e) => e.systemBreadcrumb)
  function setBreadcrumbs(value: ReducerPayloadProps['setData']) {
    dispatch(actions.setData(value))
  }
  return {
    state,
    setBreadcrumbs
  }
}
