import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useSystemStore from './useSystemStore'
import { menuItem1 } from '@/__mock__/content/menu.layout.contents'

it('should call method setMenuItems', () => {
  const { result } = renderHook(() => useSystemStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.menuItems).toHaveLength(0)

  act(() => {
    result.current.setMenuItems([menuItem1])
  })

  expect(result.current.state.menuItems).toHaveLength(1)
})

it('should call method setStatusMenuDesktop', () => {
  const { result } = renderHook(() => useSystemStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.statusMenuDesktop).toBe('close')

  act(() => {
    result.current.setStatusMenuDesktop('open')
  })

  expect(result.current.state.statusMenuDesktop).toBe('open')
})

it('should call method setStatusMenuMobile', () => {
  const { result } = renderHook(() => useSystemStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.statusMenuMobile).toBe('close')

  act(() => {
    result.current.setStatusMenuMobile('open')
  })

  expect(result.current.state.statusMenuMobile).toBe('open')
})
