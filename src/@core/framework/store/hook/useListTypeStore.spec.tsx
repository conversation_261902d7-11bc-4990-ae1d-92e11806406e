import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListTypeStore from './useListTypeStore'

it('should call method set', () => {
  const { result } = renderHook(() => useListTypeStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [
        {
          id: 1,
          name: 'Energia'
        }
      ]
    })
  })

  expect(result.current.state.list).toHaveLength(1)
})
