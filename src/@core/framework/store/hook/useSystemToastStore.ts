import { actions } from '@/@core/framework/plugins/redux/features/systemToast'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

import { uuidv4 } from '@/config/plugins'
import { Toast } from '@/types/system/toast'

export default function useSystemToastStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.systemToast)

  function reset() {
    dispatch(actions.setToast([]))
  }
  function addToast(args: IAddToastProps) {
    const uuid = uuidv4()

    const {
      message,
      duration = 'default',
      position = 'topRight',
      type = 'success'
    } = args

    const newToast: Toast = {
      uuid,
      message,
      position,
      type,
      duration,
      status: 'show'
    }

    dispatch(actions.setToast([...state.toasts, newToast]))

    return newToast
  }
  function removeToast(uuid: IRemoveToastProps) {
    const list = [...state.toasts].filter((el) => el.uuid !== uuid)
    dispatch(actions.setToast(list))
  }
  function hiddenToast(uuid: IHiddenToastProps) {
    const list = [...state.toasts].map((el) => ({
      ...el,
      status: el.uuid === uuid ? 'hidden' : el.status
    }))
    dispatch(actions.setToast(list))
  }

  return {
    state,
    reset,
    addToast,
    hiddenToast,
    removeToast
  }
}

export type IAddToastProps = {
  message: Toast['message']
  position?: Toast['position']
  duration?: Toast['duration']
  type?: Toast['type']
}

type IRemoveToastProps = Toast['uuid']

type IHiddenToastProps = Toast['uuid']
