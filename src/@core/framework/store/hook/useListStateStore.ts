import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listState'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListStateStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listState)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
