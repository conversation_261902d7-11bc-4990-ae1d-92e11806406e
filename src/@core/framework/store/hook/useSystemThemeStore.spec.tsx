import { act, renderHook } from '@testing-library/react'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useSystemThemeStore from './useSystemThemeStore'

it('should call method setTheme', () => {
  const { result } = renderHook(() => useSystemThemeStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.theme).toBe('light')

  act(() => {
    result.current.setTheme('dark')
  })

  expect(result.current.state.theme).toBe('dark')
})
