import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/auth'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useAuthStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.auth)

  function setMe(value: ReducerPayloadProps['setMe']) {
    dispatch(actions.setMe(value))
  }

  const isSuperAdmin = !!state.me?.user?.admin

  return {
    state: { ...state, isSuperAdmin },
    setMe
  }
}
