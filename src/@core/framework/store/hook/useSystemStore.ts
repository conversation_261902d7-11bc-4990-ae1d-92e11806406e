import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/system'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useSystemStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.system)

  function setStatusMenuDesktop(
    values: ReducerPayloadProps['setStatusMenuDesktop']
  ) {
    dispatch(actions.setStatusMenuDesktop(values))
  }
  function setStatusMenuMobile(
    values: ReducerPayloadProps['setStatusMenuMobile']
  ) {
    dispatch(actions.setStatusMenuMobile(values))
  }
  function setMenuItems(values: ReducerPayloadProps['setMenuItems']) {
    dispatch(actions.setMenuItems(values))
  }
  function setPermissions(values: ReducerPayloadProps['setPermissions']) {
    dispatch(actions.setPermissions(values))
  }
  function setMountComponent(key: string) {
    dispatch(actions.setMountComponent({ key, value: true }))
  }
  function setUnmountComponent(key: string) {
    dispatch(actions.setMountComponent({ key, value: false }))
  }

  return {
    state,
    setStatusMenuDesktop,
    setStatusMenuMobile,
    setMenuItems,
    setPermissions,
    setMountComponent,
    setUnmountComponent
  }
}
