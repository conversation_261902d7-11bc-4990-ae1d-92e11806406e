import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listApportionmentsFeeDivisionTypes'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListApportionmentsFeeDivisionTypes() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listApportionmentsFeeDivisionTypes)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
