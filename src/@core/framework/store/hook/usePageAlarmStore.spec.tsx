import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import usePageAlarmStore from './usePageAlarmStore'

it('should call methods', () => {
  const { result } = renderHook(() => usePageAlarmStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.loading).toBe(false)
  expect(result.current.state.formSearch.q).toBe('')
  expect(result.current.state.table.page).toBe(0)
  expect(result.current.state.table.limit).toBe(0)
  expect(result.current.state.table.lastPage).toBe(0)

  act(() => {
    result.current.setLoading(true)
    result.current.setFormSearch({
      q: 'name test'
    })
    result.current.setTable({
      items: [],
      total: 0,
      page: 0,
      limit: 0,
      lastPage: 2
    })
    result.current.setTablePage(1)
    result.current.setTableLimit(15)
  })

  expect(result.current.state.loading).toBe(true)
  expect(result.current.state.formSearch.q).toBe('name test')
  expect(result.current.state.table.page).toBe(1)
  expect(result.current.state.table.limit).toBe(15)
  expect(result.current.state.table.lastPage).toBe(2)
})
