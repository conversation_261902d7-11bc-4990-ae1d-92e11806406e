import { act } from 'react'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import useListExternalCodesStore from './useListExternalCodesStore'
import { renderHook } from '@testing-library/react'

it('should call method set', () => {
  const { result } = renderHook(() => useListExternalCodesStore(), {
    wrapper: AppStoreProvider
  })

  expect(Object.keys(result.current.state.list)).toHaveLength(2)

  act(() => {
    result.current.set({
      list: {
        comerc: 'comerc',
        zordon: 'zordon'
      }
    })
  })

  expect(Object.keys(result.current.state.list)).toHaveLength(2)

  // Verifique se os valores estão corretos
  expect(result.current.state.list.comerc).toBe('comerc')
  expect(result.current.state.list.zordon).toBe('zordon')
})
