import { act, renderHook } from '@testing-library/react'

import { AppStoreProvider } from '@/provider/AppStoreProvider'

import useListSubtypeStore from './useListSubtypeStore'

it('should call method set', () => {
  const { result } = renderHook(() => useListSubtypeStore(), {
    wrapper: AppStoreProvider
  })

  expect(result.current.state.list).toHaveLength(0)
  expect(result.current.state.listGrouped).toHaveLength(0)

  act(() => {
    result.current.set({
      list: [
        {
          id: 1,
          name: '<PERSON><PERSON>',
          type: { id: 1, name: 'Energia' }
        }
      ],
      listGrouped: [
        {
          id: 1,
          name: 'Energia',
          subTypes: [
            {
              id: 1,
              name: '<PERSON><PERSON>'
            }
          ]
        }
      ]
    })
  })

  expect(result.current.state.list).toHaveLength(1)
  expect(result.current.state.listGrouped).toHaveLength(1)
})
