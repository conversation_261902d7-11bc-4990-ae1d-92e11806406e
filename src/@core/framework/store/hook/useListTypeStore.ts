import {
  actions,
  ReducerPayloadProps
} from '@/@core/framework/plugins/redux/features/listType'
import {
  useAppDispatch,
  useAppSelector
} from '@/@core/framework/plugins/redux/store'

export default function useListTypeStore() {
  const dispatch = useAppDispatch()

  const state = useAppSelector((e) => e.listType)

  function set(values: ReducerPayloadProps['set']) {
    dispatch(actions.set(values))
  }

  return { state, set }
}
