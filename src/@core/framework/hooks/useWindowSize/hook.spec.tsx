import React from 'react'
import { cleanup, fireEvent, waitFor } from '@testing-library/react'

import { render } from '@/utils/setupTest'

import { useWindowSize } from './hook'

cleanup()

// font, https://jestjs.io/docs/timer-mocks#enable-fake-timers
jest.useFakeTimers()

const useStateSpy = jest.spyOn(require('react'), 'useState')

describe('src/hooks/useWindowSize', () => {
  let windowSize: {
    minMobile: boolean
    maxMobile: boolean
  }

  it('onResize', async () => {
    const setSizeMock = jest.fn()

    useStateSpy.mockReturnValueOnce([
      {
        width: 1024,
        height: 768
      },
      setSizeMock
    ])

    function ComponentScope() {
      windowSize = useWindowSize()
      return null
    }

    const { unmount } = render(<ComponentScope />)

    expect(windowSize.minMobile).toBe(true)
    expect(windowSize.maxMobile).toBe(false)

    waitFor(() => {
      window.innerHeight = 500
      window.innerWidth = 500

      fireEvent(window, new Event('resize'))
    })

    // Fast-forward until all timers have been executed
    jest.runAllTimers()

    expect(setSizeMock).toBeCalledTimes(1)

    unmount()
  })
})
