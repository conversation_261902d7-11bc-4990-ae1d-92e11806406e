import React from 'react'

export const useWindowSize = () => {
  const [size, setSize] = React.useState({
    width: window.innerWidth,
    height: window.innerHeight
  })

  const time = React.useRef<NodeJS.Timeout>()

  React.useEffect(() => {
    const onResize = () => {
      clearTimeout(time.current)

      time.current = setTimeout(() => {
        setSize({
          width: window.innerWidth,
          height: window.innerHeight
        })
      }, 75)
    }

    window.addEventListener('resize', onResize)

    return () => {
      window.removeEventListener('resize', onResize)
    }
  }, [])

  const screens: Record<string, number> = React.useMemo(
    () => ({
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      '2xl': 1536
    }),
    []
  )

  const minExtraLarge = React.useMemo(
    () => size.width > screens['xl'],
    [size.width]
  )
  const maxExtraLarge = React.useMemo(
    () => size.width <= screens['xl'],
    [size.width]
  )
  const minMobile = React.useMemo(
    () => size.width > screens['sm'],
    [size.width]
  )
  const maxMobile = React.useMemo(
    () => size.width <= screens['sm'],
    [size.width]
  )

  const middle = minMobile && maxExtraLarge

  return {
    size,
    minMobile,
    maxMobile,
    middle,
    minExtraLarge,
    maxExtraLarge
  }
}
