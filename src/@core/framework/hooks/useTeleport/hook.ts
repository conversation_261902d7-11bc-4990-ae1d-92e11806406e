import { FC, ReactNode, useContext, useEffect } from 'react'
import { TeleportContext } from '@/context/teleportContext'
import { TeleportType } from '@/types/teleport'
import { observer } from '@/@core/domain/observer'
import { teleportModalObserver } from '@/@core/domain/observer/teleportObserver'

export const TeleportIn: FC<{
  children: ReactNode
  teleportType: TeleportType
}> = (props) => {
  const { addTeleportItem, removeTeleportItem } = useContext(TeleportContext)

  useEffect(() => {
    addTeleportItem(props.teleportType, props.children)

    return () => removeTeleportItem(props.teleportType)
  }, [])

  return null
}
export const TeleportOut: FC<{ teleportType: TeleportType }> = (props) => {
  const { teleportMap } = useContext(TeleportContext)

  const content = teleportMap.get(props.teleportType) as JSX.Element

  return content || null
}

export const useTeleport = () => {
  const { addTeleportItem, removeTeleportItem } = useContext(TeleportContext)

  function addElement(value: { element: ReactNode; portalType: TeleportType }) {
    if (value.portalType === 'modal') {
      observer.publish(teleportModalObserver('open'))
    }

    addTeleportItem(value.portalType, value.element)
  }
  function removeElement(value: { portalType: TeleportType }) {
    if (value.portalType === 'modal') {
      observer.publish(teleportModalObserver('close'))
      return
    }

    removeTeleportItem(value.portalType)
  }

  return { TeleportIn, TeleportOut, addElement, removeElement }
}
