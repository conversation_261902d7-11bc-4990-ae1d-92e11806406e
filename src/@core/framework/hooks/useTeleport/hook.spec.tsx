import { ReactNode } from 'react'
import { cleanup, render, renderHook } from '@testing-library/react'
import { TeleportType } from '@/types/teleport'
import { TeleportContext } from '@/context/teleportContext'
import { act } from 'react'
import { useTeleport, TeleportIn, TeleportOut } from './hook'

cleanup()

const mockAddTeleportItem = jest.fn()
const mockRemoveTeleportItem = jest.fn()
const mockTeleportMap = new Map()

type Props = {
  children: ReactNode
}

const wrapper: React.FC<Props> = ({ children }) => (
  <TeleportContext.Provider
    value={{
      addTeleportItem: mockAddTeleportItem,
      removeTeleportItem: mockRemoveTeleportItem,
      teleportMap: mockTeleportMap
    }}
  >
    {children}
  </TeleportContext.Provider>
)

describe('useTeleport hook', () => {
  it('should call addTeleportItem and removeTeleportItem', () => {
    const { result } = renderHook(() => useTeleport(), {
      wrapper
    })

    const testElement = <div>Test</div>
    const testType: TeleportType = 'titlePage' // Adjust as per your TeleportType values

    act(() => {
      result.current.addElement({ element: testElement, portalType: testType })
    })
    expect(mockAddTeleportItem).toHaveBeenCalledWith(testType, testElement)

    act(() => {
      result.current.removeElement({ portalType: testType })
    })
    expect(mockRemoveTeleportItem).toHaveBeenCalledWith(testType)
  })
})

describe('TeleportIn component', () => {
  afterEach(() => {
    mockAddTeleportItem.mockClear()
    mockRemoveTeleportItem.mockClear()
  })

  it('should call addTeleportItem on mount and removeTeleportItem on unmount', () => {
    const { unmount } = render(
      <TeleportIn teleportType="titlePage">Test</TeleportIn>,
      { wrapper }
    )

    expect(mockAddTeleportItem).toHaveBeenCalled()

    unmount()
    expect(mockRemoveTeleportItem).toHaveBeenCalled()
  })
})

describe('TeleportOut component', () => {
  it('should render the content from teleportMap', () => {
    const testElement = <div>Test Content</div>
    mockTeleportMap.set('testType', testElement)

    const { container } = render(<TeleportOut teleportType="titlePage" />, {
      wrapper
    })

    expect(container).toContainHTML('<div />')
  })
})
