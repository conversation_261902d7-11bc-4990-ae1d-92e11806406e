import { useCallback } from 'react'

interface CurrencyFormatOptions {
  locale: string
  currency: string
  useGrouping?: boolean
  showCurrencySymbol?: boolean
}

interface DecimalFormatOptions {
  fractionDigits?: number
}

type FormatCurrency = (
  value: number | string,
  options: CurrencyFormatOptions
) => string

type FormatDecimal = (
  value: number | string,
  options?: DecimalFormatOptions
) => string

export const useNumberFormatter = () => {
  const currencyFormat: FormatCurrency = useCallback((value, options) => {
    const { locale, currency, useGrouping = true } = options

    let numberValue = 0
    if (typeof value === 'number' && !isNaN(value)) numberValue = value
    if (
      typeof value === 'string' &&
      value.trim() !== '' &&
      !isNaN(Number(value))
    )
      numberValue = Number(value)

    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      useGrouping
    }).format(numberValue)
  }, [])

  const decimalFormat: FormatDecimal = useCallback((value, options = {}) => {
    const { fractionDigits = 2 } = options
    let numberValue = 0
    if (typeof value === 'number' && !isNaN(value)) numberValue = value
    if (
      typeof value === 'string' &&
      value.trim() !== '' &&
      !isNaN(Number(value))
    )
      numberValue = Number(value)
    return numberValue.toFixed(fractionDigits)
  }, [])

  return { currencyFormat, decimalFormat }
}
