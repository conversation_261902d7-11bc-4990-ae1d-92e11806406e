import { renderHook } from '@testing-library/react'
import { useNumberFormatter } from './hook'

describe('useNumberFormatter', () => {
  const locale = 'en-US'
  const currency = 'USD'

  it('should format number as currency', () => {
    const { result } = renderHook(() => useNumberFormatter())
    expect(result.current.currencyFormat(1234.56, { locale, currency })).toBe(
      '$1,234.56'
    )
  })

  it('should format string as currency', () => {
    const { result } = renderHook(() => useNumberFormatter())
    expect(result.current.currencyFormat('1234.56', { locale, currency })).toBe(
      '$1,234.56'
    )
  })

  it('should format number as currency with grouping disabled', () => {
    const { result } = renderHook(() => useNumberFormatter())
    expect(
      result.current.currencyFormat(1234.56, {
        locale,
        currency,
        useGrouping: false
      })
    ).toBe('$1234.56')
  })

  it('should format invalid number as $0.00', () => {
    const { result } = renderHook(() => useNumberFormatter())
    expect(result.current.currencyFormat('invalid', { locale, currency })).toBe(
      '$0.00'
    )
    expect(result.current.currencyFormat('', { locale, currency })).toBe(
      '$0.00'
    )
    expect(
      result.current.currencyFormat(undefined as any, { locale, currency })
    ).toBe('$0.00')
  })

  it('should format number as decimal with default fractionDigits', () => {
    const { result } = renderHook(() => useNumberFormatter())
    expect(result.current.decimalFormat(1234.567)).toBe('1234.57')
  })

  it('should format string as decimal with custom fractionDigits', () => {
    const { result } = renderHook(() => useNumberFormatter())
    expect(
      result.current.decimalFormat('1234.567', { fractionDigits: 1 })
    ).toBe('1234.6')
  })

  it('should format invalid decimal as 0.00', () => {
    const { result } = renderHook(() => useNumberFormatter())
    expect(result.current.decimalFormat('invalid')).toBe('0.00')
    expect(result.current.decimalFormat('')).toBe('0.00')
    expect(result.current.decimalFormat(undefined as any)).toBe('0.00')
  })
})
