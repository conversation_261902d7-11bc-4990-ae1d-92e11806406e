import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { authApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { appCookie } from '@/@core/infra/memory/cookie'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { redirectLoginCognito } from '@/@core/utils/redirectLoginCognito'
import { useRouter } from 'next/router'

type UseLogoutHook = () => () => Promise<void>

export const useLogout: UseLogoutHook = () => {
  const router = useRouter()
  const systemLoading = useSystemLoadingStore()
  const log = useLog()

  const logoutUser = async (): Promise<void> => {
    try {
      systemLoading.setData({ pageLoading: true })
      appCookie.reset()
      await authApiV3(http).logout()
      router.push(redirectLoginCognito())
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title:
          'logoutUser. método que chama o fluxo para logout do usuário - src/@core/framework/hooks/useLogout/hook.ts'
      })
    } finally {
      systemLoading.setData({ pageLoading: false })
    }
  }
  return logoutUser
}
