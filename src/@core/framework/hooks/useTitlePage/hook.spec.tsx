import { cleanup } from '@testing-library/react'

import { useTitlePage } from './hook'
import { renderWithRedux } from '@/utils/setupTest'

cleanup()

describe('src/hooks/useTitlePage/test.spec.ts', () => {
  const namePage = 'name page text'

  const ComponentTest = () => {
    useTitlePage(namePage)

    return null
  }

  test('change value window.document.title', () => {
    const rend = renderWithRedux(<ComponentTest />)

    expect(rend.container.ownerDocument.title).toBe(namePage)
  })
})
