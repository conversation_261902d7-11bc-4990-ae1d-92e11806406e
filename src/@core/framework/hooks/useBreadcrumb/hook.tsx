import { breadcrumbsContent } from '@/@core/content/breadcrumbs.content'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { breadcrumbsLanguage } from '@/@core/language/modes/breadcrumbs.language'
import { IBreadcrumbLink } from '@/types/system/breadcrumb'
import React from 'react'
import useSystemBreadcrumbStore from '../../store/hook/useSystemBreadcrumbStore'

export const useBreadcrumb = (pathKey: string = 'default') => {
  const languageStore = useSystemLanguageStore()
  const systemBreadcrumbStore = useSystemBreadcrumbStore()
  const mapLanguageLinks = ({ ...link }: IBreadcrumbLink): IBreadcrumbLink => {
    link.label = breadcrumbsLanguage[languageStore.state.lang][link.page]
    return link
  }
  React.useEffect(() => {
    const currentBreadcrumbs = breadcrumbsContent[pathKey]
    systemBreadcrumbStore.setBreadcrumbs(
      currentBreadcrumbs.map(mapLanguageLinks)
    )
  }, [languageStore.state.lang])
}
