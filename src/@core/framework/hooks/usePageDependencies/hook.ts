import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { useLog } from '@/@core/logging/logger'

export const usePageDependencies = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  return {
    systemLoading,
    systemToast,
    log
  }
}
