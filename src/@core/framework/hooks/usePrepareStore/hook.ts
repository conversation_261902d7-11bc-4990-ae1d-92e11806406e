import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'

import useAuthStore from '@/@core/framework/store/hook/useAuthStore'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { authApiV3, menuApiV3 } from '@/@core/infra/api'
import { httpServiceCognito } from '@/@core/infra/api-cognito'
import { authorizationCodeExchangeApi } from '@/@core/infra/api/CognitoAuthorizationCodeApi/AuthorizationCodeExchangeApi'
import { userActionsApiV3 } from '@/@core/infra/api/UserActionsApiV3'
import { http } from '@/@core/infra/http'
import { memoryApp } from '@/@core/infra/memory'
import {
  appCookie,
  authCookie,
  systemCookie
} from '@/@core/infra/memory/cookie'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { changeColorsThemeByElement } from '@/@core/utils'
import { groupsUserActionsToPermissions } from '@/@core/utils/groupsUserActionsToPermissions'
import { redirectLoginCognito } from '@/@core/utils/redirectLoginCognito'

export const usePrepareStore = () => {
  const router = useRouter()
  const log = useLog()

  const [isPending, setIsPending] = useState<boolean>(true)

  const authStore = useAuthStore()
  const systemStore = useSystemStore()
  const languageStore = useSystemLanguageStore()

  const hasHandlerRun = useRef(false)

  /** STEPS */
  const stepMemory = async () => {
    await new Promise<void>((resolve) => {
      memoryApp.init()
      appCookie.init()
      languageStore.setLanguage(systemCookie.get().language)

      resolve()
    })
  }
  const stepToken = async () => {
    await new Promise<void>((resolve) => {
      const auth = authCookie.get()

      if (!auth.token) {
        resolve()
        return
      }

      http.setToken(auth.token)

      resolve()
    })
  }
  const stepExchangeCode = async () => {
    const hasToken = !!authCookie.get()?.token

    if (hasToken) return

    const params = new URL(`${window.location.origin}${router.asPath}`)
      .searchParams

    const code = params.get('code')

    if (!code) {
      throw new Error('Não foi encontrado o parâmetro code')
    }

    const { data } = await authorizationCodeExchangeApi(
      httpServiceCognito
    ).login({
      code: String(code),
      clientId: process.env.NEXT_PUBLIC_NEXT_CLIENT_ID_COGNITO!,
      redirectUri: process.env.NEXT_PUBLIC_COGNITO_CALLBACK_URL!
    })

    authCookie.setToken({
      token: data.accessToken,
      code: data.code,
      expiresIn: data.expiresIn
    })

    http.setToken(data.accessToken)
  }
  const stepUserData = async () => {
    const resultMe = await authApiV3(http).me()
    const resultMenu = await menuApiV3(http).get()

    const resultUserActions = await userActionsApiV3(http).get({
      accountUserId: resultMe.data.accountUserId!
    })

    authStore.setMe(resultMe.data)
    systemStore.setMenuItems(resultMenu.data)
    systemStore.setPermissions(
      groupsUserActionsToPermissions(resultUserActions.data.items)
    )

    changeColorsThemeByElement(systemCookie.get().themeColor, 'html')
  }
  const stepLogout = async () => {
    const auth = authCookie.get()

    if (!auth?.token) return

    http.setToken(auth.token)

    try {
      await authApiV3(http).logout()
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'stepLogout - @core/framework/hooks/usePrepareStore/hook.ts'
      })
    }
  }

  async function handler() {
    try {
      await stepMemory()
      await stepToken()
      await stepExchangeCode()
      await stepUserData()

      if (router.pathname === '/login') router.push('/dashboard')

      setIsPending(false)
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'usePrepareStore - @core/framework/hooks/usePrepareStore/hook.ts'
      })
      appCookie.reset()
      await stepLogout()
      window.location.href = redirectLoginCognito()
    }
  }

  useEffect(() => {
    if (!authStore.state.me?.user?.id && hasHandlerRun.current === false) {
      hasHandlerRun.current = true
      handler()
    }
  }, [authStore.state.me?.user?.id])

  return { isPending }
}
