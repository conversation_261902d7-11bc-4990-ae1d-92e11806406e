import { CloudWatchLogsClient } from '@aws-sdk/client-cloudwatch-logs'
export {
  CreateLogStreamCommand,
  PutLogEventsCommand
} from '@aws-sdk/client-cloudwatch-logs'

/** Configuração do AWS SDK */
const logGroupName = process.env.NEXT_PUBLIC_LOG_GROUP_NAME
const logStreamName = 'front'
const cloudWatchLogsClient = new CloudWatchLogsClient({
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_KEY_AWS_ACCESS_KEY_ID as string,
    secretAccessKey: process.env.NEXT_PUBLIC_KEY_AWS_SECRET_ACCESS_KEY as string
  },
  region: process.env.NEXT_PUBLIC_KEY_AWS_DEFAULT_REGION
})

export { cloudWatchLogsClient, logGroupName, logStreamName }
