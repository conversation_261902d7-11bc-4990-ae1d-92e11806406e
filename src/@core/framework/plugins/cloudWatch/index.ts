import { ILog } from '@/@core/logging/logger.types'
import {
  cloudWatchLogsClient,
  logGroupName,
  logStreamName,
  PutLogEventsCommand
} from './config'

export const awsPutLogEvents = (logs: ILog[]) => {
  const logEvents = logs.map(({ title, level, content }) => ({
    message: ` ${level.toUpperCase()}: ${title} ${JSON.stringify(content)}`,
    timestamp: new Date().getTime()
  }))

  /** create logStream * /
  try {
    const logStreamCommand = new CreateLogStreamCommand({
      logGroupName,
      logStreamName
    })

    cloudWatchLogsClient.send(logStreamCommand)
  } catch (error) {
    console.log('CreateLogStreamCommand', (error as Error).name)
  }
  */

  /** send logs */
  const putCommand = new PutLogEventsCommand({
    logGroupName,
    logStreamName,
    logEvents
  })
  cloudWatchLogsClient.send(putCommand)
}
