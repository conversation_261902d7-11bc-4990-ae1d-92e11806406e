import { createSlice } from '@reduxjs/toolkit'
import { Toast } from '@/types/system/toast'

export type ISliceState = {
  toasts: Toast[]
}
export type ReducerPayloadProps = {
  setToast: ISliceState['toasts']
}

const initialState: ISliceState = {
  toasts: []
}

const reducers = {
  setToast(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setToast'] }
  ) {
    state.toasts = action.payload
  }
}

export const systemToastSlice = createSlice({
  name: 'systemToast',
  initialState,
  reducers
})

export const actions = systemToastSlice.actions
