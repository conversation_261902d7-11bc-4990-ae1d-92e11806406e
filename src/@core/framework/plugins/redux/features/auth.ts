import { createSlice } from '@reduxjs/toolkit'
import { IMe } from '@/@core/domain'

export type ISliceState = {
  me: IMe
}
export type ReducerPayloadProps = {
  setMe: ISliceState['me']
}

const initialState: ISliceState = {
  me: {
    user: {
      id: null,
      name: '',
      avatar: '',
      cellphone: '',
      email: '',
      active: false,
      admin: false
    },
    accountId: null,
    accountName: '',
    accountUserId: null,
    accountUserAdmin: false,
    notification: false,
    cognitoUserId: null,
    groups: []
  }
}

const reducers = {
  setMe(state: ISliceState, action: { payload: ReducerPayloadProps['setMe'] }) {
    state.me = action.payload
  }
}

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers
})

export const actions = authSlice.actions
