import { createSlice } from '@reduxjs/toolkit'

import { AccountFormFields } from '@/types/form/accounts'

export type ISliceState = {
  form: AccountFormFields
  tabs: {
    index: number
  }
}
export type ReducerPayloadProps = {
  setForm: ISliceState['form']
  setTabActive: ISliceState['tabs']['index']
}

const initialState: ISliceState = {
  form: {
    id: null,
    name: '',
    modules: [],
    modulesIds: [],
    status: null,
    statusId: null,
    userName: '',
    userEmail: ''
  },
  tabs: {
    index: 0
  }
}

const reducers = {
  setForm: (
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setForm'] }
  ) => {
    state.form = action.payload
  },
  setTabActive: (
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setTabActive'] }
  ) => {
    state.tabs.index = action.payload
  }
}

export const pageAccountsIdSlice = createSlice({
  name: 'pageAccountsId',
  initialState,
  reducers
})

export const actions = pageAccountsIdSlice.actions
