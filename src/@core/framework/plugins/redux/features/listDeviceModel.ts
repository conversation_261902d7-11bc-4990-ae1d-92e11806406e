import { createSlice } from '@reduxjs/toolkit'

import IDeviceModel from '@/@core/domain/DeviceModel'

export type ISliceState = {
  list: IDeviceModel[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listDeviceModelSlice = createSlice({
  name: 'listDeviceModel',
  initialState,
  reducers
})

export const actions = listDeviceModelSlice.actions
