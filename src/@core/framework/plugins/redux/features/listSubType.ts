import { createSlice } from '@reduxjs/toolkit'

import ISubType, { ISubTypeGrouped } from '@/@core/domain/SubType'

export type ISliceState = {
  list: ISubType[]
  listGrouped: ISubTypeGrouped[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: [],
  listGrouped: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
    state.listGrouped = action.payload.listGrouped
  }
}

export const listSubTypeSlice = createSlice({
  name: 'listSubType',
  initialState,
  reducers
})

export const actions = listSubTypeSlice.actions
