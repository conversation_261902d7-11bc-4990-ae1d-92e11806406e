import { createSlice } from '@reduxjs/toolkit'

import { IApportionmentTypes } from '@/@core/domain/ApportionmentTypes'

export type ISliceState = {
  list: IApportionmentTypes[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listApportionmentTypesSlice = createSlice({
  name: 'listApportionmentTypes',
  initialState,
  reducers
})

export const actions = listApportionmentTypesSlice.actions
