import { createSlice } from '@reduxjs/toolkit'

import { IApportionmenttMeasuresUnits } from '@/@core/domain/ApportionmenttMeasuresUnits'

export type ISliceState = {
  list: IApportionmenttMeasuresUnits[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listApportionmenttMeasuresUnitsSlice = createSlice({
  name: 'listApportionmenttMeasuresUnits',
  initialState,
  reducers
})

export const actions = listApportionmenttMeasuresUnitsSlice.actions
