import { ICompaniesContactsCategories } from '@/@core/domain/CompaniesContactsCategories'
import { createSlice } from '@reduxjs/toolkit'

export type ISliceState = {
  list: ICompaniesContactsCategories[]
}

export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listCompanyCategoriesSlice = createSlice({
  name: 'listCompanyCategories',
  initialState,
  reducers
})

export const actions = listCompanyCategoriesSlice.actions
