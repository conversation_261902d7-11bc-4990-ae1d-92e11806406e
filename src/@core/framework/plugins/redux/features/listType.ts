import { createSlice } from '@reduxjs/toolkit'

import IType from '@/@core/domain/Type'

export type ISliceState = {
  list: IType[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listTypeSlice = createSlice({
  name: 'listType',
  initialState,
  reducers
})

export const actions = listTypeSlice.actions
