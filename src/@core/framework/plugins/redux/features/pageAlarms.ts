import { createSlice } from '@reduxjs/toolkit'

import IAlarm, { IAlarmSearch } from '@/@core/domain/Alarm'

export type ISliceState = {
  loading: boolean
  formSearch: IAlarmSearch
  table: {
    items: IAlarm[]
    total: number
    limit: number
    page: number
    lastPage: number
  }
}
export type ReducerPayloadProps = {
  loading: ISliceState['loading']
  setFormSearch: ISliceState['formSearch']
  setTable: ISliceState['table']
}

const initialState: ISliceState = {
  loading: false,
  formSearch: {
    q: '',
    order: 'desc',
    sort: 'id',
    page: 1,
    limit: 15,
    status: 1,
    alarmsCategoryId: 1
  },
  table: {
    items: [],
    total: 0,
    page: 0,
    limit: 0,
    lastPage: 0
  }
}

const reducers = {
  setLoading(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['loading'] }
  ) {
    state.loading = action.payload
  },
  setFormSearch(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setFormSearch'] }
  ) {
    state.formSearch = action.payload
  },
  setTable(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setTable'] }
  ) {
    state.table.items = action.payload.items
    state.table.total = action.payload.total
    state.table.page = action.payload.page
    state.table.limit = action.payload.limit
    state.table.lastPage = action.payload.lastPage
  },
  setTablePage(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setTable']['page'] }
  ) {
    state.table.page = action.payload
  },
  setTableLimit(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setTable']['limit'] }
  ) {
    state.table.limit = action.payload
  }
}

export const pageAlarmsSlice = createSlice({
  name: 'pageAlarms',
  initialState,
  reducers
})

export const actions = pageAlarmsSlice.actions
