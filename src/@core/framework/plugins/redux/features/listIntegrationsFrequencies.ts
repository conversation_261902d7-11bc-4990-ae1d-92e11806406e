import { createSlice } from '@reduxjs/toolkit'

import IIntegrationsAccounts from '@/@core/domain/IntegrationFrequency'

export type ISliceState = {
  list: IIntegrationsAccounts[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listIntegrationsFrequenciesSlice = createSlice({
  name: 'listIntegrationsFrequencies',
  initialState,
  reducers
})

export const actions = listIntegrationsFrequenciesSlice.actions
