import { createSlice } from '@reduxjs/toolkit'

import { IApportionmentsFeeDivisionTypes } from '@/@core/domain/ApportionmentsFeeDivisionTypes'

export type ISliceState = {
  list: IApportionmentsFeeDivisionTypes[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listApportionmentsFeeDivisionTypes = createSlice({
  name: 'listApportionmentsFeeDivisionTypes',
  initialState,
  reducers
})

export const actions = listApportionmentsFeeDivisionTypes.actions
