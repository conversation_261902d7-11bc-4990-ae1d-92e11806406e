import { createSlice } from '@reduxjs/toolkit'
import { ThemeType } from '@/types/system/theme'

export type ISliceState = {
  theme: ThemeType
}
export type ReducerPayloadProps = {
  setTheme: ISliceState['theme']
}

const initialState: ISliceState = {
  theme: 'light'
}

const reducers = {
  setTheme(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setTheme'] }
  ) {
    state.theme = action.payload
  }
}

export const systemThemeSlice = createSlice({
  name: 'systemTheme',
  initialState,
  reducers
})

export const actions = systemThemeSlice.actions
