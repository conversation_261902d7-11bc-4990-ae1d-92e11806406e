import { createSlice } from '@reduxjs/toolkit'

import { IApportionmentTariffTypes } from '@/@core/domain/ApportionmentTariffTypes'

export type ISliceState = {
  list: IApportionmentTariffTypes[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listApportionmentTariffTypesSlice = createSlice({
  name: 'listApportionmentTariffTypes',
  initialState,
  reducers
})

export const actions = listApportionmentTariffTypesSlice.actions
