import { createSlice } from '@reduxjs/toolkit'

import { IListState } from '@/@core/domain/ListState'

export type ISliceState = {
  list: IListState[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listStateSlice = createSlice({
  name: 'listState',
  initialState,
  reducers
})

export const actions = listStateSlice.actions
