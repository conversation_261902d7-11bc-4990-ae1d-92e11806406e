import { createSlice } from '@reduxjs/toolkit'

import IDeviceInstallation<PERSON>ield from '@/@core/domain/DeviceInstallationFields'

export type ISliceState = {
  list: IDeviceInstallationField[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listDeviceInstallationFieldsSlice = createSlice({
  name: 'listDeviceInstallationFields',
  initialState,
  reducers
})

export const actions = listDeviceInstallationFieldsSlice.actions
