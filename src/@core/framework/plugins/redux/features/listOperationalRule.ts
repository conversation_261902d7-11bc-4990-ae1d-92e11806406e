import { createSlice } from '@reduxjs/toolkit'

import { IOperationalRule } from '@/@core/domain/OperationalRule'

export type ISliceState = {
  list: IOperationalRule[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listOperationalRuleSlice = createSlice({
  name: 'listOperationalRule',
  initialState,
  reducers
})

export const actions = listOperationalRuleSlice.actions
