import { createSlice } from '@reduxjs/toolkit'
import { IUserActionGrouped } from '@/@core/domain/UserAction'
import {
  IMenuItem,
  StatusMenuDesktop,
  StatusMenuMobile
} from '@/types/system/layout'

export type ISliceState = {
  menuItems: IMenuItem[]
  statusMenuDesktop: StatusMenuDesktop
  statusMenuMobile: StatusMenuMobile
  permissions: Partial<IUserActionGrouped>
  mountComponent: Record<string, boolean>
}
export type ReducerPayloadProps = {
  setStatusMenuDesktop: ISliceState['statusMenuDesktop']
  setStatusMenuMobile: ISliceState['statusMenuMobile']
  setMenuItems: ISliceState['menuItems']
  setPermissions: ISliceState['permissions']
  setMountComponent: { key: string; value: boolean }
}

const initialState: ISliceState = {
  menuItems: [],
  statusMenuDesktop: 'close',
  statusMenuMobile: 'close',
  permissions: {},
  mountComponent: {}
}

const reducers = {
  setStatusMenuDesktop(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setStatusMenuDesktop'] }
  ) {
    state.statusMenuDesktop = action.payload
  },
  setStatusMenuMobile(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setStatusMenuMobile'] }
  ) {
    state.statusMenuMobile = action.payload
  },
  setMenuItems(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setMenuItems'] }
  ) {
    state.menuItems = action.payload
  },
  setPermissions(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setPermissions'] }
  ) {
    state.permissions = action.payload
  },
  setMountComponent(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setMountComponent'] }
  ) {
    state.mountComponent[action.payload.key] = action.payload.value
  }
}

export const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers
})

export const actions = systemSlice.actions
