import { createSlice } from '@reduxjs/toolkit'

export type ISliceState = {
  loading: boolean
  pageLoading: boolean
  modalLoading: boolean
}
export type ReducerPayloadProps = {
  setLoading: ISliceState['loading']
  setData: Partial<ISliceState>
}

const initialState: ISliceState = {
  loading: false,
  pageLoading: false,
  modalLoading: false
}

const reducers = {
  setLoading(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setLoading'] }
  ) {
    state.loading = action.payload
  },
  setData(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setData'] }
  ) {
    if ('loading' in action.payload) {
      state.loading = action.payload.loading!
    }
    if ('pageLoading' in action.payload) {
      state.pageLoading = action.payload.pageLoading!
    }
    if ('modalLoading' in action.payload) {
      state.modalLoading = action.payload.modalLoading!
    }
  }
}

export const systemLoadingSlice = createSlice({
  name: 'systemLoading',
  initialState,
  reducers
})

export const actions = systemLoadingSlice.actions
