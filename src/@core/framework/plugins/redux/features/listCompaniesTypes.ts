import { createSlice } from '@reduxjs/toolkit'

import ICompaniesTypes from '@/@core/domain/CompaniesTypes'

export type ISliceState = {
  list: ICompaniesTypes[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listCompaniesTypesSlice = createSlice({
  name: 'listCompaniesTypes',
  initialState,
  reducers
})

export const actions = listCompaniesTypesSlice.actions
