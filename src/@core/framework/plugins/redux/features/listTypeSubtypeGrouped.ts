import { ITypeSubTypeGrouped } from '@/@core/utils/groupedListTypeSubtypes'
import { createSlice } from '@reduxjs/toolkit'

export type ISliceState = {
  list: ITypeSubTypeGrouped[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listTypeSubTypeGroupedSlice = createSlice({
  name: 'listTypeSubTypeGrouped',
  initialState,
  reducers
})

export const actions = listTypeSubTypeGroupedSlice.actions
