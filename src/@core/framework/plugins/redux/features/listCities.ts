import { createSlice } from '@reduxjs/toolkit'

import { IListCities } from '@/@core/domain/ListCities'

export type ISliceState = {
  list: IListCities[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listCitySlice = createSlice({
  name: 'listCity',
  initialState,
  reducers
})

export const actions = listCitySlice.actions
