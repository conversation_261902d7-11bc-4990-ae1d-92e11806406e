import { createSlice } from '@reduxjs/toolkit'

import { ICostCompositionType } from '@/@core/domain/CostCompositionTypes'

export type ISliceState = {
  list: ICostCompositionType[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listCostCompositionTypesSlice = createSlice({
  name: 'listCostCompositionTypes',
  initialState,
  reducers
})

export const actions = listCostCompositionTypesSlice.actions
