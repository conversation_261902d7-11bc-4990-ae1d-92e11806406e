import { createSlice } from '@reduxjs/toolkit'

import { ISubmarkets } from '@/@core/domain/Submarkets'

export type ISliceState = {
  list: ISubmarkets[]
}
export type ReducerPayloadProps = {
  set: ISliceState
}

const initialState: ISliceState = {
  list: []
}

const reducers = {
  set(state: ISliceState, action: { payload: ReducerPayloadProps['set'] }) {
    state.list = action.payload.list
  }
}

export const listSubmarketSlice = createSlice({
  name: 'listSubmarket',
  initialState,
  reducers
})

export const actions = listSubmarketSlice.actions
