import { createSlice } from '@reduxjs/toolkit'

import { IListExternalCodes } from '@/@core/domain/ListExternalCodes'

export type ISliceExternalCodes = {
  list: IListExternalCodes
}

export type ReducerPayloadProps = {
  set: ISliceExternalCodes
}

const initialState: ISliceExternalCodes = {
  list: {
    comerc: '',
    zordon: ''
  }
}

const reducers = {
  set(
    state: ISliceExternalCodes,
    action: { payload: ReducerPayloadProps['set'] }
  ) {
    state.list = action.payload.list
  }
}

export const listExternalCodesSlice = createSlice({
  name: 'listExternalCodes',
  initialState,
  reducers
})

export const actions = listExternalCodesSlice.actions
