import { createSlice } from '@reduxjs/toolkit'
import { LanguageType } from '@/types/language'

export type ISliceState = {
  lang: LanguageType
}
export type ReducerPayloadProps = {
  setLanguage: ISliceState['lang']
}

const initialState: ISliceState = {
  lang: 'pt-BR'
}

const reducers = {
  setLanguage(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setLanguage'] }
  ) {
    state.lang = action.payload
  }
}

export const systemLanguageSlice = createSlice({
  name: 'systemLanguage',
  initialState,
  reducers
})

export const actions = systemLanguageSlice.actions
