import { IBreadcrumbLink } from '@/types/system/breadcrumb'
import { createSlice } from '@reduxjs/toolkit'

export type ISliceState = {
  breadcrumbs: IBreadcrumbLink[]
}

export type ReducerPayloadProps = {
  setData: ISliceState['breadcrumbs']
}

const initialState: ISliceState = {
  breadcrumbs: []
}

const reducers = {
  setData(
    state: ISliceState,
    action: { payload: ReducerPayloadProps['setData'] }
  ) {
    state.breadcrumbs = action.payload
  }
}

export const systemBreadcrumbSlice = createSlice({
  name: 'systemBreadcrumb',
  initialState,
  reducers
})

export const actions = systemBreadcrumbSlice.actions
