import { authSlice } from '@/@core/framework/plugins/redux/features/auth'
import { pageAccountsSlice } from '@/@core/framework/plugins/redux/features/pageAccounts'
import { pageAccountsIdSlice } from '@/@core/framework/plugins/redux/features/pageAccountsId'
import { pageAlarmsSlice } from '@/@core/framework/plugins/redux/features/pageAlarms'
import { systemSlice } from '@/@core/framework/plugins/redux/features/system'
import { systemLanguageSlice } from '@/@core/framework/plugins/redux/features/systemLanguage'
import { systemLoadingSlice } from '@/@core/framework/plugins/redux/features/systemLoading'
import { systemThemeSlice } from '@/@core/framework/plugins/redux/features/systemTheme'
import { systemToastSlice } from '@/@core/framework/plugins/redux/features/systemToast'
import { listApportionmentsFeeDivisionTypes } from './features/listApportionmentsFeeDivisionTypes'
import { listApportionmentTariffTypesSlice } from './features/listApportionmentTariffTypes'
import { listApportionmenttMeasuresUnitsSlice } from './features/listApportionmenttMeasuresUnits'
import { listApportionmentTypesSlice } from './features/listApportionmentTypes'
import { listCitySlice } from './features/listCities'
import { listCompaniesTypesSlice } from './features/listCompaniesTypes'
import { listCompanyCategoriesSlice } from './features/listCompanyCategories'
import { listCostCompositionTypesSlice } from './features/listCostCompositionTypes'
import { listDeviceInstallationFieldsSlice } from './features/listDeviceInstallationFields'
import { listDeviceModelSlice } from './features/listDeviceModel'
import { listExternalCodesSlice } from './features/listExternalCodes'
import { listIntegrationsFrequenciesSlice } from './features/listIntegrationsFrequencies'
import { listOperationalRuleSlice } from './features/listOperationalRule'
import { listStateSlice } from './features/listState'
import { listSubmarketSlice } from './features/listSubmarket'
import { listSubTypeSlice } from './features/listSubType'
import { listTypeSlice } from './features/listType'
import { listTypeSubTypeGroupedSlice } from './features/listTypeSubtypeGrouped'
import { pagePropertiesSlice } from './features/pageProperties'
import { systemBreadcrumbSlice } from './features/systemBreadcrumbs'

const reducers = {
  [authSlice.name]: authSlice.reducer,
  [pageAlarmsSlice.name]: pageAlarmsSlice.reducer,
  [pageAccountsSlice.name]: pageAccountsSlice.reducer,
  [pageAccountsIdSlice.name]: pageAccountsIdSlice.reducer,
  [pagePropertiesSlice.name]: pagePropertiesSlice.reducer,
  [systemSlice.name]: systemSlice.reducer,
  [systemLanguageSlice.name]: systemLanguageSlice.reducer,
  [systemLoadingSlice.name]: systemLoadingSlice.reducer,
  [systemThemeSlice.name]: systemThemeSlice.reducer,
  [systemToastSlice.name]: systemToastSlice.reducer,
  [listTypeSlice.name]: listTypeSlice.reducer,
  [listSubTypeSlice.name]: listSubTypeSlice.reducer,
  [listDeviceModelSlice.name]: listDeviceModelSlice.reducer,
  [listDeviceInstallationFieldsSlice.name]:
    listDeviceInstallationFieldsSlice.reducer,
  [listSubmarketSlice.name]: listSubmarketSlice.reducer,
  [listStateSlice.name]: listStateSlice.reducer,
  [listCitySlice.name]: listCitySlice.reducer,
  [listCompanyCategoriesSlice.name]: listCompanyCategoriesSlice.reducer,
  [listExternalCodesSlice.name]: listExternalCodesSlice.reducer,
  [listIntegrationsFrequenciesSlice.name]:
    listIntegrationsFrequenciesSlice.reducer,
  [listOperationalRuleSlice.name]: listOperationalRuleSlice.reducer,
  [listTypeSubTypeGroupedSlice.name]: listTypeSubTypeGroupedSlice.reducer,
  [listApportionmentTariffTypesSlice.name]:
    listApportionmentTariffTypesSlice.reducer,
  [systemBreadcrumbSlice.name]: systemBreadcrumbSlice.reducer,
  [listCostCompositionTypesSlice.name]: listCostCompositionTypesSlice.reducer,
  [listCompaniesTypesSlice.name]: listCompaniesTypesSlice.reducer,
  [listApportionmentTypesSlice.name]: listApportionmentTypesSlice.reducer,
  [listApportionmenttMeasuresUnitsSlice.name]:
    listApportionmenttMeasuresUnitsSlice.reducer,
  [listApportionmentsFeeDivisionTypes.name]:
    listApportionmentsFeeDivisionTypes.reducer
}

export default reducers
