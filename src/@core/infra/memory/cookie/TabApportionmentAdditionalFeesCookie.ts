import { Order } from '@/types/system'
import CookieAbstract from './Cookie.abstract'

interface CookieTabCustomTariffsData {
  apportionmentId: number | null
  apportionmentTypeId: number | null
  page: number
  limit: number
  order: Order
  sort: 'id'
}

const TabApportionmentAdditionalFeesCookie = () => {
  return CookieAbstract<CookieTabCustomTariffsData>(
    'tabApportionmentAdditionalFeesCookie',
    {
      apportionmentId: null,
      apportionmentTypeId: null,
      page: 1,
      limit: 10,
      order: 'desc',
      sort: 'id'
    }
  )
}

export const tabApportionmentAdditionalFeesCookie =
  TabApportionmentAdditionalFeesCookie()
