import { Order } from '@/types/system'
import CookieAbstract from './Cookie.abstract'

interface CookieTabCustomTariffsData {
  apportionmentId: number | null
  page: number
  limit: number
  order: Order
  sort: 'id' | 'vigency_end'
}

const TabApportionmentCustomTariffsCookie = () => {
  return CookieAbstract<CookieTabCustomTariffsData>(
    'tabApportionmentCustomTariffs',
    {
      apportionmentId: null,
      page: 1,
      limit: 10,
      order: 'desc',
      sort: 'vigency_end'
    }
  )
}

export const tabApportionmentCustomTariffsCookie =
  TabApportionmentCustomTariffsCookie()
