import { Order } from '@/types/system'
import CookieAbstract from './Cookie.abstract'

interface CookieTabCustomTariffsData {
  costCenterId: number | null
  page: number
  limit: number
  order: Order
  sort: 'id'
}

const TabCustomTariffsCookie = () => {
  return CookieAbstract<CookieTabCustomTariffsData>('tabCustomTariffs', {
    costCenterId: null,
    page: 1,
    limit: 10,
    order: 'desc',
    sort: 'id'
  })
}

export const tabCustomTariffsCookie = TabCustomTariffsCookie()
