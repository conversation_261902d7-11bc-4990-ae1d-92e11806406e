import CookieAbstract from './Cookie.abstract'

interface CookieData {
  tabData: {}
  tabRules: {}
  tabMonitoring: {}
  tabNotifications: Partial<{
    page: number
    limit: number
  }>
  tabHistorical: Partial<{
    page: number
    limit: number
  }>
}

const PageAlarmsIdCookie = () => {
  return CookieAbstract<CookieData>('pageAlarmsId', {
    tabData: {},
    tabRules: {},
    tabMonitoring: {},
    tabNotifications: {
      limit: 15,
      page: 1
    },
    tabHistorical: {
      limit: 15,
      page: 1
    }
  })
}

export const pageAlarmsIdCookie = PageAlarmsIdCookie()
