import { Order } from '@/types/system'
import CookieAbstract from './Cookie.abstract'

interface CookieCostCenterEquipmentsData {
  costCenterIds: number[]
  page: number
  limit: number
  order: Order
  sort: 'id'
}

const TabCostCenterEquipmentsCookie = () => {
  return CookieAbstract<CookieCostCenterEquipmentsData>(
    'tabCostCenterEquipments',
    {
      costCenterIds: [],
      page: 1,
      limit: 10,
      order: 'desc',
      sort: 'id'
    }
  )
}

export const tabCostCenterEquipmentsCookie = TabCostCenterEquipmentsCookie()
