import { IApportionmentsFeeDivisionTypes } from '@/@core/domain/ApportionmentsFeeDivisionTypes'
import <PERSON><PERSON>Abstract from './Cookie.abstract'

interface CookieData {
  list: IApportionmentsFeeDivisionTypes[]
}

const ListApportionmentsFeeDivisionTypesCookie = () => {
  return CookieAbstract<CookieData>('listApportionmentsFeeDivisionTypes', {
    list: []
  })
}

export const listApportionmentsFeeDivisionTypesCookie =
  ListApportionmentsFeeDivisionTypesCookie()
