import { Order } from '@/types/system'
import CookieAbstract from './Cookie.abstract'

interface CookieCostCenterEquipmentsData {
  costCenterId: number
  page: number
  limit: number
  order: Order
  sort: 'id'
}

const TabCostCenterCustomAdditionaisCookie = () => {
  return CookieAbstract<CookieCostCenterEquipmentsData>(
    'tabCostCenterCustomAdditionais',
    {
      costCenterId: 0,
      page: 1,
      limit: 10,
      order: 'desc',
      sort: 'id'
    }
  )
}

export const tabCostCenterCustomAdditionaisCookie =
  TabCostCenterCustomAdditionaisCookie()
