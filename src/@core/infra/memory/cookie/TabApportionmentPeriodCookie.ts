import { Order } from '@/types/system'
import CookieAbstract from './Cookie.abstract'

interface CookieTabCustomTariffsData {
  apportionmentId: number | null
  page: number
  limit: number
  order: Order
  sort: 'id'
}

const TabApportionmentPeriodCookie = () => {
  return CookieAbstract<CookieTabCustomTariffsData>(
    'tabApportionmentPeriodCookie',
    {
      apportionmentId: null,
      page: 1,
      limit: 10,
      order: 'desc',
      sort: 'id'
    }
  )
}

export const tabApportionmentPeriodCookie = TabApportionmentPeriodCookie()
