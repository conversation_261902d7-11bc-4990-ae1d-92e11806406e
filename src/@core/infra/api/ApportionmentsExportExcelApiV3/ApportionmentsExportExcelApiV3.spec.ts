import { apportionmentsExportExcelApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { cleanup } from '@testing-library/react'

cleanup()

describe('src/@core/infra/api/ApportionmentsExportExcelApiV3', () => {
  test('request create', async () => {
    http.post = jest.fn().mockImplementationOnce(() => ({
      status: 201
    }))

    const result = await apportionmentsExportExcelApiV3(http).create({
      apportionmentId: 1,
      date: '2021-06-01',
      sendCostCenters: false
    })

    expect(result.status).toBe(201)
  })
})
