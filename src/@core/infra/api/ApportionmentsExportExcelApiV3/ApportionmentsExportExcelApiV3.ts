import { IHttpClient } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import { apportionmentExportExcelParsePayload } from './ApportionmentsExportExcelApiV3.helpers'
import {
  IPayloadApportionmentExportExcel,
  ITdoApportionmentExportExcel
} from './ApportionmentsExportExcelApiV3.types'

export const apportionmentsExportExcelApiV3 = (http: IHttpClient) => ({
  create: async ({
    apportionmentId,
    ...tdo
  }: ITdoApportionmentExportExcel & {
    apportionmentId: number
  }) => {
    const payload: IPayloadApportionmentExportExcel =
      apportionmentExportExcelParsePayload(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments/${apportionmentId}/export-excel`
    })

    const { status, data } = await http.post<string[]>(url, payload)

    return {
      status,
      data
    }
  }
})
