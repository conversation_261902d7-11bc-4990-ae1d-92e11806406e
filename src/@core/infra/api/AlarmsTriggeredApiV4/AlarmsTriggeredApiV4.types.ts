export interface TdoSearch {
  // q?: string
  order?: 'asc' | 'desc'
  sort?: 'id'
  limit?: number
  page?: number
  companyId?: number
  equipmentId?: number
}

export interface Response {
  id: number
  alarm: {
    id: number
    name: string
  }
  triggered_at: string
  triggered_values: string
  normalized_at: string
  normalized_values: string
  status: string
  action_support: {
    device_status_after_trigger: number
    device_status_after_normalize: number
  }
  company: {
    id: number
    name: string
  }
  equipment: {
    id: number
    name: string
  }
}
