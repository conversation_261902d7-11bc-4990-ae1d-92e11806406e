import { IApportionment } from '@/@core/domain/Apportionment'
import {
  IApportionmentsPayload,
  IApportionmentsPayloadSearch,
  IApportionmentsResponse,
  IApportionmentsTdo
} from './ApportionmentsV3.types'

export const parseApportionmentSearchHelper = (
  search: IApportionmentsPayloadSearch
) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page
})
export const parseApportionmentDataHelper = (
  data: IApportionmentsResponse
): IApportionment => ({
  id: data.id,
  name: data.name,
  description: data.description,
  company: data.company,
  apportionmentType: data.apportionment_type,
  apportionmentMeasureUnit: data.apportionment_measure_unit,
  apportionmentTariffType: data.apportionment_tariff_type,
  costCenterIds: data.cost_center_ids,
  mixedConsumption: data.mixed_consumption
})
export const parsePayloadHelper = ({
  name,
  description,
  companyId: company_id,
  apportionmentTypeId: apportionment_type_id,
  apportionmentTariffTypeId: apportionment_tariff_type_id,
  apportionmentMeasureUnitId: apportionment_measure_unit_id,
  mixedConsumption: mixed_consumption
}: IApportionmentsTdo): IApportionmentsPayload => {
  return {
    name,
    description,
    company_id,
    apportionment_type_id,
    apportionment_tariff_type_id,
    apportionment_measure_unit_id,
    mixed_consumption
  }
}
