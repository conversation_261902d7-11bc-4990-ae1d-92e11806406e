export interface IApportionmentsPayloadSearch {
  q?: string
  sort?: 'id'
  order?: 'asc' | 'desc'
  limit?: number
  page?: number
}
export interface IApportionmentsResponse {
  id: number
  name: string
  description: string
  company: {
    id: number
    name: string
  }
  apportionment_type: {
    id: number
    name: string
  }
  apportionment_measure_unit: {
    id: number
    name: string
  }
  apportionment_tariff_type: {
    id: number
    name: string
  }
  cost_center_ids: number[]
  mixed_consumption: boolean
}
export interface IApportionmentsTdo {
  name: string
  description: string
  companyId: number
  apportionmentTypeId: number
  apportionmentTariffTypeId: number
  apportionmentMeasureUnitId: number
  mixedConsumption: boolean
}
export interface IApportionmentsPayload {
  name: string
  description: string
  company_id: number
  apportionment_type_id: number
  apportionment_tariff_type_id: number
  apportionment_measure_unit_id: number
  mixed_consumption: boolean
}
