import { IApportionment } from '@/@core/domain/Apportionment'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import {
  parseApportionmentDataHelper,
  parseApportionmentSearchHelper,
  parsePayloadHelper
} from './ApportionmentsV3.helpers'
import {
  IApportionmentsPayload,
  IApportionmentsPayloadSearch,
  IApportionmentsResponse,
  IApportionmentsTdo
} from './ApportionmentsV3.types'

export const apportionmentsApiV3 = (http: IHttpClient) => ({
  get: async (search: IApportionmentsPayloadSearch = {}) => {
    const searchParsed = parseApportionmentSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionments',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentsResponse>
    >(url)

    const itemsParsed: IApportionment[] = (data?.items ?? []).map(
      parseApportionmentDataHelper
    )

    const dataParsed = httpParseList<IApportionment>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: `/api/v3/apportionments`,
      id
    })

    const { status, data } = await http.get<IApportionmentsResponse>(url)

    const dataParsed = parseApportionmentDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: IApportionmentsTdo) => {
    const payload: IApportionmentsPayload = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments`
    })

    const { status, data } = await http.post<IApportionmentsResponse>(
      url,
      payload
    )

    const dataParsed: IApportionment = parseApportionmentDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: IApportionmentsTdo) => {
    const payload: IApportionmentsPayload = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments`,
      id
    })

    const { status, data } = await http.put<IApportionmentsResponse>(
      url,
      payload
    )

    const dataParsed: IApportionment = parseApportionmentDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const url = httpPrepareUrl({
      url: '/api/v3/apportionments',
      id
    })

    const { status } = await http.delete(url)
    return { status }
  }
})
