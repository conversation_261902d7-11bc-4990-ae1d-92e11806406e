import { cleanup } from '@testing-library/react'
import { http } from '@/@core/infra/http'
import { apportionmentsApiV3 } from './ApportionmentsV3'
import {
  apportionmentMock1,
  apportionmentResponseMock1,
  apportionmentResponseMock2
} from '@/__mock__/content/api-apportionment.content'

cleanup()

describe('src/@core/infra/api/ApportionmentsV3', () => {
  it('request get without query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [apportionmentResponseMock1, apportionmentResponseMock2],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 2
      }
    })

    const result = await apportionmentsApiV3(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items).toHaveLength(2)
  })

  it('request get with query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [apportionmentResponseMock1, apportionmentResponseMock2],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 2
      }
    })

    const result = await apportionmentsApiV3(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1
    })
    expect(result.status).toBe(200)
    expect(result.data.items).toHaveLength(2)
  })

  it('request get no data', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const result = await apportionmentsApiV3(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1
    })

    expect(result.status).toEqual(204)
    expect(result.data.items).toHaveLength(0)
  })

  it('request getById', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: apportionmentResponseMock1
    })

    const result = await apportionmentsApiV3(http).getById(1)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(apportionmentMock1)
  })

  it('request delete', async () => {
    http.delete = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const result = await apportionmentsApiV3(http).delete(1)

    expect(result.status).toBe(204)
  })
})
