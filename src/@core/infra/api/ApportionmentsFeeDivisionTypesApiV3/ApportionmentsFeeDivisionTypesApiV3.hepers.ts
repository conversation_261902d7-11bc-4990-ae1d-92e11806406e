import { IApportionmentFeeDivisionType } from '@/@core/domain/ApportionmentFeeDivisionType'
import {
  IApportionmentFeeDivisionTypeResponse,
  IApportionmentFeeDivisionTypeTdoSearch
} from './ApportionmentsFeeDivisionTypesApiV3.types'

export const parseApportionmentFeeDivisionTypeSearch = (
  search: IApportionmentFeeDivisionTypeTdoSearch
) => ({
  type_id: search?.typeId
})

export const parseApportionmentFeeDivisionTypeResponse = (
  data: IApportionmentFeeDivisionTypeResponse
): IApportionmentFeeDivisionType => {
  return {
    id: data.id,
    name: data.name
  }
}
