import { cleanup } from '@testing-library/react'

import { apportionmentsFeeDivisionTypesApiV3 } from '@/@core/infra/api/ApportionmentsFeeDivisionTypesApiV3'
import { http } from '@/@core/infra/http'
import {
  apportionmentFeeDivisionTypeResponseMock1,
  apportionmentFeeDivisionTypeResponseMock2
} from '@/__mock__/content/api-apportionments-fee-division-types.content'

cleanup()

describe('src/@core/infra/api/ApportionmentsFeeDivisionTypesApiV3', () => {
  it('request get without query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          apportionmentFeeDivisionTypeResponseMock1,
          apportionmentFeeDivisionTypeResponseMock2
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await apportionmentsFeeDivisionTypesApiV3(http).get()

    expect(result.status).toBe(200)
    expect(result.data.length).toBe(2)
  })

  it('request get with query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          apportionmentFeeDivisionTypeResponseMock1,
          apportionmentFeeDivisionTypeResponseMock2
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await apportionmentsFeeDivisionTypesApiV3(http).get({
      typeId: 118
    })

    expect(result.status).toBe(200)
    expect(result.data.length).toBe(2)
  })

  it('request get no data', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })
    const result = await apportionmentsFeeDivisionTypesApiV3(http).get({
      typeId: 118
    })

    expect(result.status).toBe(204)
    expect(result.data.length).toBe(0)
  })
})
