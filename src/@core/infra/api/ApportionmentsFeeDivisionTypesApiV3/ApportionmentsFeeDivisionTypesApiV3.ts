import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  parseApportionmentFeeDivisionTypeResponse,
  parseApportionmentFeeDivisionTypeSearch
} from './ApportionmentsFeeDivisionTypesApiV3.hepers'
import {
  IApportionmentFeeDivisionTypeResponse,
  IApportionmentFeeDivisionTypeTdoSearch
} from './ApportionmentsFeeDivisionTypesApiV3.types'

export const apportionmentsFeeDivisionTypesApiV3 = (http: IHttpClient) => ({
  get: async (search: IApportionmentFeeDivisionTypeTdoSearch = {}) => {
    const searchParsed = parseApportionmentFeeDivisionTypeSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionments-fee-division-types',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentFeeDivisionTypeResponse>
    >(url)

    const dataParsed = (data?.items ?? []).map(
      parseApportionmentFeeDivisionTypeResponse
    )

    return {
      status,
      data: dataParsed
    }
  }
})
