import { IApportionmentsGroup } from '@/@core/domain/ApportionmentsGroups'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'

import {
  parsePayloadHelper,
  parseResponseHelper,
  parseSearchHelper
} from './ApportionmentsGroupsApiV3.helpers'
import {
  PayloadApportionmentsGroup,
  ResponseApportionmentsGroup,
  TdoApportionmentsGroup,
  TdoSearchApportionmentsGroup
} from './ApportionmentsGroupsApiV3.types'

export const apportionmentsGroupsApiV3 = (http: IHttpClient) => ({
  get: async (search: TdoSearchApportionmentsGroup = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionments-groups',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<ResponseApportionmentsGroup>
    >(url)

    const itemsParsed = (data?.items ?? []).map(parseResponseHelper)

    const dataParsed = httpParseList<IApportionmentsGroup>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-groups`,
      id
    })

    const { status, data } = await http.get<ResponseApportionmentsGroup>(url)
    const dataParsed: IApportionmentsGroup = parseResponseHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: TdoApportionmentsGroup) => {
    const payload: PayloadApportionmentsGroup = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-groups`
    })

    const { status, data } = await http.post<ResponseApportionmentsGroup>(
      url,
      payload
    )

    const dataParsed: IApportionmentsGroup = parseResponseHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: TdoApportionmentsGroup) => {
    const payload: PayloadApportionmentsGroup = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-groups`,
      id
    })

    const { status, data } = await http.put<ResponseApportionmentsGroup>(
      url,
      payload
    )

    const dataParsed: IApportionmentsGroup = parseResponseHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const { status } = await http.delete(`/api/v3/apportionments-groups/${id}`)
    return { status }
  }
})
