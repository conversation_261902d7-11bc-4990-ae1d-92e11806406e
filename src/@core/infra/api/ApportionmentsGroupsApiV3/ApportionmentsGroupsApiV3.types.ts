import { Order } from '@/types/system'

export interface TdoSearchApportionmentsGroup {
  q?: string
  sort?: 'id'
  order?: Order
  limit?: number
  page?: number
  apportionmentId?: number
}

export interface ResponseApportionmentsGroup {
  id: number
  apportionment_id: number
  name: string
  number_equipments: number
  equipments: {
    id: number
    apportionment_group_id: number
    equipment_id: number
  }[]
}

export interface TdoApportionmentsGroup {
  apportionmentId: number
  name: string
}

export interface PayloadApportionmentsGroup {
  apportionment_id: number
  name: string
}
