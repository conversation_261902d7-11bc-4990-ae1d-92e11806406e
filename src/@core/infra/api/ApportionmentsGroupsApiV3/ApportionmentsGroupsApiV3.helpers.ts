import { IApportionmentsGroup } from '@/@core/domain/ApportionmentsGroups'
import {
  ResponseApportionmentsGroup,
  TdoSearchApportionmentsGroup,
  TdoApportionmentsGroup,
  PayloadApportionmentsGroup
} from './ApportionmentsGroupsApiV3.types'

export const parseSearchHelper = (search: TdoSearchApportionmentsGroup) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  apportionment_id: search?.apportionmentId
})

export const parseResponseHelper = (
  data: ResponseApportionmentsGroup
): IApportionmentsGroup => {
  return {
    id: data.id,
    apportionmentId: data.apportionment_id,
    name: data.name,
    numberEquipments: data.number_equipments,
    equipments: data.equipments.map((equipment) => ({
      id: equipment.id,
      apportionmentGroupId: equipment.apportionment_group_id,
      equipmentId: equipment.equipment_id
    }))
  }
}

export const parsePayloadHelper = (
  tdo: TdoApportionmentsGroup
): PayloadApportionmentsGroup => ({
  apportionment_id: tdo.apportionmentId,
  name: tdo.name
})
