import { cleanup } from '@testing-library/react'
import { http, IHttpResponseList } from '@/@core/infra/http'
import { apportionmentsGroupsApiV3 } from '@/@core/infra/api/ApportionmentsGroupsApiV3'
import { ResponseApportionmentsGroup } from '@/@core/infra/api/ApportionmentsGroupsApiV3/ApportionmentsGroupsApiV3.types'
import {
  apportionmentsGroupMock1,
  apportionmentsGroupMock2,
  apportionmentsGroupsResponseMock1,
  apportionmentsGroupsResponseMock2
} from '@/__mock__/content/api-apportionments-group.content'

cleanup()

describe('src/@core/infra/api/ApportionmentsGroupsApiV3', () => {
  const responseAllMock: IHttpResponseList<ResponseApportionmentsGroup> = {
    items: [],
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }

  test('request get without query', async () => {
    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const result = await apportionmentsGroupsApiV3(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    responseAllMock.items.push(apportionmentsGroupsResponseMock1)
    responseAllMock.items.push(apportionmentsGroupsResponseMock2)

    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const result = await apportionmentsGroupsApiV3(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      apportionmentId: 1
    })

    expect(result.data.items[0]).toEqual(apportionmentsGroupMock1)
    expect(result.data.items[1]).toEqual(apportionmentsGroupMock2)
  })

  test('error request get', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await apportionmentsGroupsApiV3(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      apportionmentId: 1
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      total: null,
      limit: 1,
      page: null,
      lastPage: 0
    })
  })

  test('request getById', async () => {
    const responseMock = {
      status: 200,
      data: apportionmentsGroupsResponseMock1
    }

    http.get = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await apportionmentsGroupsApiV3(http).getById(1)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(apportionmentsGroupMock1)
  })

  test('request create', async () => {
    const responseMock = {
      status: 201,
      data: apportionmentsGroupsResponseMock1
    }

    http.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await apportionmentsGroupsApiV3(http).create({
      apportionmentId: 101,
      name: 'Grupo A'
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(apportionmentsGroupMock1)
  })

  test('request update', async () => {
    const responseMock = {
      status: 201,
      data: apportionmentsGroupsResponseMock1
    }

    http.put = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await apportionmentsGroupsApiV3(http).update(1, {
      apportionmentId: 101,
      name: 'Grupo A'
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(apportionmentsGroupMock1)
  })

  test('request delete', async () => {
    const responseMock = {
      status: 204,
      data: null
    }

    http.delete = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await apportionmentsGroupsApiV3(http).delete(1)

    expect(result.status).toBe(204)
  })
})
