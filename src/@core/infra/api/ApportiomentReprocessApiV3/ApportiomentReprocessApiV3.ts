import { IHttpClient } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import { PayloadApportiomentReprocess } from './ApportiomentReprocessApiV3.types'

export const apportiomentReprocessApiV3 = (http: IHttpClient) => ({
  create: async (
    payload: PayloadApportiomentReprocess,
    apportionmentId: number
  ) => {
    const url = httpPrepareUrl({
      url: `/api/v3/apportionments/${apportionmentId}/reprocess/`
    })

    const { status, data } = await http.post<string[]>(url, payload)

    return {
      status,
      data
    }
  }
})
