import { apportiomentReprocessApiV3 } from '@/@core/infra/api/ApportiomentReprocessApiV3/'
import { http } from '@/@core/infra/http'
import { cleanup } from '@testing-library/react'

cleanup()

describe('src/@core/infra/api/ApportiomentReprocessApiV3', () => {
  test('request create', async () => {
    const responseMock = {
      status: 201
    }

    http.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await apportiomentReprocessApiV3(http).create(
      { date: '2021-06-01' },
      1
    )

    expect(result.status).toBe(201)
  })
})
