import { cleanup } from '@testing-library/react'

import { apportionmentsTariffsApiV3 } from '@/@core/infra/api/ApportionmentsTariffsApiV3'
import { IApportionmentsTariffsResponse } from '@/@core/infra/api/ApportionmentsTariffsApiV3/ApportionmentsTariffsApiV3.types'
import { http, IHttpResponseList } from '@/@core/infra/http'
import {
  apportionmentsTariffsMock1,
  apportionmentsTariffsMock2,
  apportionmentsTariffsResponseMock1,
  apportionmentsTariffsResponseMock2
} from '@/__mock__/content/api-apportionments-tariffs.content'

cleanup()

describe('src/@core/infra/api/ApportionmentsTariffsApiV3/ApportionmentsTariffsApiV3', () => {
  const responseAllMock: IHttpResponseList<IApportionmentsTariffsResponse> = {
    items: [],
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }

  test('request get without query', async () => {
    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const result = await apportionmentsTariffsApiV3(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    responseAllMock.items.push(apportionmentsTariffsResponseMock1)
    responseAllMock.items.push(apportionmentsTariffsResponseMock2)

    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    http.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await apportionmentsTariffsApiV3(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      apportionmentId: 22
    })

    expect(status).toEqual(200)
    expect(data.items[0]).toEqual(apportionmentsTariffsMock1)
    expect(data.items[1]).toEqual(apportionmentsTariffsMock2)
  })

  test('error request get', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await apportionmentsTariffsApiV3(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      total: null,
      limit: 1,
      page: null,
      lastPage: 0
    })
  })

  test('request getById', async () => {
    const responseMock = {
      status: 200,
      data: apportionmentsTariffsResponseMock1
    }

    http.get = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await apportionmentsTariffsApiV3(http).getById(1)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(apportionmentsTariffsMock1)
  })

  test('request create', async () => {
    const responseMock = {
      status: 201,
      data: apportionmentsTariffsResponseMock1
    }

    http.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await apportionmentsTariffsApiV3(http).create({
      vigencyStart: '2025-01-01',
      vigencyEnd: '2025-12-31',
      value: 10000.0,
      apportionmentId: 101
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(apportionmentsTariffsMock1)
  })

  test('request update', async () => {
    const responseMock = {
      status: 201,
      data: apportionmentsTariffsResponseMock1
    }

    http.put = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await apportionmentsTariffsApiV3(http).update(1, {
      vigencyStart: '2025-01-01',
      vigencyEnd: '2025-12-31',
      value: 10000.0,
      apportionmentId: 101
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(apportionmentsTariffsMock1)
  })

  test('request delete', async () => {
    const responseMock = {
      status: 204,
      data: null
    }

    http.delete = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await apportionmentsTariffsApiV3(http).delete(1)

    expect(result.status).toBe(204)
  })
})
