import { IApportionmentTariff } from '@/@core/domain/ApportionmentTariffs'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  apportionmentsTariffsParsePayload,
  apportionmentsTariffsParseResponse,
  apportionmentsTariffsParseSearch
} from './ApportionmentsTariffsApiV3.helpers'
import {
  IApportionmentsTariffsPayload,
  IApportionmentsTariffsResponse,
  IApportionmentsTariffsTdo,
  IApportionmentsTariffsTdoSearch
} from './ApportionmentsTariffsApiV3.types'

export const apportionmentsTariffsApiV3 = (http: IHttpClient) => ({
  get: async (search: IApportionmentsTariffsTdoSearch = {}) => {
    const searchParsed = apportionmentsTariffsParseSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionments-tariffs',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentsTariffsResponse>
    >(url)

    const itemsParsed = (data?.items ?? []).map(
      apportionmentsTariffsParseResponse
    )

    const dataParsed = httpParseList<IApportionmentTariff>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: '/api/v3/apportionments-tariffs',
      id
    })

    const { status, data } = await http.get<IApportionmentsTariffsResponse>(url)

    const dataParsed: IApportionmentTariff =
      apportionmentsTariffsParseResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: IApportionmentsTariffsTdo) => {
    const payload: IApportionmentsTariffsPayload =
      apportionmentsTariffsParsePayload(tdo)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionments-tariffs'
    })

    const { status, data } = await http.post<IApportionmentsTariffsResponse>(
      url,
      payload
    )

    const dataParsed: IApportionmentTariff =
      apportionmentsTariffsParseResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: IApportionmentsTariffsTdo) => {
    const payload: IApportionmentsTariffsPayload =
      apportionmentsTariffsParsePayload(tdo)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionments-tariffs',
      id
    })

    const { status, data } = await http.put<IApportionmentsTariffsResponse>(
      url,
      payload
    )

    const dataParsed: IApportionmentTariff =
      apportionmentsTariffsParseResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const { status } = await http.delete(`/api/v3/apportionments-tariffs/${id}`)
    return { status }
  }
})
