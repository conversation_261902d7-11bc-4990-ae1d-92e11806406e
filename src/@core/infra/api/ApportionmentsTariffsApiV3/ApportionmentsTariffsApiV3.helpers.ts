import { IApportionmentTariff } from '@/@core/domain/ApportionmentTariffs'
import {
  IApportionmentsTariffsPayload,
  IApportionmentsTariffsResponse,
  IApportionmentsTariffsTdo,
  IApportionmentsTariffsTdoSearch
} from './ApportionmentsTariffsApiV3.types'

export const apportionmentsTariffsParseSearch = (
  search: IApportionmentsTariffsTdoSearch
) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  apportionmentId: search?.apportionmentId
})
export const apportionmentsTariffsParseResponse = (
  data: IApportionmentsTariffsResponse
): IApportionmentTariff => {
  return {
    id: data.id,
    vigencyStart: data.vigency_start,
    vigencyEnd: data.vigency_end,
    value: data.value,
    apportionmentId: data.apportionment_id
  }
}
export const apportionmentsTariffsParsePayload = (
  tdo: IApportionmentsTariffsTdo
): IApportionmentsTariffsPayload => ({
  vigency_start: tdo.vigencyStart,
  vigency_end: tdo.vigencyEnd,
  value: tdo.value,
  apportionment_id: tdo.apportionmentId
})
