import { Order } from '@/types/system'

export interface IApportionmentsTariffsTdoSearch {
  q?: string
  sort?: 'id' | 'vigency_start' | 'vigency_end'
  order?: Order
  limit?: number
  page?: number
  apportionmentId?: number
}

export interface IApportionmentsTariffsResponse {
  id: number
  apportionment_id: number
  vigency_start: string
  vigency_end: string
  value: number
}

export interface IApportionmentsTariffsTdo {
  vigencyStart: string
  vigencyEnd: string
  value: number
  apportionmentId: number
}

export interface IApportionmentsTariffsPayload {
  apportionment_id: number
  vigency_start: string
  vigency_end: string
  value: number
}
