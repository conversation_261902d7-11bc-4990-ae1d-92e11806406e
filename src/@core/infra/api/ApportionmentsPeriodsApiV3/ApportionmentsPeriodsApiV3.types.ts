import { Order } from '@/types/system'

export interface IApportionmentPeriodTdoSearch {
  sort?: 'id' | 'period'
  order?: Order
  limit?: number
  page?: number
  apportionmentId: number
}
export interface IApportionmentPeriodResponse {
  id: number
  apportionment_id: number
  period: string
  period_start: string
  period_end: string
}

export interface IApportionmentPeriodTdo {
  apportionmentId: number
  period: string
  periodStart: string
  periodEnd: string
}
export interface IApportionmentPeriodPayload {
  apportionment_id: number
  period: string
  period_start: string
  period_end: string
}
