import { IApportionmentPeriod } from '@/@core/domain/ApportionmentPeriod'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  paeseApportionmentPeriodPayload,
  parseApportionmentPeriodResponse,
  parseApportionmentPeriodSearch
} from './ApportionmentsPeriodsApiV3.hepers'
import {
  IApportionmentPeriodPayload,
  IApportionmentPeriodResponse,
  IApportionmentPeriodTdo,
  IApportionmentPeriodTdoSearch
} from './ApportionmentsPeriodsApiV3.types'

export const apportionmentsPeriodsApiV3 = (http: IHttpClient) => ({
  get: async (search: IApportionmentPeriodTdoSearch) => {
    const searchParsed = parseApportionmentPeriodSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionments-periods',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentPeriodResponse>
    >(url)

    const itemsParsed = (data?.items ?? []).map(
      parseApportionmentPeriodResponse
    )

    const dataParsed = httpParseList<IApportionmentPeriod>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-periods`,
      id
    })

    const { status, data } = await http.get<IApportionmentPeriodResponse>(url)
    const dataParsed: IApportionmentPeriod =
      parseApportionmentPeriodResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: IApportionmentPeriodTdo) => {
    const payload: IApportionmentPeriodPayload =
      paeseApportionmentPeriodPayload(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-periods`
    })

    const { status, data } = await http.post<IApportionmentPeriodResponse>(
      url,
      payload
    )

    const dataParsed: IApportionmentPeriod =
      parseApportionmentPeriodResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: IApportionmentPeriodTdo) => {
    const payload: IApportionmentPeriodPayload =
      paeseApportionmentPeriodPayload(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-periods`,
      id
    })

    const { status, data } = await http.put<IApportionmentPeriodResponse>(
      url,
      payload
    )

    const dataParsed: IApportionmentPeriod =
      parseApportionmentPeriodResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const { status } = await http.delete(`/api/v3/apportionments-periods/${id}`)

    return { status }
  }
})
