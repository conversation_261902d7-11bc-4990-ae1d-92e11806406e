export interface TdoSearch {
  q?: string
  sort?: string
  order?: 'asc' | 'desc'
  limit?: number
  page?: number
  status?: number
  alarmsCategoryId?: number
}
export interface Response {
  id: number
  name: string
  description: string
  time_confirmation: number
  initial_hour: string | null
  final_hour: string | null
  days_week: number[]
  days_retention: number | null
  status: boolean
  account: { id: number; name: string } | null
  category: { id: number; name: string }
}
export interface Tdo {
  name: string
  description: string
  timeConfirmation: number
  initialHour: string | null
  finalHour: string | null
  daysWeek: number[]
  daysRetention: number | null
  status: boolean
  accountId: number
  categoryId: number
}

export interface TdoUpdate {
  name: string
  description: string
  timeConfirmation: number
  initialHour: string | null
  finalHour: string | null
  daysWeek: number[]
  daysRetention: number | null
  status: boolean
}

export interface IAlarmsRequestUpdate {
  name: string
  description: string
  time_confirmation: number
  initial_hour: string | null
  final_hour: string | null
  days_week: number[]
  days_retention: number | null
  status: boolean
}
export interface Payload {
  name: string
  description: string
  initial_hour: string | null
  final_hour: string | null
  days_week: number[]
  days_retention: string | null
  time_confirmation: number
  alarms_category_id: number
  status: boolean
  account_id?: number
}
