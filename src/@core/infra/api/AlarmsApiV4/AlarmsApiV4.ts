import IAlarm from '@/@core/domain/Alarm'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  alarmsParsePayloadUpdate,
  parsePayloadHelper,
  parseResponseDataHelper,
  parseSearchHelper
} from './AlarmsApiV4.helpers'
import {
  IAlarmsRequestUpdate,
  Payload,
  Response,
  Tdo,
  TdoSearch,
  TdoUpdate
} from './AlarmsApiV4.types'

export const alarmsApiV4 = (http: IHttpClient) => ({
  get: async (search: TdoSearch = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: `/api/v4/alarms`,
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const itemsParsed: IAlarm[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    const dataParsed = httpParseList({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: `/api/v4/alarms`,
      id
    })

    const { status, data } = await http.get<Response>(url)

    const dataParsed: IAlarm = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: Tdo) => {
    const payload: Payload = parsePayloadHelper(tdo)

    const url = httpPrepareUrl({
      url: `/api/v4/alarms`
    })

    const { status, data } = await http.post<Response>(url, payload)

    const dataParsed: IAlarm = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: TdoUpdate) => {
    const payload: IAlarmsRequestUpdate = alarmsParsePayloadUpdate(tdo)

    const url = httpPrepareUrl({
      url: `/api/v4/alarms`,
      id
    })

    const { status, data } = await http.put<Response>(url, payload)
    const dataParsed: IAlarm = parseResponseDataHelper(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const url = httpPrepareUrl({
      url: `/api/v4/alarms`,
      id
    })

    const { status } = await http.delete(url)

    return { status }
  }
})
