import IAlarm from '@/@core/domain/Alarm'
import { httpFake, IHttpResponseList } from '../../http'
import { alarmsApiV4 } from './AlarmsApiV4'
import { Response } from './AlarmsApiV4.types'
import { cleanup } from '@testing-library/react'
import {
  alarmsParsePayloadUpdate,
  parsePayloadHelper
} from './AlarmsApiV4.helpers'
import { ALARMS_CATEGORY_USER_ID } from '@/@core/constants'

cleanup()

describe('src/@core/infra/api/AccountsApiV3/AlarmsApiV4', () => {
  const content1: Response = {
    id: 1,
    name: 'alarme',
    description: 'desc',
    time_confirmation: 1,
    initial_hour: '',
    final_hour: '',
    days_week: [1, 2],
    days_retention: 1,
    status: true,
    account: null,
    category: {
      id: 3,
      name: 'category name'
    }
  }
  const responseAllMock: IHttpResponseList<Response> = {
    items: [],
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }
  const responseParsed: IAlarm = {
    id: 1,
    name: 'alarme',
    description: 'desc',
    timeConfirmation: 1,
    initialHour: '',
    finalHour: '',
    daysWeek: [1, 2],
    daysRetention: 1,
    status: true,
    account: { id: 1, name: 'account name' },
    accountId: 1,
    category: { id: 3, name: 'category name' },
    categoryId: 3,
    readonly: false
  }

  const content2: Response = {
    id: 1,
    name: 'alarme',
    description: 'desc',
    time_confirmation: 1,
    initial_hour: null,
    final_hour: null,
    days_week: [1, 2],
    days_retention: null,
    status: true,
    account: {
      id: 2,
      name: 'account name'
    },
    category: {
      id: 3,
      name: 'category name'
    }
  }

  const responseParsed2: IAlarm = {
    id: 1,
    name: 'alarme',
    description: 'desc',
    timeConfirmation: 1,
    initialHour: null,
    finalHour: null,
    daysWeek: [1, 2],
    daysRetention: null,
    status: true,
    account: { id: 2, name: 'account name' },
    accountId: 2,
    category: { id: 3, name: 'category name' },
    categoryId: 3,
    readonly: false
  }

  test('request get without query', async () => {
    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const result = await alarmsApiV4(httpFake).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    content1.account = null

    responseAllMock.items.push(content1)

    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const result = await alarmsApiV4(httpFake).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      status: 1,
      alarmsCategoryId: 1
    })

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(1)
  })

  test('error request get', async () => {
    httpFake.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await alarmsApiV4(httpFake).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      status: 1,
      alarmsCategoryId: 1
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      total: null,
      limit: 1,
      page: null,
      lastPage: 0
    })
  })

  test('request getById', async () => {
    content1.account = {
      id: 1,
      name: 'account name'
    }

    const responseMock = {
      status: 200,
      data: content1
    }

    httpFake.get = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsApiV4(httpFake).getById(1)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(responseParsed)
  })

  test('request create', async () => {
    const responseMock = {
      status: 201,
      data: content1
    }

    httpFake.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsApiV4(httpFake).create({
      name: 'alarm name',
      description: 'desc',
      timeConfirmation: 1,
      initialHour: '10:00',
      finalHour: '10:00',
      daysWeek: [1, 2],
      daysRetention: 2,
      status: true,
      accountId: 1,
      categoryId: 2
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(responseParsed)
  })

  test('request create2', async () => {
    const responseMock = {
      status: 201,
      data: content2
    }

    httpFake.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsApiV4(httpFake).create({
      name: 'alarm name',
      description: 'desc',
      timeConfirmation: 1,
      initialHour: null,
      finalHour: null,
      daysWeek: [1, 2],
      daysRetention: null,
      status: true,
      accountId: 2,
      categoryId: 2
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(responseParsed2)
  })

  test('request update', async () => {
    const responseMock = {
      status: 201,
      data: content1
    }

    httpFake.put = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsApiV4(httpFake).update(1, {
      name: 'alarm name',
      description: 'desc',
      timeConfirmation: 1,
      initialHour: '',
      finalHour: '',
      daysWeek: [1, 2],
      daysRetention: 1,
      status: true
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(responseParsed)
  })

  test('request update2', async () => {
    const responseMock = {
      status: 201,
      data: content1
    }

    httpFake.put = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsApiV4(httpFake).update(1, {
      name: 'alarm name',
      description: 'desc',
      timeConfirmation: 1,
      initialHour: '1:00',
      finalHour: '1:00',
      daysWeek: [1, 2],
      daysRetention: null,
      status: true
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(responseParsed)
  })

  test('request delete', async () => {
    const responseMock = {
      status: 204,
      data: null
    }

    httpFake.post = jest.fn().mockImplementationOnce(() => responseMock)

    const result = await alarmsApiV4(httpFake).delete(1)

    expect(result.status).toBe(204)
  })

  test('should correctly parse a valid Tdo object', () => {
    const input = {
      name: 'Test',
      description: 'Description',
      timeConfirmation: 10,
      initialHour: '08:00',
      finalHour: '18:00',
      daysWeek: [1, 2, 3],
      daysRetention: 7,
      status: true,
      accountId: 123,
      categoryId: 456
    }

    const expectedOutput = {
      name: 'Test',
      description: 'Description',
      initial_hour: '08:00',
      final_hour: '18:00',
      days_week: [1, 2, 3],
      days_retention: '7',
      alarms_category_id: 456,
      time_confirmation: 10,
      account_id: 123,
      status: true
    }

    expect(parsePayloadHelper(input)).toEqual(expectedOutput)
  })

  test('should return null for nullable fields', () => {
    const input = {
      name: 'Test',
      description: 'Description',
      timeConfirmation: 10,
      initialHour: null,
      finalHour: null,
      daysWeek: [],
      daysRetention: null,
      status: false,
      accountId: 123,
      categoryId: 456
    }

    const expectedOutput = {
      name: 'Test',
      description: 'Description',
      initial_hour: null,
      final_hour: null,
      days_week: [],
      days_retention: null,
      alarms_category_id: 456,
      time_confirmation: 10,
      account_id: 123,
      status: false
    }

    expect(parsePayloadHelper(input)).toEqual(expectedOutput)
  })

  test('should set account_id as undefined if categoryId is ALARMS_CATEGORY_USER_ID', () => {
    const input = {
      name: 'Test',
      description: 'Description',
      timeConfirmation: 10,
      initialHour: '08:00',
      finalHour: '18:00',
      daysWeek: [1, 2, 3],
      daysRetention: 7,
      status: true,
      accountId: 123,
      categoryId: ALARMS_CATEGORY_USER_ID
    }

    const result = parsePayloadHelper(input)
    expect(result.account_id).toBeUndefined()
  })

  test('should correctly parse a valid TdoUpdate object', () => {
    const input = {
      name: 'Test',
      description: 'Description',
      timeConfirmation: 10,
      initialHour: '08:00',
      finalHour: '18:00',
      daysWeek: [1, 2, 3],
      daysRetention: 7,
      status: true
    }

    const expectedOutput = {
      name: 'Test',
      description: 'Description',
      initial_hour: '08:00',
      final_hour: '18:00',
      days_week: [1, 2, 3],
      days_retention: 7,
      time_confirmation: 10,
      status: true
    }

    expect(alarmsParsePayloadUpdate(input)).toEqual(expectedOutput)
  })

  test('should return null for nullable fields when they are undefined', () => {
    const input = {
      name: 'Test',
      description: 'Description',
      timeConfirmation: 10,
      initialHour: null,
      finalHour: null,
      daysWeek: [],
      daysRetention: null,
      status: false
    }

    const expectedOutput = {
      name: 'Test',
      description: 'Description',
      initial_hour: null,
      final_hour: null,
      days_week: [],
      days_retention: null,
      time_confirmation: 10,
      status: false
    }

    expect(alarmsParsePayloadUpdate(input)).toEqual(expectedOutput)
  })
})
