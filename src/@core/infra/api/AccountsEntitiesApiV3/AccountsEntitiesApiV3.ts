import { IAccountsEntities } from '@/@core/domain/AccountsEntities'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'

import {
  accountsEntitiesParseResponse,
  accountsEntitiesParseSearch
} from './AccountsEntitiesApiV3.helpers'
import {
  AccountsEntitiesResponse,
  AccountsEntitiesPayloadSearch
} from './AccountsEntitiesApiV3.types'

export const accountsEntitiesApiV3 = (http: IHttpClient) => ({
  get: async (search: AccountsEntitiesPayloadSearch = {}) => {
    const searchParsed = accountsEntitiesParseSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/accounts-entities',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<AccountsEntitiesResponse>
    >(url)

    const itemsParsed: IAccountsEntities[] = (data?.items ?? []).map(
      accountsEntitiesParseResponse
    )

    const dataParsed = httpParseList<IAccountsEntities>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  }
})
