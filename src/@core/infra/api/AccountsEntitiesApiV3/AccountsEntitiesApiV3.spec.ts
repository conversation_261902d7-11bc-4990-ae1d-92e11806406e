import { cleanup } from '@testing-library/react'

import {
  accountsEntitiesMock1,
  accountsEntitiesResponseMock1
} from '@/__mock__/content/api-accounts-entities.content'
import { httpFake } from '@/@core/infra/http'

import { accountsEntitiesApiV3 } from './AccountsEntitiesApiV3'

cleanup()

describe('src/@core/infra/api/AccountsEntitiesApiV3/AccountsEntitiesApiV3', () => {
  test('request get without query', async () => {
    httpFake.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const { status, data } = await accountsEntitiesApiV3(httpFake).get()

    expect(status).toBe(200)
    expect(data.items).toHaveLength(0)
  })

  test('request get with query', async () => {
    httpFake.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [accountsEntitiesResponseMock1],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 1
      }
    })

    const { status, data } = await accountsEntitiesApiV3(httpFake).get({
      sort: 'id',
      order: 'asc',
      accountId: 1
    })

    expect(status).toBe(200)
    expect(data.items).toHaveLength(1)
    expect(data.items[0]).toEqual(accountsEntitiesMock1)
  })

  test('error request get', async () => {
    httpFake.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await accountsEntitiesApiV3(httpFake).get({
      sort: 'id',
      order: 'asc',
      accountId: 1
    })

    expect(status).toEqual(500)
    expect(data.items).toHaveLength(0)
  })
})
