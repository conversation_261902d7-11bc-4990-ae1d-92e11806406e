import { IAccountsEntities } from '@/@core/domain/AccountsEntities'
import {
  AccountsEntitiesResponse,
  AccountsEntitiesPayloadSearch
} from './AccountsEntitiesApiV3.types'

export const accountsEntitiesParseSearch = (
  search: AccountsEntitiesPayloadSearch
) => ({
  _sort: search?.sort,
  _order: search?.order,
  accountId: search?.accountId
})

export const accountsEntitiesParseResponse = (
  data: AccountsEntitiesResponse
): IAccountsEntities => ({
  entityId: data.entity.id,
  entityName: data.entity.name,
  entityCompanyId: data.entity.company_id_parent,
  accountEntityId: data.account_entity.id,
  accountEntityType: data.account_entity.type
})
