import { httpFake } from '@/@core/infra/http'
import {
  companiesTypesMock1,
  companiesTypesResponseMock1
} from '@/__mock__/content/api-companies-types.content'
import { cleanup } from '@testing-library/react'
import { companiesTypesApiV4 } from './CompaniesTypesApiV4'

cleanup()

describe('src/@core/infra/api/companiesTypesApiV4/companiesTypesApiV4', () => {
  test('request get without result', async () => {
    const responseMock = {
      status: 200,
      data: {
        items: []
      }
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await companiesTypesApiV4(httpFake).get()

    expect(status).toBe(200)
    expect(data.items).toHaveLength(0)
  })
  test('request get with result', async () => {
    const responseMock = {
      status: 200,
      data: {
        items: [companiesTypesResponseMock1]
      }
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await companiesTypesApiV4(httpFake).get()

    expect(status).toEqual(200)
    expect(data.items).toHaveLength(1)
    expect(data.items[0]).toEqual(companiesTypesMock1)
  })
  test('error request', async () => {
    const responseMock = {
      status: 500,
      data: null
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const { status, data } = await companiesTypesApiV4(httpFake).get()

    expect(status).toEqual(500)
    expect(data.items).toHaveLength(0)
  })
})
