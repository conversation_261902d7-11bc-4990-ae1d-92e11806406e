import ICompaniesTypes from '@/@core/domain/CompaniesTypes'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpParseList from '@/@core/utils/httpParseList'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import { parseResponseDataHelper } from './CompaniesTypesApiV4.helper'
import { CompaniesTypesResponse } from './CompaniesTypesApiV4.types'

export const companiesTypesApiV4 = (http: IHttpClient) => ({
  get: async () => {
    const url = httpPrepareUrl({
      url: '/api/v4/companies-types'
    })

    const { status, data } = await http.get<
      IHttpResponseList<CompaniesTypesResponse>
    >(url)

    const itemsParsed: ICompaniesTypes[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    const dataParsed = httpParseList<ICompaniesTypes>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  }
})
