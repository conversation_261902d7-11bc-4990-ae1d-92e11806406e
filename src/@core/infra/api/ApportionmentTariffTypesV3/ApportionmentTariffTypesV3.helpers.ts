import { IApportionmentTariffTypes } from '@/@core/domain/ApportionmentTariffTypes'
import {
  IApportionmentTariffTypesResponse,
  IApportionmentTariffTypesSearch
} from './ApportionmentTariffTypesV3.types'

export const apportionmentTariffTypesParseSearch = (
  search: IApportionmentTariffTypesSearch
) => ({
  type_id: search?.typeId
})
export const apportionmentTariffTypesParseResponse = (
  data: IApportionmentTariffTypesResponse
): IApportionmentTariffTypes => ({
  id: data.id,
  name: data.name
})
