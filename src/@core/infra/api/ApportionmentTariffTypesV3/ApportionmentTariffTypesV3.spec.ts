import { cleanup } from '@testing-library/react'
import { http } from '@/@core/infra/http'
import { apportionmentTariffTypesV3 } from './ApportionmentTariffTypesV3'
import {
  apportionmentTariffTypesResponseMock1,
  apportionmentTariffTypesResponseMock2
} from '@/__mock__/content/api-apportionment-tariff-types'

cleanup()

describe('src/@core/infra/api/ApportionmentTariffTypesV3', () => {
  test('request get width result no data', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })
    const result = await apportionmentTariffTypesV3(http).get()

    expect(result.status).toBe(204)
    expect(result.data).toHaveLength(0)
  })
  test('request get', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          apportionmentTariffTypesResponseMock1,
          apportionmentTariffTypesResponseMock2
        ]
      }
    })
    const result = await apportionmentTariffTypesV3(http).get({
      typeId: 3
    })

    expect(result.status).toBe(200)
    expect(result.data).toHaveLength(2)
  })
})
