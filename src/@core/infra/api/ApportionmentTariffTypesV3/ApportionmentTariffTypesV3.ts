import { IApportionmentTariffTypes } from '@/@core/domain/ApportionmentTariffTypes'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpFilterParams from '@/@core/utils/httpFilterParams'

import {
  IApportionmentTariffTypesResponse,
  IApportionmentTariffTypesSearch
} from './ApportionmentTariffTypesV3.types'
import {
  apportionmentTariffTypesParseResponse,
  apportionmentTariffTypesParseSearch
} from './ApportionmentTariffTypesV3.helpers'

export const apportionmentTariffTypesV3 = (http: IHttpClient) => ({
  get: async (search: IApportionmentTariffTypesSearch = {}) => {
    const searchParsed = apportionmentTariffTypesParseSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionment-tariff-types',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentTariffTypesResponse>
    >(url)

    const dataParsed: IApportionmentTariffTypes[] = (data?.items ?? []).map(
      apportionmentTariffTypesParseResponse
    )

    return {
      status,
      data: dataParsed
    }
  }
})
