import { cleanup } from '@testing-library/react'

import {
  accountMock1,
  accountMock2,
  accountResponseMock1,
  accountResponseMock2
} from '@/__mock__/content/api-accounts.content'
import { http } from '@/@core/infra/http'

import { accountsApiV3 } from './AccountsApiV3'

cleanup()

describe('src/@core/infra/api/AccountsApiV3/AccountsApiV3', () => {
  test('request get without query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await accountsApiV3(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items).toHaveLength(0)
  })

  test('request get with query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [accountResponseMock1, accountResponseMock2],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await accountsApiV3(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      statusIds: [1, 2]
    })

    expect(result.status).toBe(200)
    expect(result.data.items).toHaveLength(2)
    expect(result.data.items[0]).toEqual(accountMock1)
    expect(result.data.items[1]).toEqual(accountMock2)
  })

  test('error request get', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await accountsApiV3(http).get({
      q: '',
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      statusIds: [1, 2]
    })

    expect(status).toEqual(500)
    expect(data.items).toHaveLength(0)
  })

  test('request getById', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: accountResponseMock1
    })

    const { status, data } = await accountsApiV3(http).getById(1)

    expect(status).toBe(200)
    expect(data).toEqual(accountMock1)
  })

  test('request create', async () => {
    http.post = jest.fn().mockReturnValue({
      status: 201,
      data: accountResponseMock1
    })

    const { status, data } = await accountsApiV3(http).create({
      name: 'account name1',
      moduleIds: [1, 2, 3],
      management: 0
    })

    expect(status).toBe(201)
    expect(data).toEqual(accountMock1)
  })

  test('request update', async () => {
    http.put = jest.fn().mockReturnValue({
      status: 201,
      data: accountResponseMock1
    })

    const { status, data } = await accountsApiV3(http).update(1, {
      name: 'account name1',
      moduleIds: [1, 2, 3],
      accountStatusId: 1,
      management: 0
    })

    expect(status).toBe(201)
    expect(data).toEqual(accountMock1)
  })

  test('request delete', async () => {
    const responseMock = {
      status: 204,
      data: null
    }

    http.post = jest.fn().mockImplementationOnce(() => responseMock)

    const { status } = await accountsApiV3(http).delete(1, {
      pincode: 1234
    })

    expect(status).toBe(204)
  })

  test('request notification', async () => {
    const responseMock = {
      status: 200,
      data: null
    }

    http.put = jest.fn().mockImplementationOnce(() => responseMock)

    const { status } = await accountsApiV3(http).notification(1, {
      notification: false
    })

    expect(status).toBe(200)
  })
})
