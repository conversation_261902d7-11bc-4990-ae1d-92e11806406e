import IAccount from '@/@core/domain/Account'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'

import {
  accountsParsePayloadCreate,
  accountsParsePayloadUpdate,
  accountsParseResponse,
  accountsParseSearch
} from './AccountsApiV3.hepers'
import {
  IAccountsRequestCreate,
  IAccountsRequestUpdate,
  IAccountsRequestNotification,
  IAccountsResponse,
  IAccountsPayloadCreate,
  IAccountsPayloadSearch,
  IAccountsPayloadUpdate,
  IAccountsPayloadNotification,
  IAccountsPayloadDelete
} from './AccountsApiV3.types'

export const accountsApiV3 = (http: IHttpClient) => ({
  get: async (search: IAccountsPayloadSearch = {}) => {
    const searchParsed = accountsParseSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/accounts',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IAccountsResponse>
    >(url)

    const itemsParsed: IAccount[] = (data?.items ?? []).map(
      accountsParseResponse
    )

    const dataParsed = httpParseList<IAccount>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: `/api/v3/accounts`,
      id
    })

    const { status, data } = await http.get<IAccountsResponse>(url)

    const dataParsed: IAccount = accountsParseResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: IAccountsPayloadCreate) => {
    const payload: IAccountsRequestCreate = accountsParsePayloadCreate(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/accounts`
    })

    const { status, data } = await http.post<IAccountsResponse>(url, payload)

    const dataParsed: IAccount = accountsParseResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: IAccountsPayloadUpdate) => {
    const payload: IAccountsRequestUpdate = accountsParsePayloadUpdate(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/accounts`,
      id
    })

    const { status, data } = await http.put<IAccountsResponse>(url, payload)

    const dataParsed: IAccount = accountsParseResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number, payload: IAccountsPayloadDelete) => {
    const { status } = await http.post(
      `/api/v3/accounts/${id}/confirm-delete`,
      payload
    )
    return { status }
  },
  notification: async (id: number, data: IAccountsPayloadNotification) => {
    const payload: IAccountsRequestNotification = {
      notification: data.notification
    }

    const { status } = await http.put(
      `/api/v3/accounts-notification/${id}`,
      payload
    )

    return { status }
  }
})
