export interface IAccountsPayloadSearch {
  q?: string
  sort?: 'id' | 'name'
  order?: 'asc' | 'desc'
  limit?: number
  page?: number
  statusIds?: number[]
  management?: number
}
export interface IAccountsResponse {
  id: number
  name: string
  status: {
    id: number
    name: string
  }
  modules: {
    id: number
    name: string
  }[]
  management: number
}
export interface IAccountsPayloadCreate {
  name: string
  moduleIds: number[]
  management: number
}
export interface IAccountsRequestCreate {
  name: string
  moduleIds: number[]
  management: number
}
export interface IAccountsPayloadUpdate {
  name: string
  moduleIds: number[]
  accountStatusId: number
  management: number
}
export interface IAccountsRequestUpdate {
  name: string
  moduleIds: number[]
  account_status_id: number
  management: number
}
export interface IAccountsPayloadDelete {
  pincode: number
}

export interface IAccountsPayloadNotification {
  notification: boolean
}
export interface IAccountsRequestNotification {
  notification: boolean
}
