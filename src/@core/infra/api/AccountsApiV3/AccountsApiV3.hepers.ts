import IAccount from '@/@core/domain/Account'
import {
  IAccountsRequestCreate,
  IAccountsRequestUpdate,
  IAccountsResponse,
  IAccountsPayloadCreate,
  IAccountsPayloadSearch,
  IAccountsPayloadUpdate
} from './AccountsApiV3.types'

export const accountsParseSearch = (search: IAccountsPayloadSearch) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  statusIds: search?.statusIds,
  management: search?.management
})
export const accountsParseResponse = (data: IAccountsResponse): IAccount => ({
  id: data.id,
  name: data.name,
  statusId: data.status.id,
  statusName: data.status.name,
  modules: data.modules.map(({ id, name }) => ({ id, name })),
  management: data.management
})
export const accountsParsePayloadCreate = (
  data: IAccountsPayloadCreate
): IAccountsRequestCreate => ({
  name: data.name,
  moduleIds: data.moduleIds,
  management: data.management
})
export const accountsParsePayloadUpdate = (
  data: IAccountsPayloadUpdate
): IAccountsRequestUpdate => ({
  name: data.name,
  moduleIds: data.moduleIds,
  account_status_id: data.accountStatusId,
  management: data.management
})
