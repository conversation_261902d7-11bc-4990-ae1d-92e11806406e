import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import { reportsDownloadApiV3 } from './ReportsDownloadApiV3'

cleanup()

describe('src/@core/infra/api/ReportsDownloadApiV3/ReportsDownloadApiV3', () => {
  test('request download without message return', async () => {
    http.post = jest.fn().mockReturnValue({
      status: 200,
      data: null
    })

    const result = await reportsDownloadApiV3(http).download({})

    expect(result.data).toBe('')
  })
  test('request download', async () => {
    const message = 'Sucesso. Processando o relatório para o envio por email.'

    http.post = jest.fn().mockReturnValue({
      status: 200,
      data: [message]
    })

    const result = await reportsDownloadApiV3(http).download({})

    expect(result.data).toBe(message)
  })
})
