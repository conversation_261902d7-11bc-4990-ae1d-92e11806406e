import { IHttpClient } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

export const reportsDownloadApiV3 = (http: IHttpClient) => ({
  download: async (payload: unknown) => {
    const url = httpPrepareUrl({
      url: '/api/v3/reports-download'
    })

    const { status, data } = await http.post<[string]>(url, payload)

    return {
      status,
      data: data?.[0] ?? ''
    }
  }
})
