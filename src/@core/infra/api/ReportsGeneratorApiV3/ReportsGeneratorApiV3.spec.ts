import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  reportsGeneratorMock1,
  reportsGeneratorMock2,
  reportsGeneratorResponseMock1,
  reportsGeneratorResponseMock2
} from '@/__mock__/content/api-reports-generator.content'
import { reportsGeneratorApiV3 } from './ReportsGeneratorApiV3'

cleanup()

describe('src/@core/infra/api/ReportsGeneratorApiV3/ReportsGeneratorApiV3', () => {
  test('request generator full data', async () => {
    http.post = jest.fn().mockReturnValue({
      status: 200,
      data: reportsGeneratorResponseMock1
    })

    const result = await reportsGeneratorApiV3(http).generate({})

    expect(result.data.table).toEqual(reportsGeneratorMock1.table)
  })
  test('request generator partial data', async () => {
    http.post = jest.fn().mockReturnValue({
      status: 200,
      data: reportsGeneratorResponseMock2
    })

    const result = await reportsGeneratorApiV3(http).generate({})

    expect(result.data.table).toEqual(reportsGeneratorMock2.table)
  })
})
