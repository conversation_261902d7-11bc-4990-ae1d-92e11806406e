import dayjs from 'dayjs'

import { IReportGenerator } from '@/@core/domain/ReportGenerator'
import { IReportGeneratorResponse } from './ReportsGeneratorApiV3.types'
import { formatCells } from './ReportsGeneratorApiV3.utils'

export const parseResponse = (
  data: IReportGeneratorResponse
): IReportGenerator => {
  const {
    information,
    table: { header, body: bodyResponse }
  } = data

  const format: string = 'DD/MM/YYYY HH:mm'

  const initial: string = information.requested_period.initial
    ? dayjs(information.requested_period.initial).format(format)
    : dayjs().format(format)

  const final: string = information.requested_period.final
    ? dayjs(information.requested_period.final).format(format)
    : dayjs().format(format)

  const requestDate: string = information.request_date
    ? dayjs(information.request_date).format(format)
    : dayjs().format(format)

  const body = bodyResponse.map((line) =>
    line.map((cell, i) => formatCells(cell, header[i]))
  )

  return {
    information: {
      requestDate: requestDate,
      requestedPeriod: { initial, final }
    },
    table: { header, body }
  }
}
