import { IReportGenerator } from '@/@core/domain/ReportGenerator'
import { IHttpClient } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import { parseResponse } from './ReportsGeneratorApiV3.helpers'
import { IReportGeneratorResponse } from './ReportsGeneratorApiV3.types'

export const reportsGeneratorApiV3 = (http: IHttpClient) => ({
  generate: async (payload: unknown) => {
    const url = httpPrepareUrl({
      url: '/api/v3/reports-generator'
    })

    const { status, data } = await http.post<IReportGeneratorResponse>(
      url,
      payload
    )

    const dataParsed: IReportGenerator = parseResponse(data)

    return {
      status,
      data: dataParsed
    }
  }
})
