import {
  IReportGeneratorTableBody,
  IReportGeneratorTableHeader,
  IReportGeneratorTableHeaderFormat
} from '@/@core/domain/ReportGenerator'
import dayjs from 'dayjs'

const formatCellText = (cell: IReportGeneratorTableBody): string | number => {
  return cell ?? ''
}
const formatCellDate = (cell: IReportGeneratorTableBody): string => {
  if (Number.isInteger(cell)) {
    return dayjs.unix(Number(cell)).format('DD/MM/YYYY')
  }

  return dayjs(cell).format('DD/MM/YYYY')
}
const formatCellDateTime = (cell: IReportGeneratorTableBody): string => {
  if (Number.isInteger(cell)) {
    return dayjs.unix(Number(cell)).format('DD/MM/YYYY HH:mm')
  }

  return dayjs(cell).format('DD/MM/YYYY HH:mm')
}
const formatCellDateHour = (cell: IReportGeneratorTableBody): string => {
  return dayjs(cell).format('DD/MM/YYYY HH:mm')
}
const formatCellTime = (cell: IReportGeneratorTableBody): string => {
  if (Number.isInteger(cell)) {
    return dayjs.unix(Number(cell)).format('HH:mm')
  }

  return dayjs(cell).format('HH:mm')
}
const formatCellNumber = (
  cell: IReportGeneratorTableBody
): number | string | null => {
  if (typeof cell === 'number') return cell
  if (typeof cell === 'string' && !isNaN(Number(cell))) return Number(cell)
  return cell ?? ''
}

export const formatCells = (
  cell: IReportGeneratorTableBody,
  option: IReportGeneratorTableHeader
) => {
  const formats: Record<IReportGeneratorTableHeaderFormat, Function> = {
    text: formatCellText,
    date: formatCellDate,
    dateTime: formatCellDateTime,
    dateHour: formatCellDateHour,
    time: formatCellTime,
    number: formatCellNumber
  }

  return formats[option.format]?.(cell) ?? cell
}
