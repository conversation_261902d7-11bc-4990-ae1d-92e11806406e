import IAccountsManagement from '@/@core/domain/AccountsManagement'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import {
  accountsManagementParsePayloadCreate,
  accountsManagementParseResponse,
  accountsManagementParseSearch
} from './AccountsManagementApiV3.hepers'
import {
  IAccountsManagementRequestCreate,
  IAccountsManagementResponse,
  IAccountsManagementPayloadCreate,
  IAccountsManagementPayloadSearch,
  IAccountsManagementResponseCreate
} from './AccountsManagementApiV3.types'

export const accountsManagementApiV3 = (http: IHttpClient) => ({
  get: async (search: IAccountsManagementPayloadSearch = {}) => {
    const searchParsed = accountsManagementParseSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/accounts-management',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IAccountsManagementResponse>
    >(url)

    const itemsParsed: IAccountsManagement[] = (data?.items ?? []).map(
      accountsManagementParseResponse
    )

    const dataParsed = httpParseList<IAccountsManagement>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: IAccountsManagementPayloadCreate) => {
    const payload: IAccountsManagementRequestCreate =
      accountsManagementParsePayloadCreate(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/accounts-management`
    })

    const { status } = await http.post<IAccountsManagementResponseCreate>(
      url,
      payload
    )

    return { status }
  }
})
