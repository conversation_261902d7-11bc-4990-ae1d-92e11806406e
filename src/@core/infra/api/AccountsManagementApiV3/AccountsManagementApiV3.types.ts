export interface IAccountsManagementPayloadSearch {
  q?: string
  sort?: 'id'
  order?: 'asc' | 'desc'
  limit?: number
  page?: number
  accountId?: number
}
export interface IAccountsManagementResponse {
  account_id: number
  account_name: string
}
export interface IAccountsManagementResponseCreate {
  message: string
}
export interface IAccountsManagementPayloadCreate {
  accountManagementId: number
  accountIds: number[]
}
export interface IAccountsManagementRequestCreate {
  account_management_id: number
  accounts: number[]
}
