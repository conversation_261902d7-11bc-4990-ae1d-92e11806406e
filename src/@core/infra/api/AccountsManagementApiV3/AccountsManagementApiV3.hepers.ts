import IAccountsManagement from '@/@core/domain/AccountsManagement'
import {
  IAccountsManagementRequestCreate,
  IAccountsManagementResponse,
  IAccountsManagementPayloadCreate,
  IAccountsManagementPayloadSearch
} from './AccountsManagementApiV3.types'

export const accountsManagementParseSearch = (
  search: IAccountsManagementPayloadSearch
) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  account_id: search?.accountId
})
export const accountsManagementParseResponse = (
  data: IAccountsManagementResponse
): IAccountsManagement => ({
  account: {
    id: data.account_id,
    name: data.account_name
  },
  accountId: data.account_id,
  accountName: data.account_name
})

export const accountsManagementParsePayloadCreate = (
  data: IAccountsManagementPayloadCreate
): IAccountsManagementRequestCreate => ({
  account_management_id: data.accountManagementId,
  accounts: data.accountIds
})
