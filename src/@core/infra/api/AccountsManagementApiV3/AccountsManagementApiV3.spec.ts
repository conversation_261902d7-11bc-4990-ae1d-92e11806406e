import { cleanup } from '@testing-library/react'

import {
  accountsManagementMock1,
  accountsManagementMock2,
  accountsManagementResponseMock1,
  accountsManagementResponseMock2
} from '@/__mock__/content/api-accounts-management.content'
import { http } from '@/@core/infra/http'

import { accountsManagementApiV3 } from './AccountsManagementApiV3'

cleanup()

describe('src/@core/infra/api/AccountsApiV3/AccountsApiV3', () => {
  test('request get without query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const result = await accountsManagementApiV3(http).get()

    expect(result.status).toBe(500)
    expect(result.data.items).toHaveLength(0)
  })

  test('request get with query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          accountsManagementResponseMock1,
          accountsManagementResponseMock2
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await accountsManagementApiV3(http).get({
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1,
      accountId: 1
    })

    expect(result.status).toBe(200)
    expect(result.data.items).toHaveLength(2)
    expect(result.data.items[0]).toEqual(accountsManagementMock1)
    expect(result.data.items[1]).toEqual(accountsManagementMock2)
  })

  test('request create', async () => {
    http.post = jest.fn().mockReturnValue({
      status: 201,
      data: accountsManagementResponseMock1
    })

    const { status } = await accountsManagementApiV3(http).create({
      accountManagementId: 1,
      accountIds: [1, 2]
    })

    expect(status).toBe(201)
  })
})
