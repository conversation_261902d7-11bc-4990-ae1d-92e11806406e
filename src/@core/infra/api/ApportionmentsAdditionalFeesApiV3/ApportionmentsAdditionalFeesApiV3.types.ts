import { Order } from '@/types/system'

export interface IApportionmentAdditionalFeesTdoSearch {
  sort?: 'id'
  order?: Order
  limit?: number
  page?: number
  apportionmentId: number
}
export interface IApportionmentAdditionalFeesResponse {
  id: number
  apportionment_id: number
  division_type: { id: number; name: string }
  name: string
  period: string
  value: number
}

export interface IApportionmentAdditionalFeesTdo {
  name: string
  apportionmentId: number
  divisionTypeId: number
  period: string
  value: number
}
export interface IApportionmentAdditionalFeesPayload {
  name: string
  apportionment_id: number
  division_type_id: number
  period: string
  value: number
}
