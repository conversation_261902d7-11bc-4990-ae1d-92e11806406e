import { cleanup } from '@testing-library/react'

import { apportionmentsAdditionalFeesApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import {
  apportionmentAdditionalFeesMock1,
  apportionmentAdditionalFeesMock2,
  apportionmentAdditionalFeesResponseMock1,
  apportionmentAdditionalFeesResponseMock2
} from '@/__mock__/content/api-apportionments-additional-fees.content'

cleanup()

describe('src/@core/infra/api/ApportionmentsAdditionalFeesApiV3', () => {
  it('request get without query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          apportionmentAdditionalFeesMock1,
          apportionmentAdditionalFeesMock2
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await apportionmentsAdditionalFeesApiV3(http).get({
      apportionmentId: 118
    })

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(2)
  })

  it('request get with query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          apportionmentAdditionalFeesMock1,
          apportionmentAdditionalFeesMock2
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await apportionmentsAdditionalFeesApiV3(http).get({
      apportionmentId: 118,
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1
    })

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(2)
  })

  it('request get no data', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })
    const result = await apportionmentsAdditionalFeesApiV3(http).get({
      apportionmentId: 118
    })

    expect(result.status).toBe(204)
    expect(result.data.items.length).toBe(0)
  })

  it('request getById', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: apportionmentAdditionalFeesResponseMock1
    })

    const result = await apportionmentsAdditionalFeesApiV3(http).getById(1)

    expect(result.status).toBe(200)
    expect(result.data).toEqual(apportionmentAdditionalFeesMock1)
  })

  it('request create', async () => {
    http.post = jest.fn().mockReturnValue({
      status: 201,
      data: apportionmentAdditionalFeesResponseMock2
    })

    const result = await apportionmentsAdditionalFeesApiV3(http).create({
      apportionmentId: 1,
      divisionTypeId: 2,
      name: 'Fatura 09',
      period: '2024-09-01',
      value: 213150.22
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(apportionmentAdditionalFeesMock2)
  })

  it('request update', async () => {
    http.put = jest.fn().mockReturnValue({
      status: 201,
      data: apportionmentAdditionalFeesResponseMock1
    })

    const result = await apportionmentsAdditionalFeesApiV3(http).update(186, {
      apportionmentId: 1,
      divisionTypeId: 2,
      name: 'Fatura 09',
      period: '2024-09-01',
      value: 213150.22
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(apportionmentAdditionalFeesMock1)
  })

  it('request delete', async () => {
    http.delete = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const result = await apportionmentsAdditionalFeesApiV3(http).delete(1)

    expect(result.status).toBe(204)
  })
})
