import { IApportionmentAdditionalFees } from '@/@core/domain/ApportionmentAdditionalFees'
import {
  IApportionmentAdditionalFeesResponse,
  IApportionmentAdditionalFeesTdoSearch,
  IApportionmentAdditionalFeesTdo,
  IApportionmentAdditionalFeesPayload
} from './ApportionmentsAdditionalFeesApiV3.types'

export const parseApportionmentAdditionalFeesSearch = (
  search: IApportionmentAdditionalFeesTdoSearch
) => ({
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  apportionment_id: search?.apportionmentId
})

export const parseApportionmentAdditionalFeesResponse = (
  data: IApportionmentAdditionalFeesResponse
): IApportionmentAdditionalFees => {
  return {
    id: data.id,
    apportionmentId: data.apportionment_id,
    divisionType: data.division_type,
    name: data.name,
    period: data.period,
    value: data.value
  }
}

export const paeseApportionmentAdditionalFeesPayload = (
  tdo: IApportionmentAdditionalFeesTdo
): IApportionmentAdditionalFeesPayload => ({
  name: tdo.name,
  apportionment_id: tdo.apportionmentId,
  division_type_id: tdo.divisionTypeId,
  period: tdo.period,
  value: tdo.value
})
