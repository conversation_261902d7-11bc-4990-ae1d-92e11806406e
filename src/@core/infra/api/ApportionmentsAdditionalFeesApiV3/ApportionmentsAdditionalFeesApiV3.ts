import { IApportionmentAdditionalFees } from '@/@core/domain/ApportionmentAdditionalFees'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'

import {
  paeseApportionmentAdditionalFeesPayload,
  parseApportionmentAdditionalFeesResponse,
  parseApportionmentAdditionalFeesSearch
} from './ApportionmentsAdditionalFeesApiV3.hepers'
import {
  IApportionmentAdditionalFeesPayload,
  IApportionmentAdditionalFeesResponse,
  IApportionmentAdditionalFeesTdo,
  IApportionmentAdditionalFeesTdoSearch
} from './ApportionmentsAdditionalFeesApiV3.types'

export const apportionmentsAdditionalFeesApiV3 = (http: IHttpClient) => ({
  get: async (search: IApportionmentAdditionalFeesTdoSearch) => {
    const searchParsed = parseApportionmentAdditionalFeesSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionments-additional-fees',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentAdditionalFeesResponse>
    >(url)

    const itemsParsed = (data?.items ?? []).map(
      parseApportionmentAdditionalFeesResponse
    )

    const dataParsed = httpParseList<IApportionmentAdditionalFees>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  getById: async (id: number) => {
    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-additional-fees`,
      id
    })

    const { status, data } =
      await http.get<IApportionmentAdditionalFeesResponse>(url)
    const dataParsed: IApportionmentAdditionalFees =
      parseApportionmentAdditionalFeesResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: IApportionmentAdditionalFeesTdo) => {
    const payload: IApportionmentAdditionalFeesPayload =
      paeseApportionmentAdditionalFeesPayload(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-additional-fees`
    })

    const { status, data } =
      await http.post<IApportionmentAdditionalFeesResponse>(url, payload)

    const dataParsed: IApportionmentAdditionalFees =
      parseApportionmentAdditionalFeesResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  update: async (id: number, tdo: IApportionmentAdditionalFeesTdo) => {
    const payload: IApportionmentAdditionalFeesPayload =
      paeseApportionmentAdditionalFeesPayload(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-additional-fees`,
      id
    })

    const { status, data } =
      await http.put<IApportionmentAdditionalFeesResponse>(url, payload)

    const dataParsed: IApportionmentAdditionalFees =
      parseApportionmentAdditionalFeesResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const { status } = await http.delete(
      `/api/v3/apportionments-additional-fees/${id}`
    )

    return { status }
  }
})
