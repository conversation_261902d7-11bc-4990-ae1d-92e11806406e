import { IApportionmentMeasuresUnit } from '@/@core/domain/ApportionmentMeasuresUnit'
import {
  IApportionmentMeasuresUnitResponse,
  IApportionmentMeasuresUnitTdoSearch
} from './ApportionmentMeasuresUnitsApiV3.types'

export const parseApportionmentMeasuresUnitSearch = (
  search: IApportionmentMeasuresUnitTdoSearch
) => ({
  type_id: search?.typeId
})

export const parseApportionmentMeasuresUnitResponse = (
  data: IApportionmentMeasuresUnitResponse
): IApportionmentMeasuresUnit => {
  return {
    id: data.id,
    name: data.name
  }
}
