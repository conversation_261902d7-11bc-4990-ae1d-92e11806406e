import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpFilterParams from '@/@core/utils/httpFilterParams'

import {
  parseApportionmentMeasuresUnitResponse,
  parseApportionmentMeasuresUnitSearch
} from './ApportionmentMeasuresUnitsApiV3.hepers'
import {
  IApportionmentMeasuresUnitResponse,
  IApportionmentMeasuresUnitTdoSearch
} from './ApportionmentMeasuresUnitsApiV3.types'

export const apportionmentMeasuresUnitsApiV3 = (http: IHttpClient) => ({
  get: async (search: IApportionmentMeasuresUnitTdoSearch) => {
    const searchParsed = parseApportionmentMeasuresUnitSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionment-measures-units',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentMeasuresUnitResponse>
    >(url)

    const dataParsed = (data?.items ?? []).map(
      parseApportionmentMeasuresUnitResponse
    )

    return {
      status,
      data: dataParsed
    }
  }
})
