import { cleanup } from '@testing-library/react'
import { http } from '@/@core/infra/http'
import {
  apportionmentMeasuresUnitMock1,
  apportionmentMeasuresUnitMock2,
  apportionmentMeasuresUnitResponseMock1,
  apportionmentMeasuresUnitResponseMock2
} from '@/__mock__/content/api-apportionment-measures-units.content'
import { apportionmentMeasuresUnitsApiV3 } from '@/@core/infra/api/ApportionmentMeasuresUnitsApiV3/'

cleanup()

describe('src/@core/infra/api/ApportionmentMeasuresUnitsApiV3', () => {
  it('request get without query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          apportionmentMeasuresUnitResponseMock1,
          apportionmentMeasuresUnitResponseMock2
        ]
      }
    })

    const result = await apportionmentMeasuresUnitsApiV3(http).get({
      typeId: 118
    })

    expect(result.status).toBe(200)
    expect(result.data.length).toBe(2)
  })

  it('request get with query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [apportionmentMeasuresUnitMock1, apportionmentMeasuresUnitMock2]
      }
    })

    const result = await apportionmentMeasuresUnitsApiV3(http).get({
      typeId: 118
    })

    expect(result.status).toBe(200)
    expect(result.data.length).toBe(2)
  })

  it('request get no data', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })
    const result = await apportionmentMeasuresUnitsApiV3(http).get({
      typeId: 118
    })

    expect(result.status).toBe(204)
    expect(result.data.length).toBe(0)
  })
})
