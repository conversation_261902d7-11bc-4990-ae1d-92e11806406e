import { IApportionmentGroupEquipment } from '@/@core/domain/ApportionmentGroupEquipment'
import {
  IApportionmentGroupEquipmentResponse,
  IApportionmentGroupEquipmentTdo,
  IApportionmentGroupEquipmentPayload,
  IApportionmentGroupEquipmentSearch
} from './ApportionmentsGroupsEquipmentsApiV3.types'

export const parseApportionmentGroupEquipmentSearch = (
  search: IApportionmentGroupEquipmentSearch
) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page,
  apportionment_group_id: search?.apportionmentGroupId
})

export const parseApportionmentGroupEquipmentResponse = (
  data: IApportionmentGroupEquipmentResponse
): IApportionmentGroupEquipment => {
  return {
    id: data.id,
    apportionmentGroupId: data.apportionment_group_id,
    equipment: {
      id: data.equipment.id,
      name: data.equipment.nome
    },
    equipmentId: data.equipment.id
  }
}

export const parseApportionmentGroupEquipmentPayload = (
  tdo: IApportionmentGroupEquipmentTdo
): IApportionmentGroupEquipmentPayload => ({
  apportionment_group_id: tdo.apportionmentGroupId,
  equipment_id: tdo.equipmentId
})
