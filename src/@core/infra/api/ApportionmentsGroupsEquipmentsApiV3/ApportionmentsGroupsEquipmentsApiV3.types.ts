export interface IApportionmentGroupEquipmentSearch {
  q?: string
  sort?: 'id'
  order?: 'asc' | 'desc'
  limit?: number
  page?: number
  apportionmentGroupId: number
}
export interface IApportionmentGroupEquipmentResponse {
  id: number
  apportionment_group_id: number
  equipment: { id: number; nome: string }
  equipment_id: number
}

export interface IApportionmentGroupEquipmentTdo {
  apportionmentGroupId: number
  equipmentId: number
}

export interface IApportionmentGroupEquipmentPayload {
  apportionment_group_id: number
  equipment_id: number
}
