import { cleanup } from '@testing-library/react'

import {
  apportionmentGroupEquipmentMock1,
  apportionmentGroupEquipmentResponseMock1
} from '@/__mock__/content/api-apportionments-groups-equipments.content'
import { http } from '@/@core/infra/http'
import { apportionmentsGroupsEquipmentsApiV3 } from '@/@core/infra/api/ApportionmentsGroupsEquipmentsApiV3'

cleanup()

describe('src/@core/infra/api/ApportionmentsGroupsEquipmentsApiV3', () => {
  it('request create', async () => {
    http.post = jest.fn().mockReturnValue({
      status: 201,
      data: apportionmentGroupEquipmentResponseMock1
    })

    const result = await apportionmentsGroupsEquipmentsApiV3(http).create({
      apportionmentGroupId: 1,
      equipmentId: 119
    })

    expect(result.status).toBe(201)
    expect(result.data).toEqual(apportionmentGroupEquipmentMock1)
  })

  it('request delete', async () => {
    http.delete = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const result = await apportionmentsGroupsEquipmentsApiV3(http).delete(1)

    expect(result.status).toBe(204)
  })
})
