import { IApportionmentGroupEquipment } from '@/@core/domain/ApportionmentGroupEquipment'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'

import {
  parseApportionmentGroupEquipmentPayload,
  parseApportionmentGroupEquipmentResponse,
  parseApportionmentGroupEquipmentSearch
} from './ApportionmentsGroupsEquipmentsApiV3.hepers'
import {
  IApportionmentGroupEquipmentPayload,
  IApportionmentGroupEquipmentResponse,
  IApportionmentGroupEquipmentSearch,
  IApportionmentGroupEquipmentTdo
} from './ApportionmentsGroupsEquipmentsApiV3.types'

export const apportionmentsGroupsEquipmentsApiV3 = (http: IHttpClient) => ({
  get: async (search: IApportionmentGroupEquipmentSearch) => {
    const searchParsed = parseApportionmentGroupEquipmentSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/apportionments-groups-equipments',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentGroupEquipmentResponse>
    >(url)

    const itemsParsed = (data?.items ?? []).map(
      parseApportionmentGroupEquipmentResponse
    )

    const dataParsed = httpParseList<IApportionmentGroupEquipment>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  },
  create: async (tdo: IApportionmentGroupEquipmentTdo) => {
    const payload: IApportionmentGroupEquipmentPayload =
      parseApportionmentGroupEquipmentPayload(tdo)

    const url = httpPrepareUrl({
      url: `/api/v3/apportionments-groups-equipments`
    })

    const { status, data } =
      await http.post<IApportionmentGroupEquipmentResponse>(url, payload)

    const dataParsed: IApportionmentGroupEquipment =
      parseApportionmentGroupEquipmentResponse(data)

    return {
      status,
      data: dataParsed
    }
  },
  delete: async (id: number) => {
    const { status } = await http.delete(
      `/api/v3/apportionments-groups-equipments/${id}`
    )

    return { status, data: null }
  }
})

export interface IApportionmentsGroupsEquipmentsApiV3Create {
  status: number
  data: IApportionmentGroupEquipment
}
export interface IApportionmentsGroupsEquipmentsApiV3Delete {
  status: number
}
