import { cleanup } from '@testing-library/react'

import { costCompositionTypesApiV3 } from '@/@core/infra/api/CostCompositionTypesApiV3'
import { http } from '@/@core/infra/http'
import {
  costCompositionTypesMock1,
  costCompositionTypesMock2,
  costCompositionTypesResponseMock1,
  costCompositionTypesResponseMock2
} from '@/__mock__/content/api-cost-composition-types.content'

cleanup()

describe('src/@core/infra/api/CostCompositionTypesApiV3/CostCompositionTypesApiV3', () => {
  test('request get without query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const result = await costCompositionTypesApiV3(http).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [
          costCompositionTypesResponseMock1,
          costCompositionTypesResponseMock2
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0
      }
    })

    const { status, data } = await costCompositionTypesApiV3(http).get({
      sort: 'id',
      order: 'asc',
      limit: 15,
      page: 1
    })

    expect(status).toEqual(200)
    expect(data.items[0]).toEqual(costCompositionTypesMock1)
    expect(data.items[1]).toEqual(costCompositionTypesMock2)
  })

  test('error request get', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await costCompositionTypesApiV3(http).get({
      sort: 'id',
      order: 'asc',
      limit: 15
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      total: null,
      limit: 1,
      page: null,
      lastPage: 0
    })
  })
})
