import { ICostCompositionType } from '@/@core/domain/CostCompositionTypes'
import {
  CostCompositionTypesResponse,
  CostCompositionTypesSearch
} from './CostCompositionTypesApiV3.types'

export const CostCompositionTypesParseSearch = (
  search: CostCompositionTypesSearch
) => ({
  _sort: search?.sort,
  _order: search?.order,
  _limit: search?.limit,
  _page: search?.page
})
export const CostCompositionTypesParseResponse = (
  data: CostCompositionTypesResponse
): ICostCompositionType => {
  return {
    id: data.id,
    name: data.name,
    deletedAt: data.deleted_at ?? ''
  }
}
