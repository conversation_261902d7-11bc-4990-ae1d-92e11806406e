import { ICostCompositionType } from '@/@core/domain/CostCompositionTypes'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import {
  CostCompositionTypesParseResponse,
  CostCompositionTypesParseSearch
} from './CostCompositionTypesApiV3.helpers'
import {
  CostCompositionTypesResponse,
  CostCompositionTypesSearch
} from './CostCompositionTypesApiV3.types'

export const costCompositionTypesApiV3 = (http: IHttpClient) => ({
  get: async (search: CostCompositionTypesSearch = {}) => {
    const searchParsed = CostCompositionTypesParseSearch(search)

    const url = httpPrepareUrl({
      url: '/api/v3/cost-composition-types',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<
      IHttpResponseList<CostCompositionTypesResponse>
    >(url)

    const itemsParsed = (data?.items ?? []).map(
      CostCompositionTypesParseResponse
    )

    const dataParsed = httpParseList<ICostCompositionType>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  }
})
