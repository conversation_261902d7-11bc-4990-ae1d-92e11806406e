import { cleanup } from '@testing-library/react'

import { httpFake, IHttpResponseList } from '@/@core/infra/http'

import { actionsApiV3 } from './ActionsApiV3'
import { Response } from './ActionsApiV3.types'

cleanup()

describe('src/@core/infra/api/ActionsApiV3/ActionsApiV3', () => {
  const content1: Response = {
    id: 1,
    name: 'name',
    slug: 'name',
    module: {
      id: 1,
      name: 'module name',
      slug: 'module_name'
    },
    submodule: {
      id: 1,
      name: 'submodule name',
      slug: 'submodule_name'
    }
  }
  const content2: Response = {
    id: 2,
    name: 'name 2',
    slug: 'name_2',
    module: {
      id: 2,
      name: 'module name 2',
      slug: 'module_name_2'
    },
    submodule: null
  }
  const responseAllMock: IHttpResponseList<Response> = {
    items: [],
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }

  test('request get without query', async () => {
    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const result = await actionsApiV3(httpFake).get()

    expect(result.status).toBe(200)
    expect(result.data.items.length).toBe(0)
  })

  test('request get with query', async () => {
    responseAllMock.items.push(content1)
    responseAllMock.items.push(content2)

    const responseMock = {
      status: 200,
      data: responseAllMock
    }

    httpFake.get = jest.fn().mockReturnValue(responseMock)

    const result2 = await actionsApiV3(httpFake).get({
      q: '',
      sort: 'id',
      order: 'asc',
      accountId: 1
    })

    expect(result2.data.items[0]).toEqual({
      id: 1,
      name: 'name',
      slug: 'name',
      module: { id: 1, name: 'module name', slug: 'module_name' },
      moduleId: 1,
      submodule: { id: 1, name: 'submodule name', slug: 'submodule_name' },
      submoduleId: 1
    })
    expect(result2.data.items[1]).toEqual({
      id: 2,
      name: 'name 2',
      slug: 'name_2',
      module: { id: 2, name: 'module name 2', slug: 'module_name_2' },
      moduleId: 2,
      submodule: null,
      submoduleId: null
    })
  })

  test('error request get', async () => {
    httpFake.get = jest.fn().mockReturnValue({
      status: 500,
      data: null
    })

    const { status, data } = await actionsApiV3(httpFake).get({
      q: '',
      sort: 'id',
      order: 'asc',
      accountId: 1
    })

    expect(status).toEqual(500)
    expect(data).toEqual({
      items: [],
      total: null,
      limit: 1,
      page: null,
      lastPage: 0
    })
  })
})
