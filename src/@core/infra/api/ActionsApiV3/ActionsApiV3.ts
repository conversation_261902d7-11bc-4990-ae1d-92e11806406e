import { IAction } from '@/@core/domain/Actions'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'
import httpParseParams from '@/@core/utils/httpParseParams'
import httpFilterParams from '@/@core/utils/httpFilterParams'
import httpParseList from '@/@core/utils/httpParseList'

import {
  parseResponseDataHelper,
  parseSearchHelper
} from './ActionsApiV3.helper'
import { Response, TdoSearch } from './ActionsApiV3.types'

export const actionsApiV3 = (http: IHttpClient) => ({
  get: async (search: TdoSearch = {}) => {
    const searchParsed = parseSearchHelper(search)

    const url = httpPrepareUrl({
      url: '/api/v3/actions',
      queryString: httpParseParams(httpFilterParams(searchParsed))
    })

    const { status, data } = await http.get<IHttpResponseList<Response>>(url)

    const itemsParsed: IAction[] = (data?.items ?? []).map(
      parseResponseDataHelper
    )

    const dataParsed = httpParseList<IAction>({
      ...data,
      items: itemsParsed
    })

    return {
      status,
      data: dataParsed
    }
  }
})
