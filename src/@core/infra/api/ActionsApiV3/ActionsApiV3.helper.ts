import { IAction } from '@/@core/domain/Actions'
import { Response, TdoSearch } from './ActionsApiV3.types'

export const parseSearchHelper = (search: TdoSearch) => ({
  _q: search?.q,
  _sort: search?.sort,
  _order: search?.order,
  accountId: search?.accountId
})

export const parseResponseDataHelper = (data: Response): IAction => {
  const submoduleId = data.submodule ? data.submodule.id : null

  return {
    id: data.id,
    name: data.name,
    slug: data.slug,
    module: data.module,
    moduleId: data.module.id,
    submodule: data.submodule || null,
    submoduleId
  }
}
