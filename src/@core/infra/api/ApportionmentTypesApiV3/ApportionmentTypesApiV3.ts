import { IApportionmentTypes } from '@/@core/domain/ApportionmentTypes'
import { IHttpClient, IHttpResponseList } from '@/@core/infra/http'
import httpPrepareUrl from '@/@core/utils/httpPrepareUrl'

import { apportionmentTypesParseResponse } from './ApportionmentTypesApiV3.helpers'
import { IApportionmentTypesResponse } from './ApportionmentTypesApiV3.types'

export const apportionmentTypesApiV3 = (http: IHttpClient) => ({
  get: async () => {
    const url = httpPrepareUrl({
      url: '/api/v3/apportionment-types'
    })

    const { status, data } = await http.get<
      IHttpResponseList<IApportionmentTypesResponse>
    >(url)

    const itemsParsed: IApportionmentTypes[] = (data?.items ?? []).map(
      apportionmentTypesParseResponse
    )

    return {
      status,
      data: itemsParsed
    }
  }
})
