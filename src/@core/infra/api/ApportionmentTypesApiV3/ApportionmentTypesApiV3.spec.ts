import { cleanup } from '@testing-library/react'

import { http } from '@/@core/infra/http'
import {
  apportionmentTypeResponseMock1,
  apportionmentTypeResponseMock2
} from '@/__mock__/content/api-apportionment-types.content'
import { apportionmentTypesApiV3 } from './ApportionmentTypesApiV3'

cleanup()

describe('src/@core/infra/api/ApportionmentsResultsV3', () => {
  test('request get without query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: []
      }
    })

    const result = await apportionmentTypesApiV3(http).get()

    expect(result.status).toBe(200)
    expect(result.data).toHaveLength(0)
  })

  test('request get with query', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 200,
      data: {
        items: [apportionmentTypeResponseMock1, apportionmentTypeResponseMock2]
      }
    })

    const result = await apportionmentTypesApiV3(http).get()
    expect(result.status).toBe(200)
    expect(result.data).toHaveLength(2)
  })

  test('request get no data', async () => {
    http.get = jest.fn().mockReturnValue({
      status: 204,
      data: null
    })

    const result = await apportionmentTypesApiV3(http).get()
    expect(result.status).toBe(204)
    expect(result.data).toHaveLength(0)
  })
})
