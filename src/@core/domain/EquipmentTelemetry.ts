import { Order } from '@/types/system'

export interface IEquipmentTelemetry {
  id: number
  stMonitoring: boolean
  stAutoScde: boolean
  stModulation: boolean
  equipment: { id: number; name: string }
  equipmentId: number
  stVirtual: boolean
  stAutoForecast: boolean
}

export interface IEquipmentTelemetrySearch {
  q: string
  sort: 'id' | 'nome'
  order: Order
  limit: number
  page: number
  equipmentId: number
}
