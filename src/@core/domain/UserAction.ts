export default interface IUserAction {
  id: number
  action: {
    id: number
    name: string
  }
  actionId: number
  actionName: string
  module: {
    id: number
    name: string
    slug: string
  }
  moduleId: number
  moduleName: string
  moduleSlug: string
  submodule: {
    id: number
    name: string
    slug: string
  } | null
  submoduleId: number | null
  submoduleName: string | null
  submoduleSlug: string | null
}

export type IUserActionName = 'list' | 'create' | 'delete'

export type IUserActionModuleName =
  | 'users'
  | 'register'
  | 'register-device'
  | 'register-equipment'
  | 'accounts'
  | 'alarms'
  | 'map'
  | 'monitor'
  | 'register-company'
  | 'apportionments'
  | 'reports'
  | 'agents-ccee'
  | 'energetic-statement'
  | 'financial-statement'
  | 'contracts'
  | 'physical-assets'
  | 'dashboard'
  | 'properties'
  | 'distributors'
  | 'integrations'
  | 'installation-document'

type IModule = Record<IUserActionName, boolean>

export type IUserActionGrouped = Record<IUserActionModuleName, IModule>
