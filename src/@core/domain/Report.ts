import { Order } from '@/types/system'

export interface IReportSearch {
  q: string
  order: Order
  sort: 'id' | 'name' | 'created_at'
  page: number
  limit: number
  model?: boolean
}
export interface IReportsList {
  id: number
  name: string
  updatedAt: string
  entityId: number
  entityType: string
  entity: {
    id: number
    name: string
    type: string
  }
  entityName: string
}
export interface IReport {
  id: number
  name: string
  updatedAt: string
  filter: {
    entity_fields: object
    entity_type_fields: object
    entity_data_fields: object
    period_fields: object
    aggregate_fields: object
    type_data: object
  }
}
