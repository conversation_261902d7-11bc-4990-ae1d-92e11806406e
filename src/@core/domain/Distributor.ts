import { Order } from '@/types/system'

export interface IDistributorList {
  id: number
  name: string
  state: {
    id: number
    name: string
  }
  stateId: number
  stateName: string
  type: {
    id: number
    name: string
  }
  typeId: number
  typeName: string
  submarket: {
    id: number
    name: string
    acronyms: string
  }
  submarketId: number
  submarketName: string
}

export interface IDistributor {
  id: number
  name: string
  state: {
    id: number
    name: string
  }
  stateId: number
  stateName: string
  submarket: {
    id: number
    name: string
    acronyms: string
  }
  submarketId: number
  submarketName: string
  type: {
    id: number
    name: string
  }
  typeId: number
  typeName: string
  cnpj: string
  externalId: string
  reactivePost: null | {
    id: number
    startTime: string
    endTime: string
  }
  reactivePostId: null | number
  tariffPost: null | {
    id: number
    peakStartTime: string
    peakEndTime: string
    intermediateStartTime: string | null
    intermediateEndTime: string | null
  }
  tariffPostId: null | number
}
export interface IDistributorSearch {
  q: string
  sort: 'id'
  order: Order
  limit: number
  page: number
  type_id?: number
}
