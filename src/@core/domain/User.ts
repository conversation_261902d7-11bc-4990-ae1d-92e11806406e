import { Order } from '@/types/system'

export interface IUser {
  id: number
  account: {
    id: number
    name: string
    management: number
  } | null
  accountId: number | null
  accountName: string | null
  accountUser: {
    id: number
    userId: number
    admin: boolean
  } | null
  accountUserId: number | null
  accountUserAdmin: boolean
  firstName: string
  lastName: string
  admin: boolean
  avatar: string
  cellphone: string
  email: string
  active: boolean
  receiveAlertEmails: number
  lastAccess: string | null
  cognitoUserId?: string | null
}

export interface IUserSearch {
  q: string
  sort: 'id' | 'name' | 'email' | 'last_access'
  order: Order
  limit: number
  page: number
  account_id: number
}
