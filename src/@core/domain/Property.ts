import { Order } from '@/types/system'

export default interface IProperty {
  id: number
  name: string
  displayName: string
  description: string
  type: string
  storeable: number
  processable: number
  subTypes: { id: number; name: string; type: { id: number; name: string } }[]
}
export interface IPropertySearch {
  q: string
  sort: 'id' | 'nome' | 'storeable'
  order: Order
  limit: number
  page: number
  type?: number | null
  subType?: number | null
}
