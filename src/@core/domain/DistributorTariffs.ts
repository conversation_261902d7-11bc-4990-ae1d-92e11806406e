import { Order } from '@/types/system'

export interface IDistributorTariffs {
  id: number
  type: string
  voltageClassId: number
  vigencyStart: string
  vigencyEnd: string
  tusdMwhCaptiveValue: number
  tusdKwValue: number
  tusdMwhValue: number
  tusdKwCaptiveValue: number
  teMwhValue: number
  distributor: {
    id: number
    nome: string
  }
  distributorId: number
  tariffModalityId: number
  tariffModality: {
    id: number
    name: string
  }
  voltageClass: {
    id: number
    name: string
  }
}
export interface IDistributorTariffsSearch {
  sort: 'id'
  order: Order
  limit: number
  page: number
  validOnly?: boolean
  distributorId?: number
}
