import { AlertProps } from '@mui/material'

export type IToastPosition =
  | 'topLeft'
  | 'topCenter'
  | 'topRight'
  | 'left'
  | 'center'
  | 'right'
  | 'bottomLeft'
  | 'bottomCenter'
  | 'bottomRight'

export type IToastDuration = 'default'

export type IToastType = AlertProps['severity']

export type IToast = {
  uuid: string
  message: string
  position: IToastPosition
  duration: IToastDuration
  type: IToastType
}
