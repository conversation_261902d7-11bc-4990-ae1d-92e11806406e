import { Order } from '@/types/system'

export interface IApportionment {
  id: number
  name: string
  description: string
  company: {
    id: number
    name: string
  }
  apportionmentType: {
    id: number
    name: string
  }
  apportionmentMeasureUnit: {
    id: number
    name: string
  }
  apportionmentTariffType: {
    id: number
    name: string
  }
  costCenterIds: number[]
  mixedConsumption: boolean
}

export interface IApportionmentSearch {
  q: string
  order: Order
  sort: 'id'
  page: number
  limit: number
}
