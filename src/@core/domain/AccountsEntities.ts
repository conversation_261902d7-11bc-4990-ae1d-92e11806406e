import { Order } from '@/types/system'

export interface IAccountsEntities {
  entityId: number
  entityName: string
  entityCompanyId: number | null
  accountEntityId: number
  accountEntityType: string
}

export interface IAccountsEntitiesGrouped {
  entityId: number
  entityName: string
  entityCompanyId: number | null
  accountEntityId: number
  accountEntityType: string
  type: string | null
  subEntities: IAccountsEntitiesGrouped[]
  _uniqueKey: string
  checked: boolean
  showSubEntities: boolean
}

export interface IAccountsEntitiesSearch {
  sort: 'id'
  order: Order
  accountId: number
}
