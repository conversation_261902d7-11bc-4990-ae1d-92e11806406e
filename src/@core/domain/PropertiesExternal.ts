import { Order } from '@/types/system'

export interface IPropertiesExternal {
  id: number
  name: string
  label: string
  model: {
    id: number
    name: string
  }
  property: {
    id: number
    name: string
  }
  accumulated: number
}

export interface IPropertiesExternalSearch {
  q: string
  sort: 'id' | 'nome' | 'label'
  order: Order
  limit: number
  page: number
  accumulated: number
  model_id: number
}
