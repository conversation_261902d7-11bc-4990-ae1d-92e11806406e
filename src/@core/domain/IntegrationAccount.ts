import { Order } from '@/types/system'

export default interface IIntegrationsAccounts {
  id: number
  name: string
  integration: {
    id: number
    name: string
  }
  integrationId: number
  account: {
    id: number
    name: string
  }
  accountId: number
  user: {
    id: number
    name: string
  }
  userId: number
}

export interface IIntegrationAccount {
  id: number
  name: string
  integration: {
    id: number
    name: string
  }
  integrationId: number
  account: {
    id: number
    name: string
  }
  accountId: number
  user: {
    id: number
    name: string
  }
  userId: number
  token: string | null
  username: string | null
  frequency: {
    id: number
    name: string
  }
  delay: number
}

export interface IIntegrationAccountSearch {
  q: string
  sort: 'id' | 'name' | 'created_at'
  order: Order
  limit: number
  page: number
  integrationId?: number
  accountId?: number
}
