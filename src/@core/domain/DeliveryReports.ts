import { Order } from '@/types/system'

export default interface IDeliveryReports {
  id: number
  companyName: string
  companyId: number
  company: {
    id: number
    name: string
  }
  equipment: {
    id: number
    name: string
    device: {
      code: string
      id: number
      typeId: number
    } | null
  }
  equipmentId: number
  equipmentName: string
  luc: string
  noc: string
  installer: string
  date: string
  deviceCode: string
  deviceId: number
  status: string
  documentUrl: string | null
  protocol_settings: string
  address_settings: string
  baudRate_settings: string
  parity_settings: string
  currentRelation_settings: string
  potentialRelation_settings: string
  meterConstant_settings: string
  powerSystem_settings: string
  deviceModel_settings: string
}
export interface IDeliveryReportsSearch {
  q: string
  order: Order
  sort: 'id' | 'name'
  page: number
  limit: number
}
