import { Order } from '@/types/system'

export interface IMonitoring {
  deviceId: number
  code: string
  equipmentId: number
  equipmentName: string | null
  status: string
  lastMessage: string | null
  lastAlarm: string | null
  typeId: number
  typeName: string
  subTypeId: number
  subTypeName: string
  companyId: number | null
  companyName: string | null
  version: string
  distributorId: number | null
  distributorName: string | null
  location: string
  connection: string | null
}

export interface IMonitoringSearch {
  q: string
  order: Order
  sort: 'equipamento_id' | 'status_id' | 'last_message'
  page: number
  limit: number
  statusId: number
  subtypeId: number
}
