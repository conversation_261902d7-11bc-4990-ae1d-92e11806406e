export interface IIntegrationEquipment {
  id: number
  active: boolean
  externalId: string
  integration: {
    id: number
    name: string
  }
  integrationAccount: {
    id: number
    name: string
  }
  equipment: {
    id: number
    name: string
  }
  route: {
    slug: string
    name: string
  }
}

export interface IIntegrationEquipmentSearch {
  q: string
  sort: 'id'
  order: 'asc' | 'desc'
  limit: number
  page: number
  equipmentId: number
  integrationAccountId: number
}
