export type IDeliveryReportsVariablesDocuments = {
  id: number
  deliveryReportVariableId: number
  deviceDocumentId: number
  deviceDocument: {
    id: number
    originalName: string
    filename: string
    deviceId: number
    originalPath: string
    smallerPath: string
    biggerPath: string
  }
}

export default interface IDeliveryReportsVariables {
  id: number
  deliveryReportId: number
  variable: { id: number; name: string } | null
  variableId: number
  variableName: string
  meterValue: string
  pliersAmmeterValue: string
  telemetryValue: string
  documents: IDeliveryReportsVariablesDocuments[]
}
