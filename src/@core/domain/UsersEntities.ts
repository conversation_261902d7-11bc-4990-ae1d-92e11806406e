import { Order } from '@/types/system'

export interface IUsersEntities {
  id: number
  accountUser: {
    id: number
    accountId: number
    userId: number
    admin: boolean
  }
  accountUserId: number
  accountEntity: {
    id: number
    accountId: number
    entityId: number
    entityType: string
  }
  accountEntityId: number
}

export interface IUsersEntitiesSearch {
  sort: 'id'
  order: Order
  limit: number
  page: number
  accountId: number | null
  accountUserId: number | null
}
