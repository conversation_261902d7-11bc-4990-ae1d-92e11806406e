import { Order } from '@/types/system'

export default interface IDevice {
  id: number
  code: string
  connection: string
  urlFirmware: string
  iccid: string
  version: string
  ip: string
  alertSeconds: number
  ssidOp: string
  hourlyFrequency: number
  modelId: number | null
  model: { id: number; name: string } | null
  equipmentId: number | null
  equipment: { id: number; name: string } | null
  companyId: number | null
  company: { id: number; name: string } | null
  typeId: number
  type: { id: number; name: string }
  subtypeId: number
  subtype: { id: number; name: string }
  statusId: number
  status: { id: number; name: string }
  masterId: number | null
  master: { id: number; code: string } | null
  comment: { value: string; date: string } | null
}
export interface IDeviceSearch {
  q: string
  sort: 'id' | 'code' | 'company' | 'equipment' | 'type'
  order: Order
  limit: number
  page: number
  // company: number
  company?: number
  equipment?: number
  account_id?: number
}
