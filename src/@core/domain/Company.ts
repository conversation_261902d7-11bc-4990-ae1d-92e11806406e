import { Order } from '@/types/system'

export interface ICompany {
  id: number
  name: string
  unitCode: string | null
  cnpj: string | null
  corporateName: string | null
  address: string | null
  number: string | null
  complement: string | null
  district: string | null
  zipCode: string | null
  cnae: string | null
  parent: {
    id: number
    name: string
  } | null
  parentId: number | null
  parentName: string | null
  city: {
    id: number
    name: string
  } | null
  cityId: number | null
  cityName: string | null
  state: {
    id: number
    name: string
  } | null
  stateId: number | null
  stateName: string | null
  energyDemandsOffPeak: []
  energyDemandsPeak: []
  account: {
    id: number
    name: string
  } | null
  accountId: number | null
  accountName: string | null
  codeIbge: number | null
  timezone: number
  type: string | null
}

export interface ICompanySearch {
  q: string
  sort: 'id' | 'nome'
  order: Order
  limit: number
  page: number
  external_id?: string[]
  parent_id?: number
  account_id?: number
  type?: string
}
