import { Order } from '@/types/system'

export interface IEquipment {
  id: number
  name: string
  account: {
    id: number
    name: string
  }
  accountId: number
  accountName: string
  company: {
    id: number
    name: string
  } | null
  companyId: number | null
  companyName: string | null
  device: {
    code: string
    id: number
    typeId: number
    modelId: number
  } | null
  model: {
    id: number
    name: string
  } | null
  distributor: {
    id: number
    name: string
  } | null
  distributorId: number | null
  distributorName: string | null
  ruleOperational: {
    id: number
    title: string
  }
  isVirtual: boolean
  scdeKey: string | null
}

export interface IEquipmentSearch {
  q: string
  sort: 'id' | 'nome'
  order: Order
  limit: number
  page: number
  companies?: number[]
  companiesGroup?: number[]
  accountId?: number
  withoutDevice?: boolean
  typeId?: number
}
