export type IReportGeneratorTableHeaderFormat =
  | 'date'
  | 'dateTime'
  | 'dateHour'
  | 'text'
  | 'time'
  | 'number'

export type IReportGeneratorTableHeader = {
  graphic: boolean
  label: string
  greatness: string | null
  format: IReportGeneratorTableHeaderFormat
}

export type IReportGeneratorTableBody = string | number | null

export type IReportGenerator = {
  information: {
    requestDate: string
    requestedPeriod: {
      initial: string
      final: string
    }
  }
  table: {
    header: IReportGeneratorTableHeader[]
    body: IReportGeneratorTableBody[][]
  }
}
