import { Order } from '@/types/system'

export interface IApportionmentsGroup {
  id: number
  apportionmentId: number
  name: string
  numberEquipments: number
  equipments: {
    id: number
    apportionmentGroupId: number
    equipmentId: number
  }[]
}

export interface IApportionmentsGroupSearch {
  q: string
  sort: 'id'
  order: Order
  limit: number
  page: number
  apportionment_id: number
}
