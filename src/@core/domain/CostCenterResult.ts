export interface ICostCenterResult {
  costCenterId: number
  costCenterName: string
  tariffType: string
  tariffValue: number
  date: string
  equipmentsValue: number
  additionalFeesValue: number | null
  additionalConsumptionValue: number | null
  totalValue: number
  consumptionTotal: number
  equipmentsValuePrevious: number
  additionalFeesValuePrevious: number | null
  additionalConsumptionValuePrevious: number | null
  totalValuePrevious: number
  percentageDiffConsumptionUsed: number | null
  percentageDiff: number | null
}
