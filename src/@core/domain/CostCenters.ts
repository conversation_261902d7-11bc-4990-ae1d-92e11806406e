import { Order } from '@/types/system'

export interface ICostCenter {
  id: number
  apportionmentId: number
  name: string
  email: string
  apportionmentTariffType: {
    id: number
    name: string
  }
  equipments: {
    id: number
    costCenterId: number
    equipmentId: number
    usageLimit: number
  }[]
  companyId: number
}

export interface ICostCenterSearch {
  q: string
  sort: 'id' | 'name'
  order: Order
  limit: number
  page: number
  costCenterIds?: number[]
  apportionment_id?: number
}
