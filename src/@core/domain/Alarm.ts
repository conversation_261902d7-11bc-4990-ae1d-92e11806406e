import { Order } from '@/types/system'

export default interface IAlarm {
  id: number
  name: string
  description: string
  timeConfirmation: number
  initialHour: string
  finalHour: string
  daysWeek: number[]
  daysRetention: number
  status: boolean
  account: { id: number; name: string } | null
  accountId: number | null
  category: { id: number; name: string }
  categoryId: number
  readonly: boolean
}

export interface IAlarmSearch {
  q: string
  order: Order
  sort: 'id' | 'name' | 'status'
  page: number
  limit: number
  status: number
  alarmsCategoryId?: number
}
