import {
  IToastDuration,
  IToastPosition,
  IToastType
} from '@/@core/domain/Toast'
import { IValueObserver, valueInput } from './observer.types'

export const loadingObserver = (
  value: valueInput<boolean>
): IValueObserver => ({
  value,
  name: 'app-loading'
})

export const redirectObserver = (
  value: valueInput<string>
): IValueObserver => ({
  value,
  name: 'app-redirect'
})

export const toastAddObserver = (
  value: valueInput<{
    message: string
    position?: IToastPosition
    duration?: IToastDuration
    type?: IToastType
  }>
): IValueObserver => ({
  value,
  name: 'app-toast-add'
})
