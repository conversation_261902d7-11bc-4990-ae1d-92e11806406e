import { cleanup } from '@testing-library/react'
import { observer } from './observer'
import { IValueObserver, valueInput } from './observer.types'

cleanup()

describe('src/@core/domain/observer', () => {
  const TestCallbackObserver = jest.fn()

  const TestObserver = (value: valueInput<number>): IValueObserver => ({
    name: 'test-obser',
    value
  })

  beforeEach(() => {
    TestCallbackObserver.mockReset()
  })

  it('should subscribe and unsubscribe', async () => {
    const unsubscribe = await observer.subscribe(
      TestObserver(TestCallbackObserver)
    )
    unsubscribe()
  })

  it('should use all methods observer', async () => {
    await observer.subscribe(TestObserver(TestCallbackObserver))

    await observer.publish(TestObserver(123))

    expect(TestCallbackObserver).toHaveBeenCalledTimes(1)

    await observer.unsubscribe(TestObserver)

    expect(TestCallbackObserver).toHaveBeenCalledTimes(1)

    observer.reset()

    expect(TestCallbackObserver).toHaveBeenCalledTimes(1)
  })
})
