import { IValueObserver } from './observer.types'

class ObserverMap {
  private observers: Map<string, Function>

  constructor() {
    this.observers = new Map()
  }
  async subscribe(value: IValueObserver) {
    const exist = this.observers.get(value.name)

    if (!exist) {
      this.observers.set(value.name, value.value as Function)
    }

    return () => {
      this.observers.delete(value.name)
    }
  }
  async publish(value: IValueObserver) {
    const events: Function[] = []

    Array.from(this.observers).forEach(([name, callback]) => {
      if (value.name === name) {
        events.push(callback(value.value))
      }
    })

    await Promise.all(events)
  }
  async unsubscribe(callback: Function) {
    const value = callback() as IValueObserver

    const exist = this.observers.has(value.name)

    if (exist) {
      this.observers.delete(value.name)
    }
  }
  reset() {
    this.observers = new Map()
  }
}

export const observer = new ObserverMap()
