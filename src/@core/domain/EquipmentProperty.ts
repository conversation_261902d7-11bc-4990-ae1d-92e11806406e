import { Order } from '@/types/system'

export interface IEquipmentProperty {
  id: number
  equipment: {
    id: number
    name: string
  }
  equipmentId: number
  property: {
    id: number
    name: string
  }
  propertyId: number
  offset: number
  conversionFactor: number
}

export interface IEquipmentPropertySearch {
  sort: 'id'
  order: Order
  limit: number
  page: number
  equipmentId: number
}
