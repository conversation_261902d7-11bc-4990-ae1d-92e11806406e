export interface IAlarmTriggered {
  id: number
  alarmId: number | null
  alarmName: string | null
  companyId: number
  companyName: string
  equipmentId: number | null
  equipmentName: string | null
  triggeredAt: string
  triggeredValues: string
  normalizedAt: string
  normalizedValues: string
  status: string
  actionSupport: {
    deviceStatusAfterTrigger: number | null
    deviceStatusAfterNormalize: number | null
  }
}
