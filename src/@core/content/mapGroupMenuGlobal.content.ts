import { IconName } from '../presentation/shared/ui/icons'

type IMapMenuGlobalGroup =
  | 'zordon'
  | 'powerview'
  | 'dmc-view'
  | 'api-contract-management'
  | 'app-assinatura-solar'
  | 'hub-de-dados'

type IMapMenuGlobalLinks = Record<string, string>

export type IMapMenuGlobal = {
  name: string
  group: IMapMenuGlobalGroup
  links: IMapMenuGlobalLinks
  icon: IconName
}[]

export const mapMenuGlobal: IMapMenuGlobal = [
  {
    name: 'Telemetria',
    group: 'zordon',
    links: {
      hmg: 'https://beta-hmg.zordon.app/',
      prod: 'https://beta.zordon.app/'
    },
    icon: 'telemetry'
  },
  {
    name: 'Mercado Livre de Energia (Stage)',
    group: 'powerview',
    links: {
      hmg: 'https://powerview-poc-cognito.comerc.com.br/',
      prod: 'https://powerview.comerc.com.br/'
    },
    icon: 'freeMarket'
  },
  {
    name: 'Assinatura Solar - DMC View (HMG)',
    group: 'dmc-view',
    links: {
      hmg: 'https://hmg-souvagalume-engine.dmcview.com.br',
      prod: 'https://souvagalume-engine.dmcview.com.br'
    },
    icon: 'solarSubscription'
  },
  {
    name: 'Cadastros Internos - Mercado livre',
    group: 'api-contract-management',
    links: {
      hmg: 'https://cadastrosinternos-dev.comerc.com.br/',
      prod: ''
    },
    icon: 'freeMarket'
  },
  {
    name: 'Assinatura Solar - APP (HMG)',
    group: 'app-assinatura-solar',
    links: {
      hmg: 'https://hmg-app.assinaturasolar.comerc.com.br',
      prod: 'https://app.assinaturasolar.comerc.com.br'
    },
    icon: 'solarSubscription'
  },
  {
    name: 'Hub de dados',
    group: 'hub-de-dados',
    links: {
      hmg: 'https://hubdedados.comerc.com.br',
      prod: 'https://hubdedados.comerc.com.br'
    },
    icon: 'dataHub'
  }
]
