import { mapColorsIconsEquipmentByName } from './mapColorsIconsEquipment.content'

describe('mapColorsIconsEquipmentByName', () => {
  it('should return the correct class for each equipment status', () => {
    expect(mapColorsIconsEquipmentByName['Conectado']).toBe('is-connected')
    expect(mapColorsIconsEquipmentByName['Desconectado']).toBe(
      'is-disconnected'
    )
    expect(mapColorsIconsEquipmentByName['Alarme']).toBe('is-alarm')
    expect(mapColorsIconsEquipmentByName['Em Observação']).toBe(
      'is-under_observation'
    )
    expect(mapColorsIconsEquipmentByName['default']).toBe('is-light')
    expect(mapColorsIconsEquipmentByName['Stand By']).toBe('is-stand_by')
  })

  it('should return undefined for unknown keys', () => {
    expect(mapColorsIconsEquipmentByName['Inexistente']).toBeUndefined()
  })
})
