import { IBreadcrumbLink } from '@/types/system/breadcrumb'

export const breadcrumbsContent: Record<string, IBreadcrumbLink[]> = {
  default: [
    { page: 'dashboard', href: '', active: true },
    { page: 'dashboard', href: '', active: true }
  ],
  accounts: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'accounts', href: '', active: true }
  ],
  'accounts.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'accounts', href: '/accounts' },
    { page: 'accounts.id', href: '', active: true }
  ],
  'agents-ccee': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'agents-ccee', href: '', active: true }
  ],
  alarms: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'alarms', href: '/alarms', active: true }
  ],
  'alarms.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'alarms', href: '/alarms' },
    { page: 'alarms.id', href: '', active: true }
  ],
  apportionments: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'apportionments', href: '', active: true }
  ],
  'apportionments.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'apportionments', href: '/apportionments' },
    { page: 'apportionments.id', href: '', active: true }
  ],
  companies: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'companies', href: '', active: true }
  ],
  'companies.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'companies', href: '/companies' },
    { page: 'companies.id', href: '', active: true }
  ],
  contracts: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'contracts', href: '', active: true }
  ],
  dashboard: [
    { page: 'dashboard', href: '' },
    { page: 'dashboard', href: '', active: true }
  ],
  devices: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'devices', href: '', active: true }
  ],
  'devices.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'devices', href: '/devices' },
    { page: 'devices.id', href: '', active: true }
  ],
  distributors: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'distributors', href: '', active: true }
  ],
  'distributors.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'distributors', href: '/distributors' },
    { page: 'distributors.id', href: '', active: true }
  ],
  'energetic-statement': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'energetic-statement', href: '', active: true }
  ],
  equipments: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'equipments', href: '', active: true }
  ],
  'equipments.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'equipments', href: '/equipments' },
    { page: 'equipments.id', href: '', active: true }
  ],
  'financial-statement': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'financial-statement', href: '', active: true }
  ],
  'installation-documents': [
    { page: 'dashboard', href: '/dashboard' },
    {
      page: 'installation-documents',
      href: '/installation-documents',
      active: true
    }
  ],
  'installation-documents.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'installation-documents', href: '/installation-documents' },
    { page: 'installation-documents.id', href: '', active: true }
  ],
  integrations: [
    { page: 'dashboard', href: '/dashboard' },
    {
      page: 'integrations',
      href: '/integrations',
      active: true
    }
  ],
  'integrations.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'integrations', href: '/integrations' },
    { page: 'integrations.id', href: '', active: true }
  ],
  monitoring: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'monitoring', href: '', active: true }
  ],
  'monitor-alarms': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'monitor-alarms', href: '', active: true }
  ],
  'physical-assets': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'physical-assets', href: '', active: true }
  ],
  'probes-map': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'probes-map', href: '', active: true }
  ],
  properties: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'properties', href: '', active: true }
  ],
  'properties.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'properties', href: '/properties' },
    { page: 'properties.id', href: '', active: true }
  ],
  reports: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'reports', href: '', active: true }
  ],
  'statistics.monitoring': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'statistics.productivity', href: '/statistics/productivity' },
    {
      page: 'statistics.monitoring',
      href: '',
      active: true
    }
  ],
  'statistics.productivity': [
    { page: 'dashboard', href: '/dashboard' },
    {
      page: 'statistics.productivity',
      href: '',
      active: true
    },
    { page: 'statistics.monitoring', href: '/statistics/monitoring' }
  ],
  'statistics.productivity.indicator.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'statistics', href: '' },
    { page: 'statistics.productivity', href: '/statistics/productivity' },
    { page: 'statistics.productivity.indicator.id', href: '', active: true }
  ],
  users: [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'users', href: '', active: true }
  ],
  'users.id': [
    { page: 'dashboard', href: '/dashboard' },
    { page: 'users', href: '/users' },
    { page: 'users.id', href: '', active: true }
  ]
}
