import { ThemeColorType } from '@/types/theme'

type IMapColorsTheme = Record<string, string>

export const mapColorsTheme: Record<ThemeColorType, IMapColorsTheme> = {
  default: {
    '--palette-50': '#ecfdf5',
    '--palette-100': '#d1fae5',
    '--palette-200': '#a7f3d0',
    '--palette-300': '#6ee7b7',
    '--palette-400': '#34d399',
    '--palette-500': '#10b981',
    '--palette-600': '#059669',
    '--palette-700': '#047857',
    '--palette-800': '#065f46',
    '--palette-900': '#064e3b',
    '--palette-950': '#022c22'
  },
  red: {
    '--palette-50': '#fef2f2',
    '--palette-100': '#fee2e2',
    '--palette-200': '#fecaca',
    '--palette-300': '#fca5a5',
    '--palette-400': '#f87171',
    '--palette-500': '#ef4444',
    '--palette-600': '#dc2626',
    '--palette-700': '#b91c1c',
    '--palette-800': '#991b1b',
    '--palette-900': '#7f1d1d',
    '--palette-950': '#450a0a'
  },
  blue: {
    '--palette-50': '#eff6ff',
    '--palette-100': '#dbeafe',
    '--palette-200': '#bfdbfe',
    '--palette-300': '#93c5fd',
    '--palette-400': '#60a5fa',
    '--palette-500': '#3b82f6',
    '--palette-600': '#2563eb',
    '--palette-700': '#1d4ed8',
    '--palette-800': '#1e40af',
    '--palette-900': '#1e3a8a',
    '--palette-950': '#172554'
  }
}
