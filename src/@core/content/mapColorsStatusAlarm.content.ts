export const mapAlarmStatusBySlug: Record<
  string,
  {
    badgeClassName: string
    label: string
    icon?: string
    iconClassName?: string
  }
> = {
  conectado: {
    badgeClassName: 'alarm-badge-status-is-connected',
    label: 'Conectado',
    icon: 'flash',
    iconClassName: 'alarm-icon-status-is-connected'
  },
  desconectado: {
    badgeClassName: 'alarm-badge-status-is-disconnected',
    label: 'Desconectado',
    icon: 'wifiOf',
    iconClassName: 'alarm-icon-status-is-disconnected'
  },
  em_observacao: {
    badgeClassName: 'alarm-badge-status-is-under_observation',
    label: 'Em Observação',
    icon: 'eye',
    iconClassName: 'alarm-icon-status-is-under_observation'
  },
  alarme: {
    badgeClassName: 'alarm-badge-status-is-alarm',
    label: 'Alarme',
    icon: 'bell01',
    iconClassName: 'alarm-icon-status-is-alarm'
  },
  desativado: {
    badgeClassName: 'alarm-badge-status-is-disabled',
    label: 'Desativado',
    icon: 'flashOf',
    iconClassName: 'alarm-icon-status-is-disabled'
  },
  em_espera: {
    badgeClassName: 'alarm-badge-status-is-stand_by',
    label: 'Em Espera',
    icon: 'hourglass',
    iconClassName: 'alarm-icon-status-is-stand_by'
  },
  default: {
    badgeClassName: 'alarm-status-is-disabled',
    label: 'Desativado'
  }
}
