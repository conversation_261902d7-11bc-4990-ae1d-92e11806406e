type Entries<T> = { [K in keyof T]: [K, T[K]] }[keyof T][]

export default function convertAlignmentToJSON<T extends object>(
  data: T
): object {
  const values = Object.entries(data) as Entries<Record<string, [string]>>

  let obj: object = {}

  values.forEach(([keys, [value]]) => {
    let item = value as unknown

    keys
      .split('.')
      .filter((key) => !!Number.isNaN(+key))
      .reverse()
      .forEach((key) => {
        item = { [String(key)]: item }
      })

    obj = Object.assign(obj, item)
  })

  return obj
}
