import convertAlignmentToJSON from './convertAlignmentToJSON'

describe('src/@core/utils/convertAlignmentToJSON', () => {
  it('should check return the function', () => {
    const message = 'campo obrigatório'

    const dataInput = {
      'entity_fields.entity': [message],
      'type_data.consumption.tariff_post': [message]
    }
    const dataOutput = {
      entity_fields: {
        entity: message
      },
      type_data: {
        consumption: {
          tariff_post: message
        }
      }
    }

    const result = convertAlignmentToJSON(dataInput)

    expect(result).toEqual(dataOutput)
  })
})
