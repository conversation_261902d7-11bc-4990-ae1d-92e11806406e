export const defineValuesFormFields = <F, T, D>(
  set: F extends Function ? (k: unknown, v: unknown) => void : Function,
  obj: T extends object ? {} : object,
  objDefault: D extends object ? Record<string, unknown> : any = {}
) => {
  Object.entries(obj).forEach(([key, value]) => {
    try {
      set(key, value ?? objDefault?.[key])
    } catch (error) {
      console.error('defineValuesFormFields', key, value, { error })
    }
  })
}
