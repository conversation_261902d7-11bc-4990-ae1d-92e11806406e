import { cleanup } from '@testing-library/react'

import {
  deviceConstantMock1,
  deviceConstantMock2
} from '@/__mock__/content/api-devices-constant.content'
import { FormDataInput, formDataOutput, parseDataInput } from './Tab.utils'

cleanup()

describe('@core/presentation/views/devices.id/TabConstants/Tab.utils', () => {
  describe('parseDataInput', () => {
    test('should parse device constant data with null lossFactor', () => {
      const result = parseDataInput(deviceConstantMock1)

      expect(result).toEqual({
        id: deviceConstantMock1.id,
        deviceId: deviceConstantMock1.deviceId,
        potentialRelation: deviceConstantMock1.potentialRelation,
        currentRelation: deviceConstantMock1.currentRelation,
        meterConstant: deviceConstantMock1.meterConstant,
        lossFactor: deviceConstantMock1.lossFactor,
        initialDate: deviceConstantMock1.initialDate
      })
    })

    test('should parse device constant data with numeric lossFactor', () => {
      const result = parseDataInput(deviceConstantMock2)

      expect(result).toEqual({
        id: deviceConstantMock2.id,
        deviceId: deviceConstantMock2.deviceId,
        potentialRelation: deviceConstantMock2.potentialRelation,
        currentRelation: deviceConstantMock2.currentRelation,
        meterConstant: deviceConstantMock2.meterConstant,
        lossFactor: deviceConstantMock2.lossFactor,
        initialDate: deviceConstantMock2.initialDate
      })
    })

    test('should handle undefined lossFactor as null', () => {
      const mockData = {
        ...deviceConstantMock1,
        lossFactor: undefined
      }

      const result = parseDataInput(mockData as any)
      expect(result.lossFactor).toBe(null)
    })
  })

  describe('FormDataInput', () => {
    test('should convert device page data to form data with null lossFactor', () => {
      const devicePageData = parseDataInput(deviceConstantMock1)
      const result = FormDataInput(devicePageData)

      expect(result).toEqual({
        id: deviceConstantMock1.id,
        deviceId: deviceConstantMock1.deviceId,
        potentialRelation: deviceConstantMock1.potentialRelation,
        currentRelation: deviceConstantMock1.currentRelation,
        meterConstant: deviceConstantMock1.meterConstant,
        lossFactor: '',
        initialDate: deviceConstantMock1.initialDate
      })
    })

    test('should convert device page data to form data with numeric lossFactor', () => {
      const devicePageData = parseDataInput(deviceConstantMock2)
      const result = FormDataInput(devicePageData)

      expect(result).toEqual({
        id: deviceConstantMock2.id,
        deviceId: deviceConstantMock2.deviceId,
        potentialRelation: deviceConstantMock2.potentialRelation,
        currentRelation: deviceConstantMock2.currentRelation,
        meterConstant: deviceConstantMock2.meterConstant,
        lossFactor: '3',
        initialDate: deviceConstantMock2.initialDate
      })
    })
  })

  describe('formDataOutput', () => {
    test('should convert form data to send format with empty lossFactor', () => {
      const devicePageData = parseDataInput(deviceConstantMock1)
      const formData = FormDataInput(devicePageData)

      const result = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      expect(result).toEqual({
        id: deviceConstantMock1.id,
        deviceId: deviceConstantMock1.deviceId,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: deviceConstantMock1.lossFactor,
        initialDate: deviceConstantMock1.initialDate
      })
    })

    test('should convert form data to send format with numeric lossFactor', () => {
      const devicePageData = parseDataInput(deviceConstantMock2)
      const formData = FormDataInput(devicePageData)

      const result = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      expect(result).toEqual({
        id: deviceConstantMock2.id,
        deviceId: deviceConstantMock2.deviceId,
        potentialRelation: deviceConstantMock2.potentialRelation,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: deviceConstantMock2.lossFactor,
        initialDate: deviceConstantMock2.initialDate
      })
    })

    test('should handle string lossFactor conversion correctly', () => {
      const testData = {
        id: 1,
        deviceId: 497,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: '2.5',
        initialDate: '2023-12-14 10:42'
      }

      const result = formDataOutput(testData)

      expect(result).toEqual({
        id: 1,
        deviceId: 497,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: 2.5,
        initialDate: '2023-12-14 10:42:00'
      })
    })

    test('should handle empty string lossFactor as null', () => {
      const testData = {
        id: 1,
        deviceId: 497,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: '',
        initialDate: '2023-12-14 10:42'
      }

      const result = formDataOutput(testData)

      expect(result).toEqual({
        id: 1,
        deviceId: 497,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: null,
        initialDate: '2023-12-14 10:42:00'
      })
    })

    test('should handle whitespace-only lossFactor as 0', () => {
      const testData = {
        id: 1,
        deviceId: 497,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: '   ',
        initialDate: '2023-12-14 10:42'
      }

      const result = formDataOutput(testData)

      expect(result).toEqual({
        id: 1,
        deviceId: 497,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: 0,
        initialDate: '2023-12-14 10:42:00'
      })
    })

    test('should handle invalid string lossFactor as NaN', () => {
      const testData = {
        id: 1,
        deviceId: 497,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: 'invalid',
        initialDate: '2023-12-14 10:42'
      }

      const result = formDataOutput(testData)

      expect(result).toEqual({
        id: 1,
        deviceId: 497,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: NaN,
        initialDate: '2023-12-14 10:42:00'
      })
    })
  })
})
