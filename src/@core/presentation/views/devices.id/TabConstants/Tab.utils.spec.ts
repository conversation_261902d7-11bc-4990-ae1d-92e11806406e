import { cleanup } from '@testing-library/react'

import {
  deviceConstantMock1,
  deviceConstantMock2
} from '@/__mock__/content/api-devices-constant.content'
import { FormDataInput, formDataOutput, parseDataInput } from './Tab.utils'

cleanup()

describe('@core/presentation/views/devices.id/TabConstants/Tab.utils', () => {
  describe('parseDataInput', () => {
    describe('when using deviceConstantMock1 (lossFactor: null)', () => {
      it('should parse all fields correctly', () => {
        const result = parseDataInput(deviceConstantMock1)

        expect(result).toEqual({
          id: 329,
          deviceId: 497,
          potentialRelation: 1,
          currentRelation: 10,
          meterConstant: 2,
          lossFactor: null,
          initialDate: '2023-12-14 10:42:05'
        })
      })

      it('should maintain null lossFactor', () => {
        const result = parseDataInput(deviceConstantMock1)
        expect(result.lossFactor).toBeNull()
      })

      it('should preserve all other properties unchanged', () => {
        const result = parseDataInput(deviceConstantMock1)

        expect(result.id).toBe(deviceConstantMock1.id)
        expect(result.deviceId).toBe(deviceConstantMock1.deviceId)
        expect(result.potentialRelation).toBe(deviceConstantMock1.potentialRelation)
        expect(result.currentRelation).toBe(deviceConstantMock1.currentRelation)
        expect(result.meterConstant).toBe(deviceConstantMock1.meterConstant)
        expect(result.initialDate).toBe(deviceConstantMock1.initialDate)
      })
    })

    describe('when using deviceConstantMock2 (lossFactor: 3)', () => {
      it('should parse all fields correctly', () => {
        const result = parseDataInput(deviceConstantMock2)

        expect(result).toEqual({
          id: 330,
          deviceId: 497,
          potentialRelation: 1,
          currentRelation: 10,
          meterConstant: 2,
          lossFactor: 3,
          initialDate: '2023-12-14 10:45:05'
        })
      })

      it('should preserve numeric lossFactor', () => {
        const result = parseDataInput(deviceConstantMock2)
        expect(result.lossFactor).toBe(3)
        expect(typeof result.lossFactor).toBe('number')
      })
    })

    describe('edge cases', () => {
      it('should convert undefined lossFactor to null', () => {
        const mockWithUndefined = {
          ...deviceConstantMock1,
          lossFactor: undefined
        }

        const result = parseDataInput(mockWithUndefined as any)
        expect(result.lossFactor).toBeNull()
      })
    })
  })

  describe('FormDataInput', () => {
    describe('when using deviceConstantMock1 (lossFactor: null)', () => {
      it('should convert null lossFactor to empty string', () => {
        const pageData = parseDataInput(deviceConstantMock1)
        const result = FormDataInput(pageData)

        expect(result).toEqual({
          id: 329,
          deviceId: 497,
          potentialRelation: 1,
          currentRelation: 10,
          meterConstant: 2,
          lossFactor: '',
          initialDate: '2023-12-14 10:42:05'
        })
      })

      it('should convert lossFactor to empty string specifically', () => {
        const pageData = parseDataInput(deviceConstantMock1)
        const result = FormDataInput(pageData)

        expect(result.lossFactor).toBe('')
        expect(typeof result.lossFactor).toBe('string')
      })

      it('should preserve all other fields as numbers', () => {
        const pageData = parseDataInput(deviceConstantMock1)
        const result = FormDataInput(pageData)

        expect(typeof result.id).toBe('number')
        expect(typeof result.deviceId).toBe('number')
        expect(typeof result.potentialRelation).toBe('number')
        expect(typeof result.currentRelation).toBe('number')
        expect(typeof result.meterConstant).toBe('number')
      })
    })

    describe('when using deviceConstantMock2 (lossFactor: 3)', () => {
      it('should convert numeric lossFactor to string', () => {
        const pageData = parseDataInput(deviceConstantMock2)
        const result = FormDataInput(pageData)

        expect(result).toEqual({
          id: 330,
          deviceId: 497,
          potentialRelation: 1,
          currentRelation: 10,
          meterConstant: 2,
          lossFactor: '3',
          initialDate: '2023-12-14 10:45:05'
        })
      })

      it('should convert lossFactor number to string correctly', () => {
        const pageData = parseDataInput(deviceConstantMock2)
        const result = FormDataInput(pageData)

        expect(result.lossFactor).toBe('3')
        expect(typeof result.lossFactor).toBe('string')
      })
    })
  })

  describe('formDataOutput', () => {
    describe('when using deviceConstantMock1 data (lossFactor: null → empty string)', () => {
      it('should convert form data with empty lossFactor to send format', () => {
        const pageData = parseDataInput(deviceConstantMock1)
        const formData = FormDataInput(pageData)

        // Convert to the format expected by formDataOutput
        const formDataForOutput = {
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        }

        const result = formDataOutput(formDataForOutput)

        expect(result).toEqual({
          id: 329,
          deviceId: 497,
          potentialRelation: 1,
          currentRelation: 10,
          meterConstant: 2,
          lossFactor: null,
          initialDate: '2023-12-14 10:42:05:00'
        })
      })

      it('should convert empty string lossFactor to null', () => {
        const pageData = parseDataInput(deviceConstantMock1)
        const formData = FormDataInput(pageData)

        const formDataForOutput = {
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        }

        const result = formDataOutput(formDataForOutput)
        expect(result.lossFactor).toBeNull()
      })

      it('should add :00 to initialDate', () => {
        const pageData = parseDataInput(deviceConstantMock1)
        const formData = FormDataInput(pageData)

        const formDataForOutput = {
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        }

        const result = formDataOutput(formDataForOutput)
        expect(result.initialDate).toBe('2023-12-14 10:42:05:00')
      })
    })

    describe('when using deviceConstantMock2 data (lossFactor: 3 → "3")', () => {
      it('should convert form data with numeric lossFactor to send format', () => {
        const pageData = parseDataInput(deviceConstantMock2)
        const formData = FormDataInput(pageData)

        const formDataForOutput = {
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        }

        const result = formDataOutput(formDataForOutput)

        expect(result).toEqual({
          id: 330,
          deviceId: 497,
          potentialRelation: 1,
          currentRelation: 10,
          meterConstant: 2,
          lossFactor: 3,
          initialDate: '2023-12-14 10:45:05:00'
        })
      })

      it('should convert string lossFactor back to number', () => {
        const pageData = parseDataInput(deviceConstantMock2)
        const formData = FormDataInput(pageData)

        const formDataForOutput = {
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        }

        const result = formDataOutput(formDataForOutput)
        expect(result.lossFactor).toBe(3)
        expect(typeof result.lossFactor).toBe('number')
      })

      it('should convert all string numbers to actual numbers', () => {
        const pageData = parseDataInput(deviceConstantMock2)
        const formData = FormDataInput(pageData)

        const formDataForOutput = {
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        }

        const result = formDataOutput(formDataForOutput)

        expect(typeof result.potentialRelation).toBe('number')
        expect(typeof result.currentRelation).toBe('number')
        expect(typeof result.meterConstant).toBe('number')
        expect(result.potentialRelation).toBe(1)
        expect(result.currentRelation).toBe(10)
        expect(result.meterConstant).toBe(2)
      })
    })

    describe('edge cases with custom data', () => {
      it('should handle empty string lossFactor as null', () => {
        const formData = {
          id: 100,
          deviceId: 200,
          potentialRelation: '5',
          currentRelation: '15',
          meterConstant: '3',
          lossFactor: '',
          initialDate: '2024-01-15 14:30'
        }

        const result = formDataOutput(formData)
        expect(result.lossFactor).toBeNull()
      })

      it('should handle whitespace-only lossFactor as 0', () => {
        const formData = {
          id: 100,
          deviceId: 200,
          potentialRelation: '5',
          currentRelation: '15',
          meterConstant: '3',
          lossFactor: '   ',
          initialDate: '2024-01-15 14:30'
        }

        const result = formDataOutput(formData)
        expect(result.lossFactor).toBe(0)
      })

      it('should handle invalid string lossFactor as NaN', () => {
        const formData = {
          id: 100,
          deviceId: 200,
          potentialRelation: '5',
          currentRelation: '15',
          meterConstant: '3',
          lossFactor: 'invalid',
          initialDate: '2024-01-15 14:30'
        }

        const result = formDataOutput(formData)
        expect(result.lossFactor).toBeNaN()
      })

      it('should handle zero values correctly', () => {
        const formData = {
          id: 0,
          deviceId: 200,
          potentialRelation: '0',
          currentRelation: '0',
          meterConstant: '0',
          lossFactor: '0',
          initialDate: '2024-01-15 14:30'
        }

        const result = formDataOutput(formData)

        expect(result.id).toBe(0)
        expect(result.potentialRelation).toBe(0)
        expect(result.currentRelation).toBe(0)
        expect(result.meterConstant).toBe(0)
        expect(result.lossFactor).toBe(0)
      })

      it('should handle null id correctly', () => {
        const formData = {
          id: null,
          deviceId: 200,
          potentialRelation: '5',
          currentRelation: '15',
          meterConstant: '3',
          lossFactor: '2.5',
          initialDate: '2024-01-15 14:30'
        }

        const result = formDataOutput(formData)
        expect(result.id).toBeNull()
      })
    })
  })

  describe('Integration Tests - Complete Data Pipeline', () => {
    describe('using deviceConstantMock1 (null lossFactor)', () => {
      it('should transform data through complete pipeline correctly', () => {
        // Step 1: Parse input data
        const pageData = parseDataInput(deviceConstantMock1)

        // Step 2: Convert to form data
        const formData = FormDataInput(pageData)

        // Step 3: Convert to output format
        const outputData = formDataOutput({
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        })

        // Verify final result
        expect(outputData).toEqual({
          id: 329,
          deviceId: 497,
          potentialRelation: 1,
          currentRelation: 10,
          meterConstant: 2,
          lossFactor: null,
          initialDate: '2023-12-14 10:42:05:00'
        })
      })

      it('should verify lossFactor transformation: null → "" → null', () => {
        const pageData = parseDataInput(deviceConstantMock1)
        const formData = FormDataInput(pageData)
        const outputData = formDataOutput({
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        })

        expect(deviceConstantMock1.lossFactor).toBeNull()
        expect(pageData.lossFactor).toBeNull()
        expect(formData.lossFactor).toBe('')
        expect(outputData.lossFactor).toBeNull()
      })
    })

    describe('using deviceConstantMock2 (numeric lossFactor)', () => {
      it('should transform data through complete pipeline correctly', () => {
        const pageData = parseDataInput(deviceConstantMock2)
        const formData = FormDataInput(pageData)
        const outputData = formDataOutput({
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        })

        expect(outputData).toEqual({
          id: 330,
          deviceId: 497,
          potentialRelation: 1,
          currentRelation: 10,
          meterConstant: 2,
          lossFactor: 3,
          initialDate: '2023-12-14 10:45:05:00'
        })
      })

      it('should verify lossFactor transformation: 3 → 3 → "3" → 3', () => {
        const pageData = parseDataInput(deviceConstantMock2)
        const formData = FormDataInput(pageData)
        const outputData = formDataOutput({
          ...formData,
          potentialRelation: String(formData.potentialRelation),
          currentRelation: String(formData.currentRelation),
          meterConstant: String(formData.meterConstant)
        })

        expect(deviceConstantMock2.lossFactor).toBe(3)
        expect(pageData.lossFactor).toBe(3)
        expect(formData.lossFactor).toBe('3')
        expect(outputData.lossFactor).toBe(3)
      })
    })

    describe('comparing both mock scenarios', () => {
      it('should handle both null and numeric lossFactor scenarios correctly', () => {
        // Process both mocks
        const result1 = formDataOutput({
          ...FormDataInput(parseDataInput(deviceConstantMock1)),
          potentialRelation: '1',
          currentRelation: '10',
          meterConstant: '2'
        })

        const result2 = formDataOutput({
          ...FormDataInput(parseDataInput(deviceConstantMock2)),
          potentialRelation: '1',
          currentRelation: '10',
          meterConstant: '2'
        })

        // Both should have same structure but different lossFactor
        expect(result1.lossFactor).toBeNull()
        expect(result2.lossFactor).toBe(3)

        // Other fields should be identical
        expect(result1.potentialRelation).toBe(result2.potentialRelation)
        expect(result1.currentRelation).toBe(result2.currentRelation)
        expect(result1.meterConstant).toBe(result2.meterConstant)
        expect(result1.deviceId).toBe(result2.deviceId)
      })
    })
  })
})
