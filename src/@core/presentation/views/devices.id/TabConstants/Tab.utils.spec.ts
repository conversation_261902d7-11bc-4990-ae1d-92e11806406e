import { cleanup } from '@testing-library/react'

import {
  deviceConstantMock1,
  deviceConstantMock2
} from '@/__mock__/content/api-devices-constant.content'
import { FormDataInput, formDataOutput, parseDataInput } from './Tab.utils'

cleanup()

describe('@core/presentation/views/devices.id/TabConstants/Tab.utils', () => {
  // Mock data for testing
  const mockDeviceConstant = {
    id: 100,
    deviceId: 200,
    potentialRelation: 5,
    currentRelation: 15,
    meterConstant: 3,
    lossFactor: 2.5,
    initialDate: '2024-01-15 14:30:00'
  }

  const mockDeviceConstantWithNullLoss = {
    ...mockDeviceConstant,
    lossFactor: null
  }

  describe('parseDataInput', () => {
    it('should correctly parse device constant with all fields', () => {
      const result = parseDataInput(mockDeviceConstant)

      expect(result).toEqual({
        id: 100,
        deviceId: 200,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: 2.5,
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should handle null lossFactor correctly', () => {
      const result = parseDataInput(mockDeviceConstantWithNullLoss)

      expect(result).toEqual({
        id: 100,
        deviceId: 200,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: null,
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should convert undefined lossFactor to null', () => {
      const mockWithUndefined = {
        ...mockDeviceConstant,
        lossFactor: undefined
      }

      const result = parseDataInput(mockWithUndefined as any)
      expect(result.lossFactor).toBe(null)
    })

    it('should work with real mock data - deviceConstantMock1', () => {
      const result = parseDataInput(deviceConstantMock1)

      expect(result).toEqual({
        id: deviceConstantMock1.id,
        deviceId: deviceConstantMock1.deviceId,
        potentialRelation: deviceConstantMock1.potentialRelation,
        currentRelation: deviceConstantMock1.currentRelation,
        meterConstant: deviceConstantMock1.meterConstant,
        lossFactor: deviceConstantMock1.lossFactor,
        initialDate: deviceConstantMock1.initialDate
      })
    })

    it('should work with real mock data - deviceConstantMock2', () => {
      const result = parseDataInput(deviceConstantMock2)

      expect(result).toEqual({
        id: deviceConstantMock2.id,
        deviceId: deviceConstantMock2.deviceId,
        potentialRelation: deviceConstantMock2.potentialRelation,
        currentRelation: deviceConstantMock2.currentRelation,
        meterConstant: deviceConstantMock2.meterConstant,
        lossFactor: deviceConstantMock2.lossFactor,
        initialDate: deviceConstantMock2.initialDate
      })
    })
  })

  describe('FormDataInput', () => {
    it('should convert page data with numeric lossFactor to form data', () => {
      const pageData = parseDataInput(mockDeviceConstant)
      const result = FormDataInput(pageData)

      expect(result).toEqual({
        id: 100,
        deviceId: 200,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: '2.5',
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should convert null lossFactor to empty string', () => {
      const pageData = parseDataInput(mockDeviceConstantWithNullLoss)
      const result = FormDataInput(pageData)

      expect(result).toEqual({
        id: 100,
        deviceId: 200,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: '',
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should work with real mock data - null lossFactor', () => {
      const devicePageData = parseDataInput(deviceConstantMock1)
      const result = FormDataInput(devicePageData)

      expect(result).toEqual({
        id: deviceConstantMock1.id,
        deviceId: deviceConstantMock1.deviceId,
        potentialRelation: deviceConstantMock1.potentialRelation,
        currentRelation: deviceConstantMock1.currentRelation,
        meterConstant: deviceConstantMock1.meterConstant,
        lossFactor: '',
        initialDate: deviceConstantMock1.initialDate
      })
    })

    it('should work with real mock data - numeric lossFactor', () => {
      const devicePageData = parseDataInput(deviceConstantMock2)
      const result = FormDataInput(devicePageData)

      expect(result).toEqual({
        id: deviceConstantMock2.id,
        deviceId: deviceConstantMock2.deviceId,
        potentialRelation: deviceConstantMock2.potentialRelation,
        currentRelation: deviceConstantMock2.currentRelation,
        meterConstant: deviceConstantMock2.meterConstant,
        lossFactor: '3',
        initialDate: deviceConstantMock2.initialDate
      })
    })
  })

  describe('formDataOutput', () => {
    it('should convert form data to send format with numeric values', () => {
      const formData = {
        id: 100,
        deviceId: 200,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: '2.5',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)

      expect(result).toEqual({
        id: 100,
        deviceId: 200,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: 2.5,
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should convert empty lossFactor to null', () => {
      const formData = {
        id: 100,
        deviceId: 200,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: '',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)

      expect(result).toEqual({
        id: 100,
        deviceId: 200,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: null,
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should handle zero values correctly', () => {
      const formData = {
        id: 0,
        deviceId: 200,
        potentialRelation: '0',
        currentRelation: '0',
        meterConstant: '0',
        lossFactor: '0',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)

      expect(result).toEqual({
        id: 0,
        deviceId: 200,
        potentialRelation: 0,
        currentRelation: 0,
        meterConstant: 0,
        lossFactor: 0,
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should handle negative values correctly', () => {
      const formData = {
        id: 100,
        deviceId: 200,
        potentialRelation: '-5',
        currentRelation: '-15',
        meterConstant: '-3',
        lossFactor: '-2.5',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)

      expect(result).toEqual({
        id: 100,
        deviceId: 200,
        potentialRelation: -5,
        currentRelation: -15,
        meterConstant: -3,
        lossFactor: -2.5,
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should handle null id correctly', () => {
      const formData = {
        id: null,
        deviceId: 200,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: '2.5',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)

      expect(result).toEqual({
        id: null,
        deviceId: 200,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: 2.5,
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should handle whitespace-only lossFactor as 0', () => {
      const formData = {
        id: 100,
        deviceId: 200,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: '   ',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)

      expect(result).toEqual({
        id: 100,
        deviceId: 200,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: 0,
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should handle invalid string lossFactor as NaN', () => {
      const formData = {
        id: 100,
        deviceId: 200,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: 'invalid',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)

      expect(result).toEqual({
        id: 100,
        deviceId: 200,
        potentialRelation: 5,
        currentRelation: 15,
        meterConstant: 3,
        lossFactor: NaN,
        initialDate: '2024-01-15 14:30:00'
      })
    })

    it('should work with real mock data - empty lossFactor', () => {
      const devicePageData = parseDataInput(deviceConstantMock1)
      const formData = FormDataInput(devicePageData)

      const result = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      expect(result).toEqual({
        id: deviceConstantMock1.id,
        deviceId: deviceConstantMock1.deviceId,
        potentialRelation: deviceConstantMock1.potentialRelation,
        currentRelation: deviceConstantMock1.currentRelation,
        meterConstant: deviceConstantMock1.meterConstant,
        lossFactor: null,
        initialDate: deviceConstantMock1.initialDate + ':00'
      })
    })

    it('should work with real mock data - numeric lossFactor', () => {
      const devicePageData = parseDataInput(deviceConstantMock2)
      const formData = FormDataInput(devicePageData)

      const result = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      expect(result).toEqual({
        id: deviceConstantMock2.id,
        deviceId: deviceConstantMock2.deviceId,
        potentialRelation: deviceConstantMock2.potentialRelation,
        currentRelation: deviceConstantMock2.currentRelation,
        meterConstant: deviceConstantMock2.meterConstant,
        lossFactor: deviceConstantMock2.lossFactor,
        initialDate: deviceConstantMock2.initialDate + ':00'
      })
    })
  })

  describe('Integration Tests - Complete Pipeline', () => {
    it('should handle complete data transformation pipeline with null lossFactor', () => {
      // Start with raw device constant data
      const rawData = deviceConstantMock1

      // Transform through the pipeline
      const pageData = parseDataInput(rawData)
      const formData = FormDataInput(pageData)
      const outputData = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      // Verify final output
      expect(outputData).toEqual({
        id: 329,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: null,
        initialDate: '2023-12-14 10:42:05:00'
      })
    })

    it('should handle complete data transformation pipeline with numeric lossFactor', () => {
      // Start with raw device constant data
      const rawData = deviceConstantMock2

      // Transform through the pipeline
      const pageData = parseDataInput(rawData)
      const formData = FormDataInput(pageData)
      const outputData = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      // Verify final output
      expect(outputData).toEqual({
        id: 330,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: 3,
        initialDate: '2023-12-14 10:45:05:00'
      })
    })

    it('should handle complete pipeline with custom data', () => {
      // Start with custom mock data
      const customData = {
        id: 999,
        deviceId: 888,
        potentialRelation: 7,
        currentRelation: 21,
        meterConstant: 4,
        lossFactor: 1.8,
        initialDate: '2024-02-20 16:45:30'
      }

      // Transform through the pipeline
      const pageData = parseDataInput(customData)
      const formData = FormDataInput(pageData)
      const outputData = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      // Verify each step
      expect(pageData.lossFactor).toBe(1.8)
      expect(formData.lossFactor).toBe('1.8')
      expect(outputData).toEqual({
        id: 999,
        deviceId: 888,
        potentialRelation: 7,
        currentRelation: 21,
        meterConstant: 4,
        lossFactor: 1.8,
        initialDate: '2024-02-20 16:45:30:00'
      })
    })
  })
})
