import { cleanup } from '@testing-library/react'

import {
  deviceConstantMock1,
  deviceConstantMock2
} from '@/__mock__/content/api-devices-constant.content'
import { FormDataInput, formDataOutput, parseDataInput } from './Tab.utils'

cleanup()

describe('Tab.utils', () => {
  describe('parseDataInput', () => {
    test('should parse deviceConstantMock1 with null lossFactor', () => {
      const result = parseDataInput(deviceConstantMock1)

      expect(result).toEqual({
        id: 329,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: null,
        initialDate: '2023-12-14 10:42:05'
      })
    })

    test('should parse deviceConstantMock2 with numeric lossFactor', () => {
      const result = parseDataInput(deviceConstantMock2)

      expect(result).toEqual({
        id: 330,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: 3,
        initialDate: '2023-12-14 10:45:05'
      })
    })

    test('should handle undefined lossFactor as null', () => {
      const mockData = { ...deviceConstantMock1, lossFactor: undefined }
      const result = parseDataInput(mockData as any)

      expect(result.lossFactor).toBeNull()
    })
  })

  describe('FormDataInput', () => {
    test('should convert deviceConstantMock1 to form data with empty lossFactor', () => {
      const pageData = parseDataInput(deviceConstantMock1)
      const result = FormDataInput(pageData)

      expect(result).toEqual({
        id: 329,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: '',
        initialDate: '2023-12-14 10:42:05'
      })
    })

    test('should convert deviceConstantMock2 to form data with string lossFactor', () => {
      const pageData = parseDataInput(deviceConstantMock2)
      const result = FormDataInput(pageData)

      expect(result).toEqual({
        id: 330,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: '3',
        initialDate: '2023-12-14 10:45:05'
      })
    })
  })

  describe('formDataOutput', () => {
    test('should convert form data from deviceConstantMock1 to send format', () => {
      const pageData = parseDataInput(deviceConstantMock1)
      const formData = FormDataInput(pageData)

      const result = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      expect(result).toEqual({
        id: 329,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: null,
        initialDate: '2023-12-14 10:42:05:00'
      })
    })

    test('should convert form data from deviceConstantMock2 to send format', () => {
      const pageData = parseDataInput(deviceConstantMock2)
      const formData = FormDataInput(pageData)

      const result = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      expect(result).toEqual({
        id: 330,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: 3,
        initialDate: '2023-12-14 10:45:05:00'
      })
    })

    test('should handle empty string lossFactor as null', () => {
      const formData = {
        id: 100,
        deviceId: 200,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: '',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)
      expect(result.lossFactor).toBeNull()
    })

    test('should handle whitespace lossFactor as 0', () => {
      const formData = {
        id: 100,
        deviceId: 200,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: '   ',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)
      expect(result.lossFactor).toBe(0)
    })

    test('should handle invalid string lossFactor as NaN', () => {
      const formData = {
        id: 100,
        deviceId: 200,
        potentialRelation: '5',
        currentRelation: '15',
        meterConstant: '3',
        lossFactor: 'invalid',
        initialDate: '2024-01-15 14:30'
      }

      const result = formDataOutput(formData)
      expect(result.lossFactor).toBeNaN()
    })
  })

  describe('Integration Tests', () => {
    test('should handle complete pipeline with deviceConstantMock1', () => {
      const pageData = parseDataInput(deviceConstantMock1)
      const formData = FormDataInput(pageData)
      const outputData = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      expect(outputData).toEqual({
        id: 329,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: null,
        initialDate: '2023-12-14 10:42:05:00'
      })
    })

    test('should handle complete pipeline with deviceConstantMock2', () => {
      const pageData = parseDataInput(deviceConstantMock2)
      const formData = FormDataInput(pageData)
      const outputData = formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })

      expect(outputData).toEqual({
        id: 330,
        deviceId: 497,
        potentialRelation: 1,
        currentRelation: 10,
        meterConstant: 2,
        lossFactor: 3,
        initialDate: '2023-12-14 10:45:05:00'
      })
    })

    test('should verify lossFactor transformations', () => {
      // Mock1: null → null → "" → null
      const pageData1 = parseDataInput(deviceConstantMock1)
      const formData1 = FormDataInput(pageData1)
      const outputData1 = formDataOutput({
        ...formData1,
        potentialRelation: '1',
        currentRelation: '10',
        meterConstant: '2'
      })

      expect(deviceConstantMock1.lossFactor).toBeNull()
      expect(pageData1.lossFactor).toBeNull()
      expect(formData1.lossFactor).toBe('')
      expect(outputData1.lossFactor).toBeNull()

      // Mock2: 3 → 3 → "3" → 3
      const pageData2 = parseDataInput(deviceConstantMock2)
      const formData2 = FormDataInput(pageData2)
      const outputData2 = formDataOutput({
        ...formData2,
        potentialRelation: '1',
        currentRelation: '10',
        meterConstant: '2'
      })

      expect(deviceConstantMock2.lossFactor).toBe(3)
      expect(pageData2.lossFactor).toBe(3)
      expect(formData2.lossFactor).toBe('3')
      expect(outputData2.lossFactor).toBe(3)
    })
  })
})