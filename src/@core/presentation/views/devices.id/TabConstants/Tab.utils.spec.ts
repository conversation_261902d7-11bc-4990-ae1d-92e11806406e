import { cleanup } from '@testing-library/react'

import {
  deviceConstantMock1,
  deviceConstantMock2
} from '@/__mock__/content/api-devices-constant.content'
import { FormDataInput, formDataOutput, parseDataInput } from './Tab.utils'

cleanup()

describe('@core/presentation/views/devices.id/TabConstants/Tab.utils', () => {
  test('should check return the utility function parseDataInput', () => {
    expect(parseDataInput(deviceConstantMock1)).toEqual({
      id: deviceConstantMock1.id,
      deviceId: deviceConstantMock1.deviceId,
      potentialRelation: deviceConstantMock1.potentialRelation,
      currentRelation: deviceConstantMock1.currentRelation,
      meterConstant: deviceConstantMock1.meterConstant,
      lossFactor: deviceConstantMock1.lossFactor,
      initialDate: deviceConstantMock1.initialDate
    })
  })

  test('should check return the utility function formDataOutput', () => {
    const devicePageData1 = parseDataInput(deviceConstantMock1)

    expect(FormDataInput(devicePageData1)).toEqual({
      id: devicePageData1.id,
      deviceId: devicePageData1.deviceId,
      potentialRelation: devicePageData1.potentialRelation,
      currentRelation: devicePageData1.currentRelation,
      meterConstant: devicePageData1.meterConstant,
      lossFactor: '',
      initialDate: devicePageData1.initialDate
    })

    const devicePageData2 = parseDataInput(deviceConstantMock2)
    expect(FormDataInput(devicePageData2)).toEqual({
      id: devicePageData2.id,
      deviceId: devicePageData2.deviceId,
      potentialRelation: devicePageData2.potentialRelation,
      currentRelation: devicePageData2.currentRelation,
      meterConstant: devicePageData2.meterConstant,
      lossFactor: String(devicePageData2.lossFactor),
      initialDate: devicePageData2.initialDate
    })
  })

  test('should check return the utility function formDataOutput', () => {
    const devicePageData = parseDataInput(deviceConstantMock1)
    const formData = FormDataInput(devicePageData)
    expect(
      formDataOutput({
        ...formData,
        potentialRelation: String(formData.potentialRelation),
        currentRelation: String(formData.currentRelation),
        meterConstant: String(formData.meterConstant)
      })
    ).toEqual({
      id: formData.id,
      deviceId: formData.deviceId,
      potentialRelation: Number(formData.potentialRelation),
      currentRelation: Number(formData.currentRelation),
      meterConstant: Number(formData.meterConstant),
      lossFactor: Number(formData.lossFactor),
      initialDate: formData.initialDate + ':00'
    })
  })
})
