import { useRouter } from 'next/router'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { costCentersApiV3 } from '@/@core/infra/api/CostCentersApiV3'
import { http } from '@/@core/infra/http'
import { pageApportionmentsIdCookie } from '@/@core/infra/memory/cookie/PageApportionmentsIdCookie'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'

import { useStatePage } from '../page.hooks'

export const useMethodSection = () => {
  const router = useRouter()
  const log = useLog()

  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()

  const statePage = useStatePage()

  const languageSection = useLanguageSection()

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const search = pageApportionmentsIdCookie.get().sectionCostCenters

      const { data } = await costCentersApiV3(http).get({
        ...search,
        apportionmentId: Number(router.query.id)
      })

      statePage.set({
        costCenters: {
          items: data.items,
          lastPage: data.lastPage,
          total: data.total
        }
      })
    } catch (error) {
      systemToast.addToast({
        message: languageSection.errors.request,
        type: 'error'
      })
      log.send(loggerRequest, {
        error,
        title: 'apportionments.id/SectionCostCenter/useMethodSection/fetchData'
      })
    } finally {
      systemLoading.setLoading(false)
    }
  }

  const downloadCostCenter = () => {}

  return { fetchData, downloadCostCenter }
}

export const useLanguageSection = () => {
  const { lang } = useSystemLanguageStore().state
  const { pages, modalDelete, errors } = languageByMode(lang)

  const { sectionCostCenter } = pages.apportionmentsId

  return {
    ...sectionCostCenter,
    modalDelete,
    errors
  }
}
