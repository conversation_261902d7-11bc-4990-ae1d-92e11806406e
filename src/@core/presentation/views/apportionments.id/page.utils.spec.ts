import { apportionmentMock1 } from '@/__mock__/content/api-apportionment.content'
import { apportionmentPeriodMock1 } from '@/__mock__/content/api-apportionments-periods.content'
import { apportionmentResultMock1 } from '@/__mock__/content/api-apportionments-results.content'
import {
  costCenterResultMock1,
  costCenterResultMock2
} from '@/__mock__/content/api-cost-centers-results'
import {
  parseApportionmentData,
  parseApportionmentPeriodData,
  parseApportionmentResultData,
  parseCostCenterResultData,
  renderTableResultHistoryData
} from './page.utils'

describe('src/@core/presentation/views/apportionments.id/page.utils', () => {
  it('should check return the util function parseApportionmentData', () => {
    const dataParsed = parseApportionmentData(apportionmentMock1)

    expect(dataParsed).toEqual({
      id: 120,
      name: 'rateio exemplo',
      description: 'exemplo',
      company: {
        id: 137,
        name: '137 - Comany without account'
      },
      apportionmentType: {
        id: 3,
        name: '<PERSON><PERSON>'
      },
      apportionmentMeasureUnit: {
        id: 4,
        name: 'Nm³'
      },
      apportionmentTariffType: {
        id: 3,
        name: 'Customizado'
      },
      costCenterIds: [479, 483, 484, 485, 486],
      lastResult: null,
      mixedConsumption: true
    })
  })

  it('should check return the util function parseApportionmentResultData', () => {
    const dataParsedFullData = parseApportionmentResultData({
      ...apportionmentResultMock1,
      percentageDiff: -95.69
    })

    expect(dataParsedFullData).toEqual({
      date: '2024-09-01',
      label: 'September',
      percentageDiff: -95.69,
      tagIcon: 'arrowDown',
      tagType: 'success',
      totalValueCurrent: 1457916.87,
      totalValuePrevious: ********.38
    })

    const dataParsedDataDefault = parseApportionmentResultData({
      ...apportionmentResultMock1,
      percentageDiff: 0
    })

    expect(dataParsedDataDefault).toEqual({
      date: '2024-09-01',
      label: 'September',
      percentageDiff: 0,
      tagIcon: 'arrowDown',
      tagType: 'default',
      totalValueCurrent: 1457916.87,
      totalValuePrevious: ********.38
    })

    const dataParsedDataError = parseApportionmentResultData({
      ...apportionmentResultMock1,
      totalValueCurrent: ********.38,
      totalValuePrevious: 1457916.87
    })

    expect(dataParsedDataError).toEqual({
      date: '2024-09-01',
      label: 'September',
      percentageDiff: -95.69,
      tagIcon: 'arrowUp',
      tagType: 'error',
      totalValueCurrent: ********.38,
      totalValuePrevious: 1457916.87
    })
  })

  it('should check return the util function parseApportionmentPeriodData', () => {
    const dataParsed = parseApportionmentPeriodData(apportionmentPeriodMock1)

    expect(dataParsed).toEqual({
      id: 186,
      period: '2024-12-01',
      periodEnd: '2024-12-31',
      periodStart: '2024-12-01'
    })
  })

  it('should check return the util function parseCostCenterResultData', () => {
    const dataParsed = parseCostCenterResultData(costCenterResultMock1)

    expect(dataParsed).toEqual({
      additionalConsumptionValue: 291.18,
      additionalConsumptionValuePrevious: null,
      additionalFeesValue: 0.29,
      additionalFeesValuePrevious: 0.25,
      costCenterId: 411,
      costCenterName: 'R115 - CDC - 0512 - Equipment - Medição Belo Horizonte',
      date: '2024-07-01',
      equipmentsValue: 322972.69,
      equipmentsValuePrevious: 312966.74,
      percentageDiff: 3.29,
      percentageDiffConsumptionUsed: 10.78,
      tariffType: 'Cativo',
      tariffValue: 291.19,
      totalValue: 323264.16,
      totalValuePrevious: 312966.99,
      consumptionTotal: 1193.629
    })
  })

  it('should check return the util function renderTableResultHistoryData', () => {
    const dataParsedWithoutQuery = renderTableResultHistoryData({
      items: [costCenterResultMock1, costCenterResultMock2],
      query: '',
      page: 1
    })

    expect(dataParsedWithoutQuery).toEqual({
      items: [costCenterResultMock1, costCenterResultMock2],
      itemsFiltered: [costCenterResultMock1, costCenterResultMock2],
      query: '',
      page: 1,
      lastPage: 1
    })

    const dataParsedWithQuery = renderTableResultHistoryData({
      items: [costCenterResultMock1, costCenterResultMock2],
      query: 'R115 - CDC - 0512 - Equipment - Medição Belo Horizonte',
      page: 1
    })

    expect(dataParsedWithQuery).toEqual({
      items: [costCenterResultMock1, costCenterResultMock2],
      itemsFiltered: [costCenterResultMock1],
      query: 'R115 - CDC - 0512 - Equipment - Medição Belo Horizonte',
      page: 1,
      lastPage: 1
    })
  })
})
