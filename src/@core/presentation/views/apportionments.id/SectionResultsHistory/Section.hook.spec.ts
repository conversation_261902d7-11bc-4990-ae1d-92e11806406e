import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { renderHook } from '@testing-library/react'
import { useLanguageSection } from './Section.hook'

describe('src/@core/presentation/views/apportionments.id/SectionResultsHistory/Section.hook | useLanguageSection', () => {
  it('check de page title', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageSection()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.title).toBe('Histórico de resultados')
  })
})
