import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { languageByMode } from '@/@core/language'

export const useLanguageSection = () => {
  const { lang } = useSystemLanguageStore().state
  const { pages, errors } = languageByMode(lang)
  const { sectionResultsHistory } = pages.apportionmentsId
  const { title, table, graphic, withoutData } = sectionResultsHistory

  return {
    title,
    table: {
      columns: {
        date: table.columns.date,
        constCenter: table.columns.constCenter,
        tariffType: table.columns.tariffType,
        tariff: table.columns.tariff,
        amountConsumer: table.columns.amountConsumer,
        actions: table.columns.actions
      }
    },
    graphic,
    withoutData,
    errors
  }
}
