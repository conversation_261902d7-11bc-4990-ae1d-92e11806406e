import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'

import { useNumberFormatter } from '@/@core/framework/hooks/useNumberFormatter/hook'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import dayjs from 'dayjs'
import Highcharts from 'highcharts'
import HighchartsReact from 'highcharts-react-official'
import { useMethodPage, useStatePage } from '../page.hooks'
import { IApportionmentResultPage } from '../page.types'
import { renderTableResultHistoryData } from '../page.utils'
import { useLanguageSection } from './Section.hook'

export const SectionResultsHistory = () => {
  const statePage = useStatePage()

  return (
    <div className="my-4">
      <ButtonMonthList />
      <ButtonMonthListEmpty />

      <div
        className={cn('grid grid-cols-1 lg:grid-cols-3 gap-1', {
          'opacity-0 hidden': !statePage.apportionmentResults.length
        })}
      >
        <div className="lg:col-span-2">
          <ResultsHistoryTable />
        </div>
        <div className="rounded-lg border border-primary lg:col-span-1 py-[20px]">
          <ResultsHistoryGraphic />
        </div>
      </div>
    </div>
  )
}

const ButtonMonthList = () => {
  const statePage = useStatePage()

  return (
    <ul
      className={cn('flex flex-row gap-2 overflow-x-auto my-4', {
        hidden: !statePage.apportionmentResults.length
      })}
    >
      {statePage.apportionmentResults
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .map((apportionmentResult) => (
          <ButtonMonth
            key={apportionmentResult.label}
            {...apportionmentResult}
          />
        ))}
    </ul>
  )
}
const ButtonMonth = (apportionmentResult: IApportionmentResultPage) => {
  const systemLoading = useSystemLoadingStore()
  const statePage = useStatePage()
  const methodPage = useMethodPage()
  const { currencyFormat } = useNumberFormatter()

  return (
    <button
      key={apportionmentResult.date}
      className={cn(
        'flex flex-col justify-start *:mx-auto gap-[10px] min-h-[132px] min-w-[142px] py-[20px] px-[12px] rounded',
        'shadow shadow-comerc-grayLight-100 dark:shadow-comerc-grayLight-800',
        'text-primary hover:bg-primary_hover',
        'border border-comerc-grayLight-100 dark:border-comerc-grayLight-800',
        'classe-sei-lá',
        {
          'border-comerc-brand01-300 dark:border-comerc-brand01-400':
            statePage.resultsHistory.monthActive === apportionmentResult.date,
          'bg-primary_hover': systemLoading.state.loading
          //'is-disabled': !apportionmentResult.totalValueCurrent
        }
      )}
      data-status={apportionmentResult.label}
      onClick={() =>
        methodPage.getResultHistoryByMonth(apportionmentResult.date)
      }
      disabled={systemLoading.state.loading}
    >
      <span className="inline-block leading-[24px] text-center font-acuminPro-Bold">
        {apportionmentResult.label}
      </span>

      <span className="inline-block leading-[24px] text-[16px] text-center">
        {currencyFormat(apportionmentResult.totalValueCurrent || 0, {
          locale: 'pt-BR',
          currency: 'BRL'
        })}
      </span>

      <Badge.Root
        className="gap-1 max-h-[22px]"
        variant={apportionmentResult.tagType as 'success'}
      >
        <Badge.Icon className="flex *:m-auto">
          {!!apportionmentResult.percentageDiff ? (
            <>
              {apportionmentResult.tagType === 'success' ? (
                <Icon
                  icon="arrow-up"
                  width="18"
                  height="18"
                  viewBox="0 0 13 12"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="icon-success"
                />
              ) : (
                <Icon
                  icon="arrow-up"
                  width="18"
                  height="18"
                  viewBox="0 0 13 12"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="icon-error rotate-180"
                />
              )}
            </>
          ) : (
            <Icon
              icon="minus"
              width="18"
              height="18"
              viewBox="0 0 12 12"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="icon-default"
            />
          )}
        </Badge.Icon>

        <Badge.Content
          className={cn('text-[12px]', {
            hidden: !apportionmentResult.percentageDiff
          })}
        >
          {apportionmentResult.percentageDiff} %
        </Badge.Content>
      </Badge.Root>
    </button>
  )
}
const ButtonMonthListEmpty = () => {
  const statePage = useStatePage()
  const languageSection = useLanguageSection()

  return (
    <p
      className={cn('text-center my-4', {
        hidden: statePage.apportionmentResults.length
      })}
    >
      {languageSection.withoutData}
    </p>
  )
}

const ResultsHistoryTable = () => {
  const systemLoading = useSystemLoadingStore()
  const statePage = useStatePage()
  const languageSection = useLanguageSection()
  const { currencyFormat, decimalFormat } = useNumberFormatter()

  const handleInputQuery = (query: string) => {
    statePage.set({
      resultsHistory: {
        ...statePage.resultsHistory,
        ...renderTableResultHistoryData({
          items: statePage.resultsHistory.items,
          query
        })
      }
    })
  }
  const handleChangePage = (page: number) => {
    statePage.set({
      resultsHistory: {
        ...statePage.resultsHistory,
        ...renderTableResultHistoryData({
          items: statePage.resultsHistory.items,
          query: statePage.resultsHistory.query,
          page
        })
      }
    })
  }

  return (
    <Table.Root>
      <Table.Info>
        <Table.InfoTitle>{languageSection.title}</Table.InfoTitle>

        <Input.Root className="ml-auto">
          <Input.Content
            type="search"
            slotStart={
              <Icon
                icon="searchLg"
                className="icon-menu-primary"
                height="24"
                width="24"
                viewBox="0 0 20 20"
              />
            }
            value={statePage.resultsHistory.query}
            onChange={({ target }) => handleInputQuery(target.value)}
            // onKeyUp={(e) => handleKeyEnter(e.key, methodPage.getData)}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>
      </Table.Info>
      <Table.Header>
        <Table.Row>
          <Table.Head>{languageSection.table.columns.date}</Table.Head>
          <Table.Head>{languageSection.table.columns.constCenter}</Table.Head>
          <Table.Head>{languageSection.table.columns.tariffType}</Table.Head>
          <Table.Head>{languageSection.table.columns.tariff}</Table.Head>
          <Table.Head>Total</Table.Head>
          <Table.Head>{languageSection.table.columns.amountConsumer}</Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {statePage.resultsHistory.itemsFiltered.map((costCenterResult, i) => (
          <Table.Row key={i}>
            <Table.Cell>
              {dayjs(costCenterResult.date).format('MM/YYYY')}
            </Table.Cell>
            <Table.Cell>{costCenterResult.costCenterName}</Table.Cell>
            <Table.Cell>{costCenterResult.tariffType}</Table.Cell>
            <Table.Cell>
              {costCenterResult.tariffValue
                ? currencyFormat(costCenterResult.tariffValue, {
                  locale: 'pt-BR',
                  currency: 'BRL'
                })
                : ''}
            </Table.Cell>
            <Table.Cell>
              {costCenterResult.totalValue
                ? currencyFormat(costCenterResult.totalValue, {
                  locale: 'pt-BR',
                  currency: 'BRL'
                })
                : ''}
            </Table.Cell>

            <Table.Cell>
              {costCenterResult.consumptionTotal
                ? `${statePage.apportionment.apportionmentMeasureUnit?.name
                } ${decimalFormat(costCenterResult.consumptionTotal, {
                  fractionDigits: 3
                })}`
                : ''}
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>

      <Table.Paginate
        status={systemLoading.state.loading}
        currentPage={statePage.resultsHistory.page}
        lastPage={statePage.resultsHistory.lastPage}
        handleChangePage={(page) => handleChangePage(page)}
      />
    </Table.Root>
  )
}
const ResultsHistoryGraphic = () => {
  const statePage = useStatePage()
  const languageSection = useLanguageSection()

  return (
    <>
      <p className="text-2xl bold leading-8 text-center mx-[24px]">
        {languageSection.graphic.title}
      </p>
      <span className="border-t-[1px] border-primary inline-block w-[calc(100%_-_48px)] mx-[24px]"></span>
      <HighchartsReact
        highcharts={Highcharts}
        options={{
          chart: {
            zoomType: 'x',
            type: 'pie',
            backgroundColor: 'transparent'
          },
          title: {
            text: null
          },
          legend: {
            align: 'right',
            verticalAlign: 'top',
            layout: 'vertical',
            itemMarginBottom: 10,
            y: 100,
            enabled: true,
            padding: 5
          },
          credits: {
            enabled: false
          },
          series: [
            {
              type: 'pie',
              innerSize: '80%',
              name: 'Custos',
              data: (statePage.resultsHistory.itemsFiltered || [])
                .map(({ totalValue, costCenterName }) => {
                  return {
                    y: totalValue,
                    name: costCenterName || 'N/D'
                  }
                })
                .sort((a, b) => (a.y === null || b.y === null ? -1 : b.y - a.y))
            }
          ],
          tooltip: {
            pointFormat: '{series.name}: <b>R$ {point.y}</b>'
          },
          plotOptions: {
            pie: {
              cursor: 'pointer',
              dataLabels: {
                enabled: true,
                format: '{point.percentageDiff:.1f} %'
              },
              showInLegend: true
            }
          }
        }}
      />
    </>
  )
}
