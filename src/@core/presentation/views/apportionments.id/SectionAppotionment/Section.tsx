import { useNumberFormatter } from '@/@core/framework/hooks/useNumberFormatter/hook'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Table } from '@/@core/presentation/shared/ui/table'
import { useStatePage } from '../page.hooks'
import { useLanguageSection } from './Section.hook'

export const SectionApportionment = () => {
  const languageSection = useLanguageSection()
  const statePage = useStatePage()
  const mapApportionments = statePage.apportionment.id
    ? [statePage.apportionment]
    : []
  const { currencyFormat } = useNumberFormatter()

  return (
    <Table.Root className="mb-4" classNameWrapper="block">
      <Table.Info className="justify-between">
        <Table.InfoTitle>{languageSection.title}</Table.InfoTitle>
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head>{languageSection.table.columns.company}</Table.Head>
          <Table.Head>
            {languageSection.table.columns.apportionmentType}
          </Table.Head>
          <Table.Head>{languageSection.table.columns.tariff}</Table.Head>
          <Table.Head>
            {languageSection.table.columns.costCenterLength}
          </Table.Head>
          <Table.Head>
            {languageSection.table.columns.totalValueCurrent}
          </Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {mapApportionments.map((apportionment, i) => (
          <Table.Row key={i}>
            <Table.Cell>{apportionment.company?.name}</Table.Cell>
            <Table.Cell>{apportionment.apportionmentType?.name}</Table.Cell>
            <Table.Cell>
              <Badge.Root variant="alert" className="w-min">
                <Badge.Content>
                  {apportionment.apportionmentTariffType?.name}
                </Badge.Content>
              </Badge.Root>
            </Table.Cell>
            <Table.Cell>{apportionment.costCenterIds?.length}</Table.Cell>
            <Table.Cell>
              {currencyFormat(
                apportionment.lastResult?.totalValueCurrent || 0,
                {
                  locale: 'pt-BR',
                  currency: 'BRL'
                }
              )}
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
    </Table.Root>
  )
}
