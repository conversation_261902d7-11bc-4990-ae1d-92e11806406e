import { useRouter } from 'next/router'
import { FC, useState } from 'react'

import { mapColorsDevicesById } from '@/@core/content/mapColorsDevices.content'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { pageDevicesCookie } from '@/@core/infra/memory/cookie'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import { mergeSortOrderData } from '@/@core/utils/handleSorteColumn'

import { Badge } from '@/@core/presentation/shared/ui/badge'
import { useLanguagePage, useMethodPage, useStatePage } from '../page.hooks'
import { IDevicePage } from '../page.types'

export const DevicesTable = () => {
  const router = useRouter()

  const statePage = useStatePage()
  const methodPage = useMethodPage()
  const languagePage = useLanguagePage()

  const systemLoading = useSystemLoadingStore()
  const permissions = useSystemStore().state.permissions

  const searchFields = pageDevicesCookie.get()

  const handleSortColumn = async (props: {
    key: 'id' | 'code' | 'equipment' | 'type'
  }) => {
    const { sort, order } = mergeSortOrderData(props.key, searchFields)

    pageDevicesCookie.set({ order, sort })

    await methodPage.getData()
  }

  return (
    <Table.Root>
      <Table.Info>
        <Table.InfoTitle>{languagePage.page.title}</Table.InfoTitle>

        <Table.InfoBadge className="lg:mr-auto">
          {statePage.total}
          <span className="hidden md:inline-block ml-1">
            {languagePage.table.totalRegisters}
          </span>
        </Table.InfoBadge>

        <Table.InfoNewRegister
          onClick={() => router.push('/devices/new')}
          permission={permissions.properties?.create}
        />
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head
            className="cursor-pointer"
            onClick={() => handleSortColumn({ key: 'id' })}
          >ID
            <Table.CellIcon
              field="id"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head
            className="cursor-pointer"
            onClick={() => handleSortColumn({ key: 'code' })}
          >
            {languagePage.table.columns['code']}
            <Table.CellIcon
              field="code"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head
            className="cursor-pointer"
            onClick={() => handleSortColumn({ key: 'type' })}
          >
            {languagePage.table.columns['type']}
            <Table.CellIcon
              field="type"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head>{languagePage.table.columns['status']}</Table.Head>
          <Table.Head>{languagePage.table.columns['company']}</Table.Head>
          <Table.Head
            className="cursor-pointer"
            onClick={() => handleSortColumn({ key: 'equipment' })}
          >
            {languagePage.table.columns['equipment']}
            <Table.CellIcon
              field="equipment"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head></Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {statePage.items.map((device) => (
          <Table.Row key={device.id}>
            <Table.Cell>{device.id}</Table.Cell>
            <Table.Cell>{device.code}</Table.Cell>
            <Table.Cell>{device.typeName}</Table.Cell>
            <Table.Cell>
              <Badge.Root className={cn(
                'cell-device-status',
                mapColorsDevicesById[device.statusId]
              )}>
                <Badge.Content>
                  {device.statusName}
                </Badge.Content>
              </Badge.Root>
            </Table.Cell>
            <Table.Cell>{device.companyName}</Table.Cell>
            <Table.Cell>{device.equipmentName}</Table.Cell>
            <Table.Cell width={80}>
              <Actions device={device} />
            </Table.Cell>
          </Table.Row>
        ))}

        <Table.RowLoading status={systemLoading.state.loading} colSpan={6} />
      </Table.Body>

      <Table.Mobile>
        {statePage.items.map((device) => (
          <TableMobile.Item key={device.id}>
            <TableMobile.Head />
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns['code']}</TableMobile.Cell>
              <TableMobile.Cell>{device.code}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns['type']}</TableMobile.Cell>
              <TableMobile.Cell>{device.typeName}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns['status']}</TableMobile.Cell>
              <TableMobile.Cell>
                <Badge.Root className={cn(
                  'cell-device-status min-w-28',
                  mapColorsDevicesById[device.statusId]
                )}>
                  <Badge.Content>
                    {device.statusName}
                  </Badge.Content>
                </Badge.Root>
              </TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns['company']}</TableMobile.Cell>
              <TableMobile.Cell>{device.companyName}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns['equipment']}</TableMobile.Cell>
              <TableMobile.Cell>{device.equipmentName}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Footer>
              <Actions device={device} />
            </TableMobile.Footer>
          </TableMobile.Item>
        ))}
      </Table.Mobile>

      <Table.Paginate
        status={systemLoading.state.loading}
        lastPage={statePage.lastPage}
        currentPage={searchFields.page}
        handleChangePage={(page) => {
          pageDevicesCookie.set({ page })
          methodPage.getData()
        }}
      />
    </Table.Root>
  )
}

const Actions: FC<{ device: IDevicePage }> = ({ device }) => {
  const permissionStore = useSystemStore().state.permissions
  const router = useRouter()

  return (
    <div className="table-td-actions">
      <ModalDelete deviceId={device.id} deviceCode={device.code} />

      {permissionStore?.['register-device']?.create && (
        <button
          className="table-td-action hover:cursor-pointer"
          onClick={() => router.push(`/devices/${device.id}`)}
        >
          <Icon
            icon="edit"
            className="icon-menu-primary"
            height="20"
            width="20"
            viewBox="0 0 20 20"
          />
        </button>
      )}
    </div>
  )
}

interface ModalDeleteProps {
  deviceId: number
  deviceCode: string
}
const ModalDelete: FC<ModalDeleteProps> = ({ deviceId, deviceCode }) => {
  const [openDialog, setOpenDialog] = useState(false)
  const languagePage = useLanguagePage()
  const methodPage = useMethodPage()

  const handleClick = async () => {
    const result = await methodPage.handleDelete(deviceId)
    if (result) setOpenDialog(false)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger className="p-1 w-8 text-center text-[19px]">
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </Dialog.Trigger>

      <Dialog.Content size="lg2">
        <Dialog.Header>
          <Dialog.Title>{languagePage.table.modalDelete.title}</Dialog.Title>
        </Dialog.Header>

        <Dialog.Description>
          {languagePage.table.modalDelete.textInfo}{' '}
          <span className="text-red-600">{deviceCode}</span>
        </Dialog.Description>

        <Dialog.Footer>
          <Button
            variant="secondary-gray"
            type="button"
            onClick={() => setOpenDialog(false)}
          >
            {languagePage.table.modalDelete.textCancel}
          </Button>

          <Button type="button" variant="error-primary" onClick={handleClick}>
            {languagePage.table.modalDelete.textConfirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
