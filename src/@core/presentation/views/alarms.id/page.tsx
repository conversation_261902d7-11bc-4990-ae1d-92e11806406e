import { useEffect } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'

import { useBreadcrumb } from '@/@core/framework/hooks/useBreadcrumb'
import { useTitlePage } from '@/@core/framework/hooks/useTitlePage'
import { useLanguageAlarmesIdPage, useStateAlarmesIdPage } from './page.hooks'

import { pageAlarmsIdCookie } from '@/@core/infra/memory/cookie/PageAlarmsIdCookie'

import { HeaderList } from '@/@core/presentation/shared/pages/HeaderList'
import { PageContent } from '@/@core/presentation/shared/pages/PageContent'
import { Breadcrumbs } from '@/@core/presentation/shared/ui/breadcrumbs'
import { Tabs } from '@/@core/presentation/shared/ui/tabs'
import { TabData } from './TabData/Tab'
import { TabHistory } from './TabHistory/Tab'
import { TabMonitoring } from './TabMonitoring/Tab'
import { TabNotification } from './TabNotification/Tab'
import { TabRules } from './TabRules/Tab'

export const Page = () => {
  const statePage = useStateAlarmesIdPage()
  const languagePage = useLanguageAlarmesIdPage()

  useTitlePage(languagePage.page.title)
  useBreadcrumb('alarms.id')

  useEffect(() => {
    return () => {
      statePage.reset()
      pageAlarmsIdCookie.reset()
    }
  }, [])

  return (
    <PageContent>
      <Breadcrumbs />

      <HeaderList.Root>
        <HeaderList.Content
          title={languagePage.page.title}
          className="mr-auto"
        />
      </HeaderList.Root>

      <Tabs.Root
        variant="primary"
        className="lg:flex items-start gap-2"
        value={statePage.tab.active}
        onValueChange={statePage.setTab}
      >
        <Tabs.List className="mb-3 lg:flex-col lg:pr-2 lg:mr-6 lg:min-w-[177px] lg:min-h-[350px] justify-start">
          <Tabs.Trigger className="flex gap-2" value="data">
            {/* <Icon
              icon="clipboard"
              width="18"
              height="18"
              fill="none"
              className="icon-menu-primary"
              strokeWidth="2"
              viewBox="0 0 24 24"
            /> */}
            {languagePage.page.tabs.data}
          </Tabs.Trigger>

          <Tabs.Trigger
            className={cn('w-full justify-start')}
            value="rules"
            // disabled={!statePage.tabData.alarme?.id}
          >
            {languagePage.page.tabs.rules}
          </Tabs.Trigger>

          <Tabs.Trigger
            className={cn('w-full justify-start')}
            value="monitoring"
            // disabled={!statePage.tabData.alarme?.id}
          >
            {languagePage.page.tabs.monitoring}
          </Tabs.Trigger>
          <Tabs.Trigger
            className={cn('w-full justify-start')}
            value="notification"
            // disabled={!statePage.tabData.alarme?.id}
          >
            {languagePage.page.tabs.notification}
          </Tabs.Trigger>
          <Tabs.Trigger
            className={cn('w-full justify-start')}
            value="history"
            // disabled={!statePage.tabData.alarme?.id}
          >
            {languagePage.page.tabs.history}
          </Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="data" className="w-full">
          <TabData />
        </Tabs.Content>

        <Tabs.Content value="rules" className="w-full">
          <TabRules />
        </Tabs.Content>

        <Tabs.Content value="monitoring" className="w-full">
          <TabMonitoring />
        </Tabs.Content>

        <Tabs.Content value="notification" className="w-full">
          <TabNotification />
        </Tabs.Content>

        <Tabs.Content value="history" className="w-full">
          <TabHistory />
        </Tabs.Content>
      </Tabs.Root>
    </PageContent>
  )
}
