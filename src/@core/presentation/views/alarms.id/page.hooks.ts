import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { languageByMode } from '@/@core/language'

import { stateData } from './page.content'
import { IState } from './page.types'

const statePage = create<IState>((set) => ({
  ...stateData,
  set: (initialData) => {
    set((state) => ({ ...state, ...initialData }))
  },
  reset: () => {
    set((state) => ({ ...state, ...stateData }))
  },
  setTab: (active: string) => {
    set((state) => ({
      ...state,
      tab: { ...state.tab, active }
    }))
  },
  setAlarmData: (value) => {
    set((state) => ({
      ...state,
      alarmData: { ...state.alarmData, ...value }
    }))
  },
  setTabData: (value) => {
    set((state) => ({
      ...state,
      tabData: { ...state.tabData, ...value }
    }))
  },
  setTabMonitoring: (value) => {
    set((state) => ({
      ...state,
      tabMonitoring: { ...state.tabMonitoring, ...value }
    }))
  },
  setTabMounted: (tabName: string) => {
    set((state) => {
      let mounted = JSON.parse(JSON.stringify(state.tab.mounted))

      mounted = {
        ...mounted,
        [tabName]: true
      }

      return {
        ...state,
        tab: { ...state.tab, mounted }
      }
    })
  }
}))

export const useStateAlarmesIdPage = () => {
  const state = statePage()

  const isEdit = !!state.alarmData.id

  return { ...state, isEdit }
}
export const useLanguageAlarmesIdPage = () => {
  const lang = useSystemLanguageStore().state.lang

  const { pages } = languageByMode(lang)

  const { tabs, title, tabData } = pages.alarmsId

  return {
    page: { tabs, title },
    tabData: {
      ...tabData
    }
  }
}
