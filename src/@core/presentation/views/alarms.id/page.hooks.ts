import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { languageByMode } from '@/@core/language'

import { stateData } from './page.content'
import { IState } from './page.types'

const statePage = create<IState>((set) => ({
  ...stateData,
  set: (initialData) => {
    set((state) => ({ ...state, ...initialData }))
  },
  reset: () => {
    set((state) => ({ ...state, ...stateData }))
  },
  setTab: (active: string) => {
    set((state) => ({
      ...state,
      tab: { ...state.tab, active }
    }))
  }
}))

export const useStateAlarmesIdPage = () => {
  const state = statePage()

  const isEdit = !!state.tabData.alarme?.id

  return { ...state, isEdit }
}
export const useLanguageAlarmesIdPage = () => {
  const lang = useSystemLanguageStore().state.lang

  const { pages } = languageByMode(lang)

  const { tabs, title } = pages.alarmsId

  return {
    page: { tabs, title }
  }
}
