import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { languageByMode } from '@/@core/language'

export const useLanguageTabHistory = () => {
  const lang = useSystemLanguageStore().state.lang

  const { pages } = languageByMode(lang)

  const { form, title, table } = pages.alarmsId.tabHistory

  return {
    title,
    form: {
      input: {
        status: form.input.status,
        equipment: form.input.equipment
      }
    },
    table: {
      columns: {
        status: table.columns.status,
        company: table.columns.company,
        equipmenet: table.columns.equipmenet,
        acionado: table.columns.acionado,
        normalizado: table.columns.normalizado,
        actions: table.columns.actions
      }
    }
  }
}
