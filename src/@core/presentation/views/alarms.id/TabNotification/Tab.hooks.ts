import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { languageByMode } from '@/@core/language'

export const useLanguageTabNotification = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields

  const { form, title, table } = pages.alarmsId.tabNotification

  return {
    title,
    form: {
      input: {
        channel: form.input.channel,
        frequency: form.input.frequency,
        user: form.input.user
      },
      requiredField,
      btn: { cancel, save, confirm, add, clean }
    },
    table: {
      columns: {
        channel: table.columns.channel,
        frequency: table.columns.frequency,
        user: table.columns.user,
        actions: table.columns.actions
      }
    }
  }
}
