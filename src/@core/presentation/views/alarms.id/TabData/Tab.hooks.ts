import { useRouter } from 'next/router'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { useLog } from '@/@core/logging/logger'
import { useLanguageAlarmesIdPage, useStateAlarmesIdPage } from '../page.hooks'

import { alarmsApiV4 } from '@/@core/infra/api/AlarmsApiV4'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import loggerRequest from '@/@core/logging/loggerRequest'

import { AlarmFormData } from './Tab.types'
import { formDataOutput, parseAlarmDataInput } from './Tab.utils'

export const useMethodTabData = () => {
  const router = useRouter()
  const log = useLog()

  const statePage = useStateAlarmesIdPage()
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const languagePage = useLanguageAlarmesIdPage()

  const fetchData = async () => {
    try {
      systemLoading.setLoading(true)

      const result = await alarmsApiV4(http).getById(Number(router.query?.id))

      if (result.status === 204) {
        return
      }

      statePage.setAlarmData({
        ...parseAlarmDataInput(result.data)
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/tabData/useMethodTabData/fetchData'
      })
      statePage.setAlarmData({})
    } finally {
      systemLoading.setLoading(false)
    }
  }

  const onSubmit = async (data: AlarmFormData) => {
    if (!statePage.isEdit) {
      statePage.setTabData({
        alarm: {
          ...statePage.tabData.alarm,
          ...data
        }
      })

      statePage.setTab('rules')
      return
    }

    try {
      systemLoading.setData({ pageLoading: true })

      const result = await alarmsApiV4(http).update(
        Number(router.query?.id),
        formDataOutput(statePage.alarmData, data)
      )

      systemToast.addToast({
        message: languagePage.tabData.formMessages.updateSuccessMessage
      })

      statePage.setAlarmData({
        ...parseAlarmDataInput(result.data)
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/tabData/useMethodTabData/onSubmit'
      })

      systemToast.addToast({
        message: languagePage.tabData.formMessages.updateErrorMessage
      })
    } finally {
      systemLoading.setData({ pageLoading: false })
    }
  }
  return { fetchData, onSubmit }
}

export const useLanguageTabData = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields

  const { form, title } = pages.alarmsId.tabData

  return {
    title,
    form: {
      input: {
        status: form.input.status,
        name: form.input.name,
        description: form.input.description,
        category: form.input.category,
        account: form.input.account
      },
      requiredField,
      btn: { cancel, save, confirm, add, clean, continue: btn.continue }
    }
  }
}
