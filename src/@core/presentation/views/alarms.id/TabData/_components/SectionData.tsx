import { useRouter } from 'next/router'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

import useAuthStore from '@/@core/framework/store/hook/useAuthStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { useStateAlarmesIdPage } from '@/@core/presentation/views/alarms.id/page.hooks'
import {
  useLanguageTabData,
  useMethodTabData
} from '@/@core/presentation/views/alarms.id/TabData/Tab.hooks'

import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { http } from '@/@core/infra/http'

import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'
import {
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'

import { updateFormDataInput } from '@/@core/presentation/views/alarms.id/TabData/Tab.utils'
import { AlarmFormData } from '../Tab.types'

const CONST_CATEGORY_USER_ID = '3'

export const SectionData = () => {
  const languageTab = useLanguageTabData()
  const statePage = useStateAlarmesIdPage()
  const router = useRouter()
  const authStore = useAuthStore()
  const systemLoading = useSystemLoadingStore()
  const formFields = useFormFields()
  const methodTabData = useMethodTabData()

  useEffect(() => {
    if (
      !statePage.isEdit ||
      Object.keys(statePage.tabData.alarm).length !== 0
    ) {
      formFields.setValues(statePage.tabData.alarm)
    }
  }, [statePage.tabData.alarm])

  useEffect(() => {
    if (statePage.isEdit || Object.keys(statePage.alarmData).length !== 0) {
      formFields.setValues(updateFormDataInput(statePage.alarmData))
    }
  }, [statePage.alarmData])

  const showAccountInput = useMemo(() => {
    return (
      authStore.state.isSuperAdmin &&
      formFields.values.categoryId === CONST_CATEGORY_USER_ID
    )
  }, [authStore.state.isSuperAdmin, formFields.values.categoryId])

  const categoryOptions = useMemo(() => {
    if (authStore.state.isSuperAdmin) {
      return [
        { value: '2', label: 'Operação' },
        { value: '3', label: 'Usuário' }
      ]
    }
    return [{ value: '3', label: 'Usuário' }]
  }, [authStore.state.isSuperAdmin])

  return (
    <>
      <form
        className="form-container"
        onSubmit={formFields.handleSubmit(() => {
          methodTabData.onSubmit(formFields.values)
        })}
      >
        <div className="w-full grid grid-cols-1 gap-8 max-w-[1920px] mx-auto">
          <Switch.Content
            label={languageTab.form.input.status}
            labelPosition="end"
            checked={!!formFields.values.status}
            onChange={(value) => {
              formFields.setValue('status', value)
            }}
            className="mb-2"
          />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <Input.Root>
            <Input.Label htmlFor="name">
              {languageTab.form.input.name}
            </Input.Label>
            <Input.Content
              id="name"
              value={formFields.values.name}
              onChange={(e) => {
                formFields.setValue('name', e.target.value)
              }}
              helperText={formFields.errors.name?.message}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="description">
              {languageTab.form.input.description}
            </Input.Label>
            <Input.Content
              id="description"
              value={formFields.values.description}
              onChange={(e) => {
                formFields.setValue('description', e.target.value)
              }}
              helperText={formFields.errors.description?.message}
            />
          </Input.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="category">
              {languageTab.form.input.category}
            </TagInput.Label>
            <TagInput.Content
              name="category"
              value={formFields.values.categoryId}
              disabled={statePage.isEdit ?? systemLoading.state.loading}
              onChange={(items) => {
                formFields.setValue('categoryId', items![0]!.value)
                authStore.state.isSuperAdmin &&
                  formFields.setValue('account', [])
              }}
              options={categoryOptions}
              helperText={formFields.errors.categoryId?.message}
            />
          </TagInput.Root>

          {showAccountInput && (
            <TagInput.Root>
              <TagInput.Label htmlFor="account">
                {languageTab.form.input.account}
              </TagInput.Label>
              <TagInput.ContentApi
                name="account"
                value={formatInputValues(formFields.values.account)}
                onChange={(values) => {
                  formFields.setValue('account', formatOutputValues(values))
                }}
                featchData={(p) =>
                  companiesApiV4(http).get({ ...p, sort: 'nome' })
                }
                helperText={formFields.errors.account?.message}
                disabled={statePage.isEdit ?? systemLoading.state.loading}
              />
            </TagInput.Root>
          )}
        </div>

        <div className="footer-form">
          <Button
            type="button"
            onClick={() => router.push('/alarms')}
            disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.cancel}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={systemLoading.state.loading}
          >
            {statePage.isEdit
              ? languageTab.form.btn.save
              : languageTab.form.btn.continue}
          </Button>
        </div>
      </form>
    </>
  )
}

const useFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabData()

  const formSchema = z
    .object({
      id: z.union([z.number(), z.null()]),
      name: z.string().min(1, { message }),
      description: z.string(),
      status: z.boolean(),
      account: z.array(z.object({ id: z.number(), name: z.string() })),
      categoryId: z.string()
    })
    .superRefine((values, ctx) => {
      if (!values.name || values.name.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message,
          path: ['name']
        })
      }
      if (!values.categoryId || values.categoryId.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message,
          path: ['categoryId']
        })
      }
      if (
        values.categoryId === CONST_CATEGORY_USER_ID &&
        (!values.account || values.account.length === 0)
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message,
          path: ['account']
        })
      }
    })

  type FormSchema = z.infer<typeof formSchema>

  const authStore = useAuthStore()

  const parseInitialData = (data: Partial<AlarmFormData>): FormSchema => {
    let accountUser = data.account

    if (
      (!accountUser || accountUser.length === 0) &&
      !authStore.state.isSuperAdmin
    ) {
      accountUser = [
        {
          id: authStore.state.me.accountId!,
          name: authStore.state.me.accountName
        }
      ]
    }

    return {
      id: data.id ?? null,
      name: data.name ?? '',
      description: data.description ?? '',
      status: data.status ?? false,
      account: accountUser ?? [],
      categoryId: data.categoryId ?? ''
    }
  }

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const setValues = (data: Partial<AlarmFormData>) => {
    const dataParsed = parseInitialData(data)

    setValue('id', dataParsed.id)
    setValue('name', dataParsed.name)
    setValue('description', dataParsed.description)
    setValue('status', dataParsed.status)
    setValue('account', dataParsed.account)
    setValue('categoryId', dataParsed.categoryId)
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors }
}
