import { useRouter } from 'next/router'
import { useEffect, useRef } from 'react'

import { useLanguageAlarmesIdPage, useStateAlarmesIdPage } from '../page.hooks'
import { useMethodTabData } from './Tab.hooks'

import { PageSection } from '@/@core/presentation/shared/pages'
import checkQueryIdIsNumber from '@/@core/utils/checkIsNumber'
import { SectionData } from './_components/SectionData'

export const TabData = () => {
  const isMounted = useRef<boolean>(false)

  const router = useRouter()

  const statePage = useStateAlarmesIdPage()
  const languagePage = useLanguageAlarmesIdPage()
  const methodTabData = useMethodTabData()

  useEffect(() => {
    statePage.setTabMounted('tabData')

    if (
      !isMounted.current &&
      !statePage.tab.mounted?.tabData &&
      checkQueryIdIsNumber(router.query.id)
    ) {
      methodTabData.fetchData()
    }

    return () => {
      isMounted.current = true
    }
  }, [])

  return (
    <>
      <PageSection.Root className="mb-8 flex justify-center items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 w-full gap-4 max-w-[1920px]">
          <div className="col-span-2 space-y-[8px]">
            <PageSection.Content title={languagePage.page.tabs.data} />
            <p className="font-acuminPro-Regular text-[14px] leading-[20px] dark:text-comerc-vibra-grayLightMode-300">
              {languagePage.page.tabs.subtitleData}
            </p>
          </div>
        </div>
      </PageSection.Root>
      <SectionData />
    </>
  )
}
