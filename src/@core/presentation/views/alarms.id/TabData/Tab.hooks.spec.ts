import { cleanup, renderHook, waitFor } from '@testing-library/react'

import { appCookie } from '@/@core/infra/memory/cookie'
import { alarmMock1 } from '@/__mock__/content/api-alarms.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { useStateAlarmesIdPage } from '../page.hooks'
import { useLanguageTabData, useMethodTabData } from './Tab.hooks'

cleanup()

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/AlarmsApiV4/AlarmsApiV4')
const spyAlarmsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsApiV4/AlarmsApiV4'),
  'alarmsApiV4'
)

describe('@core/presentation/views/alarms.id/TabData/Tab.hooks', () => {
  beforeEach(() => {
    appCookie.init()

    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabData(),
        state: useStateAlarmesIdPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyAlarmsApiV4.mockImplementation(() => ({
      getById: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request successful without data **/
    spyAlarmsApiV4.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    /* request success **/
    spyAlarmsApiV4.mockImplementation(() => ({
      getById: jest.fn().mockResolvedValue({
        status: 200,
        data: alarmMock1
      })
    }))
    await waitFor(() => {
      result.current.method.fetchData()
    })

    expect(result.current.state.alarmData.id).toBe(alarmMock1.id)
    expect(result.current.state.alarmData.name).toBe(alarmMock1.name)
  })

  it('should check return the function onSubmit', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabData(),
        state: useStateAlarmesIdPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(() => result.current.state.setAlarmData({ id: 56 }))

    await waitFor(async () => {
      await result.current.method.onSubmit({
        id: 56,
        name: 'Fator potência horário',
        description: '',
        status: true,
        account: [
          {
            id: 9,
            name: 'Desenvolvimento'
          }
        ],
        categoryId: '3'
      })
    })

    await waitFor(() => result.current.state.reset())

    /* request success **/
    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 200,
        data: alarmMock1
      })
    }))

    await waitFor(() => result.current.state.setAlarmData({ id: 56 }))

    await waitFor(async () => {
      await result.current.method.onSubmit({
        id: 56,
        name: 'Fator potência horário',
        description: '',
        status: true,
        account: [
          {
            id: 9,
            name: 'Desenvolvimento'
          }
        ],
        categoryId: '3'
      })
    })

    expect(result.current.state.alarmData.id).toBe(alarmMock1.id)
    expect(result.current.state.alarmData.name).toBe(alarmMock1.name)

    await waitFor(() => result.current.state.reset())

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 200,
        data: alarmMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit({
        id: 56,
        name: 'Fator potência horário',
        description: '',
        status: true,
        account: [
          {
            id: 9,
            name: 'Desenvolvimento'
          }
        ],
        categoryId: '3'
      })
    })

    expect(result.current.state.tabData.alarm.id).toBe(alarmMock1.id)
    expect(result.current.state.tabData.alarm.name).toBe(alarmMock1.name)
  })
})

describe('src/@core/presentation/views/alarms.id/_components/TabData/Tab.hooks.spec.ts | useLanguageTabDat', () => {
  it('check the form texts', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabData()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    expect(result.current.language.form.input).toEqual({
      account: 'Conta',
      category: 'Categoria',
      description: 'Descrição',
      name: 'Nome',
      status: 'Status'
    })
  })
})
