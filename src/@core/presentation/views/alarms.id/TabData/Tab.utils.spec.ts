import {
  formDataOutput,
  parseAlarmDataInput,
  updateFormDataInput
} from './Tab.utils'

describe('formDataOutput', () => {
  const alarmData = {
    name: 'Alarm 1',
    description: 'Desc',
    timeConfirmation: 10,
    initialHour: '08:00',
    finalHour: '18:00',
    daysWeek: [1, 2, 3],
    daysRetention: 5,
    status: true,
    account: { id: 2, name: 'Account 2' },
    categoryId: 3
  }

  const formData = {
    id: 1,
    name: 'Alarm 2',
    description: 'Form Desc',
    status: false,
    account: [{ id: 5, name: 'Account 5' }],
    categoryId: '7'
  }

  it('merges and transforms account and categoryId correctly', () => {
    const result = formDataOutput(alarmData, formData)
    expect(result.accountId).toBe(5)
    expect(result.categoryId).toBe(7)
    expect(result.name).toBe('Alarm 2')
    expect(result.description).toBe('Form Desc')
    expect(result.status).toBe(false)
    expect(result.timeConfirmation).toBe(10)
    expect(result.initialHour).toBe('08:00')
    expect(result.finalHour).toBe('18:00')
    expect(result.daysWeek).toEqual([1, 2, 3])
    expect(result.daysRetention).toBe(5)
  })

  it('handles empty account array', () => {
    const formDataEmptyAccount = { ...formData, account: [] }
    const result = formDataOutput(alarmData, formDataEmptyAccount)
    expect(result.accountId).toBeUndefined()
  })

  it('handles categoryId as number', () => {
    const formDataNumCat = { ...formData, categoryId: '9' }
    const result = formDataOutput(alarmData, formDataNumCat)
    expect(result.categoryId).toBe(9)
  })
})
describe('parseAlarmDataInput', () => {
  it('should parse IAlarm into IAlarmData correctly', () => {
    const alarm = {
      id: 1,
      name: 'Alarm Test',
      description: 'Desc',
      status: true,
      account: { id: 2, name: 'Account' },
      category: { id: 3, name: 'Cat' },
      daysRetention: 10,
      timeConfirmation: 5,
      initialHour: '08:00',
      finalHour: '18:00',
      daysWeek: [1, 2, 3]
    }
    const result = parseAlarmDataInput(alarm as any)
    expect(result).toEqual({
      id: 1,
      name: 'Alarm Test',
      description: 'Desc',
      status: true,
      account: { id: 2, name: 'Account' },
      category: { id: 3, name: 'Cat' },
      categoryId: 3,
      daysRetention: 10,
      timeConfirmation: 5,
      initialHour: '08:00',
      finalHour: '18:00',
      daysWeek: [1, 2, 3]
    })
  })
})

describe('updateFormDataInput', () => {
  it('should convert Partial<IAlarmData> to AlarmFormData', () => {
    const data = {
      id: 1,
      name: 'Alarm',
      categoryId: 5,
      description: 'Desc',
      status: true,
      account: { id: 2, name: 'Account' }
    }
    const result = updateFormDataInput(data)
    expect(result).toEqual({
      id: 1,
      name: 'Alarm',
      categoryId: '5',
      description: 'Desc',
      status: true,
      account: [{ id: 2, name: 'Account' }]
    })
  })

  it('should handle missing fields with defaults', () => {
    const data = {}
    const result = updateFormDataInput(data)
    expect(result).toEqual({
      id: null,
      name: '',
      categoryId: '',
      description: undefined,
      status: false,
      account: []
    })
  })
})
