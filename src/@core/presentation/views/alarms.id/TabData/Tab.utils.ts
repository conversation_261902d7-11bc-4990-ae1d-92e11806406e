import IAlarm from '@/@core/domain/Alarm'
import { IAlarmData } from '../page.types'
import { AlarmFormData, FormDataSend } from './Tab.types'

export const parseAlarmDataInput = (data: IAlarm): IAlarmData => {
  return {
    id: data.id,
    name: data.name,
    description: data.description,
    status: data.status,
    account: data.account,
    category: data.category,
    categoryId: data.category.id,
    daysRetention: data.daysRetention,
    timeConfirmation: data.timeConfirmation,
    initialHour: data.initialHour,
    finalHour: data.finalHour,
    daysWeek: data.daysWeek
  }
}

export const updateFormDataInput = (
  data: Partial<IAlarmData>
): AlarmFormData => {
  return {
    id: data?.id ?? null,
    name: data?.name ?? '',
    categoryId: data?.categoryId ? String(data.categoryId) : '',
    description: data.description!,
    status: data?.status ?? false,
    account: data.account ? [data.account] : []
  }
}

export const formDataOutput = (
  alarmData: Partial<IAlarmData>,
  formData: AlarmFormData
): FormDataSend => {
  const merged = {
    ...alarmData,
    ...formData,
    account: formData.account[0],
    categoryId: Number(formData.categoryId)
  }

  return {
    name: merged.name,
    description: merged.description,
    timeConfirmation: merged.timeConfirmation!,
    initialHour: merged.initialHour!,
    finalHour: merged.finalHour!,
    daysWeek: merged.daysWeek!,
    daysRetention: merged.daysRetention!,
    status: merged.status,
    accountId: merged.account?.id,
    categoryId: merged.categoryId
  }
}
