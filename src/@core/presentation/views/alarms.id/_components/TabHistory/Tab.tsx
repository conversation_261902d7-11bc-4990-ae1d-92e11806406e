import { equipmentsApiV4 } from '@/@core/infra/api/EquipmentsApiV4'
import { http } from '@/@core/infra/http'
import { PageSection } from '@/@core/presentation/shared/pages'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'
import { useLanguageTabHistory } from './Tab.hooks'

export const TabHistory = () => {
  const languageTab = useLanguageTabHistory()

  return (
    <>
      <PageSection.Root className="mb-4">
        <PageSection.Content title={languageTab.title} />
      </PageSection.Root>

      <Table.Root>
        <Table.Info>
          {/* <Table.InfoTitle>{languageTab.title}</Table.InfoTitle>

          <Table.InfoBadge className="lg:mr-auto">
            total
            <span className="hidden xl:inline-block"> 0</span> 
          </Table.InfoBadge>
            */}

          <TagInput.Root className="mr-auto">
            <TagInput.Label htmlFor="status">
              {languageTab.form.input.status}
            </TagInput.Label>
            <TagInput.Content
              className="min-w-[150px]"
              name="status"
              // value={formatInputValues(formFields.values.status)}
              onChange={(values) => {
                // formFields.setValue('status', formatOutputValues(values))
              }}
              options={[
                { value: '1', label: 'Alarmes' },
                { value: '2', label: 'Todos' },
                { value: '3', label: 'normalizado' },
                { value: '4', label: 'Reconhecido' }
              ]}
              // helperText={formFields.errors.status?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>

          <TagInput.Root className="min-w-[150px]">
            <TagInput.Label htmlFor="equipment">
              {languageTab.form.input.equipment}
            </TagInput.Label>
            <TagInput.ContentApi
              name="equipment"
              // value={formatInputValues(formFields.values.equipment)}
              onChange={(values) => {
                // formFields.setValue('equipment', formatOutputValues(values))
              }}
              featchData={(p) => equipmentsApiV4(http).get({ ...p })}
              // helperText={formFields.errors.equipment?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>
        </Table.Info>

        <Table.Header>
          <Table.Row>
            <Table.Head>{languageTab.table.columns.status}</Table.Head>
            <Table.Head>{languageTab.table.columns.company}</Table.Head>
            <Table.Head>{languageTab.table.columns.equipmenet}</Table.Head>
            <Table.Head>{languageTab.table.columns.acionado}</Table.Head>
            <Table.Head>{languageTab.table.columns.normalizado}</Table.Head>
            <Table.Head>{languageTab.table.columns.actions}</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {([] as { id: number }[]).map((item) => (
            <Table.Row key={item?.id}>
              <Table.Cell></Table.Cell>
              <Table.Cell></Table.Cell>
              <Table.Cell></Table.Cell>
              <Table.Cell></Table.Cell>
              <Table.Cell></Table.Cell>
              <Table.Cell width={80} role="td-actions">
                {/* <Actions alarm={item} /> */}
              </Table.Cell>
            </Table.Row>
          ))}

          {/* <Table.RowLoading status={systemLoading.state.loading} colSpan={2} /> */}
        </Table.Body>
      </Table.Root>
    </>
  )
}
