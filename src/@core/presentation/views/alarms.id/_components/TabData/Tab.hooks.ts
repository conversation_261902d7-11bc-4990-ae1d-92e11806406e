import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { languageByMode } from '@/@core/language'

export const useLanguageTabData = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields

  const { form, title } = pages.alarmsId.tabData

  return {
    title,
    form: {
      input: {
        status: form.input.status,
        name: form.input.name,
        description: form.input.description,
        category: form.input.category,
        account: form.input.account
      },
      requiredField,
      btn: { cancel, save, confirm, add, clean }
    }
  }
}
