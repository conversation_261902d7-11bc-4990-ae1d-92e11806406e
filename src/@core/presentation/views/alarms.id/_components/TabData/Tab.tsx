import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { http } from '@/@core/infra/http'
import { PageSection } from '@/@core/presentation/shared/pages'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'

import { useLanguageTabData } from './Tab.hooks'

export const TabData = () => {
  const languageTab = useLanguageTabData()

  return (
    <>
      <PageSection.Root className="mb-4">
        <PageSection.Content title={languageTab.title} />
      </PageSection.Root>

      <form className="form-container">
        <div className="w-full grid grid-cols-1 gap-8 max-w-[1920px] mx-auto">
          <Switch.Content
            label={languageTab.form.input.status}
            labelPosition="end"
            checked={false}
            onChange={(value) => {
              //formFields.setValue('admin', value)
            }}
            className="mb-2"
            // disabled={systemLoading.state.loading}
          />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <Input.Root>
            <Input.Label htmlFor="name">
              {languageTab.form.input.name}
            </Input.Label>
            <Input.Content
              id="name"
              // value={formFields.values.name}
              onChange={(e) => {
                // formFields.setValue('name', e.target.value)
              }}
              // helperText={formFields.errors.name?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="description">
              {languageTab.form.input.description}
            </Input.Label>
            <Input.Content
              id="description"
              // value={formFields.values.description}
              onChange={(e) => {
                // formFields.setValue('description', e.target.value)
              }}
              // helperText={formFields.errors.description?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="category">
              {languageTab.form.input.category}
            </TagInput.Label>
            <TagInput.Content
              name="category"
              // value={formatInputValues(formFields.values.category)}
              onChange={(values) => {
                // formFields.setValue('category', formatOutputValues(values))
              }}
              options={[
                { value: 'operation', label: 'Operação' },
                { value: 'user', label: 'Usuário' }
              ]}
              // helperText={formFields.errors.category?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="account">
              {languageTab.form.input.account}
            </TagInput.Label>
            <TagInput.ContentApi
              name="account"
              // value={formatInputValues(formFields.values.account)}
              onChange={(values) => {
                // formFields.setValue('account', formatOutputValues(values))
              }}
              featchData={(p) =>
                companiesApiV4(http).get({ ...p, sort: 'nome' })
              }
              // helperText={formFields.errors.account?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>
        </div>

        <div className="footer-form">
          <Button
            type="button"
            // onClick={() => router.push('/users')}
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.cancel}
          </Button>
          <Button
            // type="submit"
            variant="primary"
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.save}
          </Button>
        </div>
      </form>
    </>
  )
}
