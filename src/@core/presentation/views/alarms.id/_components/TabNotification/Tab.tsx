import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { http } from '@/@core/infra/http'
import { PageSection } from '@/@core/presentation/shared/pages'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'
import { useLanguageTabNotification } from './Tab.hooks'

export const TabNotification = () => {
  const languageTab = useLanguageTabNotification()

  return (
    <>
      <PageSection.Root className="mb-4">
        <PageSection.Content title={languageTab.title} />
      </PageSection.Root>

      <form className="form-container mb-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <TagInput.Root>
            <TagInput.Label htmlFor="channel">
              {languageTab.form.input.channel}
            </TagInput.Label>
            <TagInput.Content
              name="channel"
              // value={formatInputValues(formFields.values.channel)}
              onChange={(values) => {
                // formFields.setValue('channel', formatOutputValues(values))
              }}
              options={[
                { value: 'operation', label: 'Operação' },
                { value: 'user', label: 'Usuário' }
              ]}
              // helperText={formFields.errors.channel?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="frequency">
              {languageTab.form.input.frequency}
            </TagInput.Label>
            <TagInput.Content
              name="frequency"
              // value={formatInputValues(formFields.values.frequency)}
              onChange={(values) => {
                // formFields.setValue('frequency', formatOutputValues(values))
              }}
              options={[
                { value: 'operation', label: 'Operação' },
                { value: 'user', label: 'Usuário' }
              ]}
              // helperText={formFields.errors.frequency?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="user">
              {languageTab.form.input.user}
            </TagInput.Label>
            <TagInput.ContentApi
              name="user"
              // value={formatInputValues(formFields.values.user)}
              onChange={(values) => {
                // formFields.setValue('user', formatOutputValues(values))
              }}
              featchData={(p) =>
                companiesApiV4(http).get({ ...p, sort: 'nome' })
              }
              // helperText={formFields.errors.user?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>
        </div>

        <div className="footer-form">
          <Button
            type="button"
            // onClick={() => router.push('/users')}
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.cancel}
          </Button>
          <Button
            // type="submit"
            variant="primary"
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.save}
          </Button>
        </div>
      </form>

      <Table.Root>
        <Table.Header>
          <Table.Row>
            <Table.Head>{languageTab.table.columns.channel}</Table.Head>
            <Table.Head>{languageTab.table.columns.frequency}</Table.Head>
            <Table.Head>{languageTab.table.columns.user}</Table.Head>
            <Table.Head>{languageTab.table.columns.actions}</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {(
            [] as {
              id: number
              canal: string
              frequency: string
              userName: string
            }[]
          ).map((item) => (
            <Table.Row key={item.id}>
              <Table.Cell>{item.canal}</Table.Cell>
              <Table.Cell>{item.frequency}</Table.Cell>
              <Table.Cell>{item.userName}</Table.Cell>
              <Table.Cell width={80} role="td-actions">
                {/* <Actions alarm={item} /> */}
              </Table.Cell>
            </Table.Row>
          ))}
          {/* <Table.RowLoading status={systemLoading.state.loading} colSpan={2} /> */}
        </Table.Body>
      </Table.Root>
    </>
  )
}
