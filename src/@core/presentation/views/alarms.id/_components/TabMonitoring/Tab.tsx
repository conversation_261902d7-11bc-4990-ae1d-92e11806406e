import { PageSection } from '@/@core/presentation/shared/pages'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'
import { useLanguageTabMonitoring } from './Tab.hooks'

export const TabMonitoring = () => {
  const languageTab = useLanguageTabMonitoring()

  return (
    <>
      <PageSection.Root className="mb-4">
        <PageSection.Content title={languageTab.title} />
      </PageSection.Root>

      <form className="form-container mb-4">
        <div className="flex flex-wrap items-center gap-4 mb-4">
          <Switch.Content
            label={languageTab.form.input.days}
            labelPosition="start"
            checked={false}
            onChange={(value) => {
              //formFields.setValue('admin', value)
            }}
            className="mb-2"
            // disabled={systemLoading.state.loading}
          />
          <Switch.Content
            label={languageTab.form.input.days}
            labelPosition="start"
            checked={false}
            onChange={(value) => {
              //formFields.setValue('admin', value)
            }}
            className="mb-2"
            // disabled={systemLoading.state.loading}
          />
          <Switch.Content
            label={languageTab.form.input.days}
            labelPosition="start"
            checked={false}
            onChange={(value) => {
              //formFields.setValue('admin', value)
            }}
            className="mb-2"
            // disabled={systemLoading.state.loading}
          />
          <Switch.Content
            label={languageTab.form.input.days}
            labelPosition="start"
            checked={false}
            onChange={(value) => {
              //formFields.setValue('admin', value)
            }}
            className="mb-2"
            // disabled={systemLoading.state.loading}
          />
          <Switch.Content
            label={languageTab.form.input.days}
            labelPosition="start"
            checked={false}
            onChange={(value) => {
              //formFields.setValue('admin', value)
            }}
            className="mb-2"
            // disabled={systemLoading.state.loading}
          />
          <Switch.Content
            label={languageTab.form.input.days}
            labelPosition="start"
            checked={false}
            onChange={(value) => {
              //formFields.setValue('admin', value)
            }}
            className="mb-2"
            // disabled={systemLoading.state.loading}
          />
          <Switch.Content
            label={languageTab.form.input.days}
            labelPosition="start"
            checked={false}
            onChange={(value) => {
              //formFields.setValue('admin', value)
            }}
            className="mb-2"
            // disabled={systemLoading.state.loading}
          />
          <Switch.Content
            label={languageTab.form.input.days}
            labelPosition="start"
            checked={false}
            onChange={(value) => {
              //formFields.setValue('admin', value)
            }}
            className="mb-2"
            // disabled={systemLoading.state.loading}
          />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <Input.Root>
            <Input.Label htmlFor="initialHour">
              {languageTab.form.input.initialHour}
            </Input.Label>
            <Input.ContentTimeDate
              // value={formFields.values.initialHour}
              onChange={(e) => {
                // formFields.setValue('initialHour', e.target.value)
              }}
              // helperText={formFields.errors.initialHour?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="fimHour">
              {languageTab.form.input.fimHour}
            </Input.Label>
            <Input.ContentTimeDate
              // value={formFields.values.fimHour}
              onChange={(e) => {
                // formFields.setValue('fimHour', e.target.value)
              }}
              // helperText={formFields.errors.fimHour?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="setting">
              {languageTab.form.input.setting}
            </Input.Label>
            <Input.Content
              // value={formFields.values.setting}
              onChange={(e) => {
                // formFields.setValue('setting', e.target.value)
              }}
              // helperText={formFields.errors.setting?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="daysRetention">
              {languageTab.form.input.daysRetention}
            </Input.Label>
            <Input.Content
              // value={formFields.values.daysRetention}
              onChange={(e) => {
                // formFields.setValue('daysRetention', e.target.value)
              }}
              // helperText={formFields.errors.daysRetention?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>
        </div>

        <div className="footer-form">
          <Button
            type="button"
            // onClick={() => router.push('/users')}
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.cancel}
          </Button>
          <Button
            // type="submit"
            variant="primary"
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.save}
          </Button>
        </div>
      </form>
    </>
  )
}
