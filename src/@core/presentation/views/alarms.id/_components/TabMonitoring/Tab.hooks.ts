import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { languageByMode } from '@/@core/language'

export const useLanguageTabMonitoring = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields

  const { form, title } = pages.alarmsId.tabMonitoring

  return {
    title,
    form: {
      input: {
        days: form.input.days,
        initialHour: form.input.initialHour,
        fimHour: form.input.fimHour,
        setting: form.input.config,
        daysRetention: form.input.daysRetention
      },
      requiredField,
      btn: { cancel, save, confirm, add, clean }
    }
  }
}
