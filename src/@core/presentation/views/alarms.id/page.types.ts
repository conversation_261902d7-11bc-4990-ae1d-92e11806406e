import { AlarmFormData } from './TabData/Tab.types'
import { AlarmMonitoringFormData } from './TabMonitoring/Tab.types'

export type IAlarmData = {
  id: number | null
  name: string
  description: string
  status: boolean
  account: {
    id: number
    name: string
  } | null
  category: {
    id: number
    name: string
  } | null
  categoryId: number
  timeConfirmation: number
  initialHour: string
  finalHour: string
  daysWeek: number[]
  daysRetention: number
}

export interface IStateData {
  tab: {
    active: string
    mounted: Record<string, boolean>
  }
  alarmData: Partial<IAlarmData>
  tabData: {
    alarm: Partial<AlarmFormData>
  }
  tabRules: {}
  tabMonitoring: {
    monitoring: Partial<AlarmMonitoringFormData>
  }
  tabNotification: {}
  tabHistory: {}
}
export interface IState extends IStateData {
  set: (p: Partial<IStateData>) => void
  reset: () => void
  setTab: (p: Partial<IStateData['tab']['active']>) => void
  setAlarmData: (p: Partial<IStateData['alarmData']>) => void
  setTabData: (p: Partial<IStateData['tabData']>) => void
  setTabMonitoring: (p: Partial<IStateData['tabMonitoring']>) => void
  setTabMounted: (p: string) => void
}
