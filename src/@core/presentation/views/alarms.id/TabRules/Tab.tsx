import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { http } from '@/@core/infra/http'
import { PageSection } from '@/@core/presentation/shared/pages'
import { Button } from '@/@core/presentation/shared/ui/button'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'

import { Input } from '@/@core/presentation/shared/ui/input'
import { useLanguageTabRules } from './Tab.hooks'

export const TabRules = () => {
  const languageTab = useLanguageTabRules()

  return (
    <>
      <PageSection.Root className="mb-4">
        <PageSection.Content title={languageTab.form.target.title} />
      </PageSection.Root>

      <form className="form-container mb-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <TagInput.Root>
            <TagInput.Label htmlFor="type">
              {languageTab.form.input.type}
            </TagInput.Label>
            <TagInput.Content
              name="type"
              // value={formatInputValues(formFields.values.type)}
              onChange={(values) => {
                // formFields.setValue('type', formatOutputValues(values))
              }}
              options={[
                { value: 'operation', label: 'Operação' },
                { value: 'user', label: 'Usuário' }
              ]}
              // helperText={formFields.errors.category?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="target">
              {languageTab.form.input.target}
            </TagInput.Label>
            <TagInput.ContentApi
              name="target"
              // value={formatInputValues(formFields.values.target)}
              onChange={(values) => {
                // formFields.setValue('target', formatOutputValues(values))
              }}
              featchData={(p) =>
                companiesApiV4(http).get({ ...p, sort: 'nome' })
              }
              // helperText={formFields.errors.target?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>
        </div>

        <div className="footer-form">
          <Button
            // type="submit"
            variant="primary"
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.save}
          </Button>
        </div>
      </form>

      <PageSection.Root className="mb-4">
        <PageSection.Content title={languageTab.form.shot.title} />
      </PageSection.Root>

      <form className="form-container mb-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <TagInput.Root>
            <TagInput.Label htmlFor="rule">
              {languageTab.form.input.rule}
            </TagInput.Label>
            <TagInput.Content
              name="rule"
              // value={formatInputValues(formFields.values.rule)}
              onChange={(values) => {
                // formFields.setValue('rule', formatOutputValues(values))
              }}
              options={[
                { value: 'operation', label: 'Operação' },
                { value: 'user', label: 'Usuário' }
              ]}
              // helperText={formFields.errors.rule?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>

          <Input.Root>
            <Input.Label htmlFor="processedData">
              {languageTab.form.input.processedData}
            </Input.Label>
            <Input.Content
              id="processedData"
              // value={formFields.values.processedData}
              onChange={(e) => {
                // formFields.setValue('processedData', e.target.value)
              }}
              // helperText={formFields.errors.processedData?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="logic">
              {languageTab.form.input.logic}
            </TagInput.Label>
            <TagInput.Content
              name="logic"
              // value={formatInputValues(formFields.values.logic)}
              onChange={(values) => {
                // formFields.setValue('logic', formatOutputValues(values))
              }}
              options={[
                { value: 'operation', label: 'Operação' },
                { value: 'user', label: 'Usuário' }
              ]}
              // helperText={formFields.errors.logic?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>

          <Input.Root>
            <Input.Label htmlFor="value">
              {languageTab.form.input.value}
            </Input.Label>
            <Input.Content
              id="value"
              // value={formFields.values.value}
              onChange={(e) => {
                // formFields.setValue('value', e.target.value)
              }}
              // helperText={formFields.errors.value?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>
        </div>

        <div className="footer-form">
          <Button
            type="button"
            // onClick={() => router.push('/users')}
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.cancel}
          </Button>
          <Button
            // type="submit"
            variant="primary"
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.save}
          </Button>
        </div>
      </form>

      <PageSection.Root className="mb-4">
        <PageSection.Content title={languageTab.form.normalized.title} />
      </PageSection.Root>

      <form className="form-container mb-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <TagInput.Root>
            <TagInput.Label htmlFor="rule">
              {languageTab.form.input.rule}
            </TagInput.Label>
            <TagInput.Content
              name="rule"
              // value={formatInputValues(formFields.values.rule)}
              onChange={(values) => {
                // formFields.setValue('rule', formatOutputValues(values))
              }}
              options={[
                { value: 'operation', label: 'Operação' },
                { value: 'user', label: 'Usuário' }
              ]}
              // helperText={formFields.errors.rule?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>

          <Input.Root>
            <Input.Label htmlFor="processedData">
              {languageTab.form.input.processedData}
            </Input.Label>
            <Input.Content
              id="processedData"
              // value={formFields.values.processedData}
              onChange={(e) => {
                // formFields.setValue('processedData', e.target.value)
              }}
              // helperText={formFields.errors.processedData?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="logic">
              {languageTab.form.input.logic}
            </TagInput.Label>
            <TagInput.Content
              name="logic"
              // value={formatInputValues(formFields.values.logic)}
              onChange={(values) => {
                // formFields.setValue('logic', formatOutputValues(values))
              }}
              options={[
                { value: 'operation', label: 'Operação' },
                { value: 'user', label: 'Usuário' }
              ]}
              // helperText={formFields.errors.logic?.message}
              // disabled={!isSuperAdmin || systemLoading.state.loading}
            />
          </TagInput.Root>

          <Input.Root>
            <Input.Label htmlFor="value">
              {languageTab.form.input.value}
            </Input.Label>
            <Input.Content
              id="value"
              // value={formFields.values.value}
              onChange={(e) => {
                // formFields.setValue('value', e.target.value)
              }}
              // helperText={formFields.errors.value?.message}
              // disabled={systemLoading.state.loading || isEditUserComerc}
            />
          </Input.Root>
        </div>

        <div className="footer-form">
          <Button
            type="button"
            // onClick={() => router.push('/users')}
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.cancel}
          </Button>
          <Button
            // type="submit"
            variant="primary"
            // disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.save}
          </Button>
        </div>
      </form>
    </>
  )
}
