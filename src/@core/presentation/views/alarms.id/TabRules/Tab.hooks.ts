import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import { languageByMode } from '@/@core/language'

export const useLanguageTabRules = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields

  const { form, title } = pages.alarmsId.tabRules

  return {
    title,
    form: {
      ...form,
      input: {
        type: form.input.type,
        target: form.input.target,
        rule: form.input.rule,
        ruleInfo: form.input.ruleInfo,
        logic: form.input.logic,
        value: form.input.value,
        processedData: form.input.processedData
      },
      requiredField,
      btn: { cancel, save, confirm, add, clean }
    }
  }
}
