import { useRouter } from 'next/router'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { useLog } from '@/@core/logging/logger'
import { useLanguageAlarmesIdPage, useStateAlarmesIdPage } from '../page.hooks'

import { alarmsApiV4 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import loggerRequest from '@/@core/logging/loggerRequest'
import { parseAlarmDataInput } from '../TabData/Tab.utils'
import { AlarmMonitoringFormData } from './Tab.types'
import { formMonitoringOutput } from './Tab.utils'

export const useMethodTabMonitoring = () => {
  const router = useRouter()
  const log = useLog()

  const statePage = useStateAlarmesIdPage()
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const languagePage = useLanguageAlarmesIdPage()

  const onSubmit = async (data: AlarmMonitoringFormData) => {
    if (!statePage.isEdit) {
      statePage.setTabMonitoring({
        monitoring: {
          ...statePage.tabData.alarm,
          ...data
        }
      })

      statePage.setTab('notification')
      return
    }
    try {
      systemLoading.setData({ pageLoading: true })

      const result = await alarmsApiV4(http).update(
        Number(router.query?.id),
        formMonitoringOutput(statePage.alarmData, data)
      )

      systemToast.addToast({
        message: languagePage.tabData.formMessages.updateSuccessMessage
      })

      statePage.setAlarmData({
        ...parseAlarmDataInput(result.data)
      })
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: 'alarms.id/tabMonitoring/useMethodTabMonitoring/onSubmit'
      })

      systemToast.addToast({
        message: languagePage.tabData.formMessages.updateErrorMessage
      })
    } finally {
      systemLoading.setData({ pageLoading: false })
    }
  }
  return { onSubmit }
}

export const useLanguageTabMonitoring = () => {
  const lang = useSystemLanguageStore().state.lang

  const { validationFields, pages, btn } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields

  const { form, title } = pages.alarmsId.tabMonitoring

  return {
    title,
    form: {
      daysWeek: form.daysWeek,
      input: {
        days: form.input.days,
        initialHour: form.input.initialHour,
        fimHour: form.input.fimHour,
        preActivation: form.input.preActivation,
        daysRetention: form.input.daysRetention,
        daysWeekWarning: form.input.daysWeekWarning
      },
      requiredField,
      btn: { cancel, save, confirm, add, clean }
    }
  }
}
