import { IAlarmData } from '../page.types'
import { FormDataSend } from '../TabData/Tab.types'
import { AlarmMonitoringFormData } from './Tab.types'

export const updateMonitoringInputFormat = (
  data: Partial<IAlarmData>
): AlarmMonitoringFormData => {
  return {
    id: data?.id ?? null,
    timeConfirmation: data?.timeConfirmation
      ? Number(data.timeConfirmation)
      : 0,
    initialHour: data?.initialHour ? String(data.initialHour) : '',
    finalHour: data?.finalHour ? String(data.finalHour) : '',
    daysWeek: data?.daysWeek ? data.daysWeek.map(String) : [],
    daysRetention: data?.daysRetention ? Number(data.daysRetention) : 0
  }
}

export const formMonitoringOutput = (
  alarmData: Partial<IAlarmData>,
  formMonitoring: AlarmMonitoringFormData
): FormDataSend => {
  const merged = {
    ...alarmData,
    ...formMonitoring,
    account: alarmData.account!,
    categoryId: Number(alarmData.categoryId),
    daysWeek: formMonitoring.daysWeek.map(Number)
  }

  return {
    name: merged.name!,
    description: merged.description!,
    timeConfirmation: merged.timeConfirmation,
    initialHour: merged.initialHour,
    finalHour: merged.finalHour,
    daysWeek: merged.daysWeek,
    daysRetention: merged.daysRetention,
    status: merged.status!,
    accountId: merged.account?.id,
    categoryId: merged.categoryId
  }
}
