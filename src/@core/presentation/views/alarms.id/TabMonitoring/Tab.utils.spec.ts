import { formMonitoringOutput, updateMonitoringInputFormat } from './Tab.utils'

describe('updateMonitoringInputFormat', () => {
  it('should convert full IAlarmData to AlarmMonitoringFormData', () => {
    const input = {
      id: 1,
      timeConfirmation: 30,
      initialHour: '08:00',
      finalHour: '18:00',
      daysWeek: [1, 2, 3],
      daysRetention: 7
    }
    expect(updateMonitoringInputFormat(input)).toEqual({
      id: 1,
      timeConfirmation: 30,
      initialHour: '08:00',
      finalHour: '18:00',
      daysWeek: ['1', '2', '3'],
      daysRetention: 7
    })
  })

  it('should handle missing fields and use defaults', () => {
    const input = {}
    expect(updateMonitoringInputFormat(input)).toEqual({
      id: null,
      timeConfirmation: 0,
      initialHour: '',
      finalHour: '',
      daysWeek: [],
      daysRetention: 0
    })
  })

  it('should handle partial data', () => {
    const input = {
      id: 5,
      daysWeek: [0, 6]
    }
    expect(updateMonitoringInputFormat(input)).toEqual({
      id: 5,
      timeConfirmation: 0,
      initialHour: '',
      finalHour: '',
      daysWeek: ['0', '6'],
      daysRetention: 0
    })
  })
})

describe('formMonitoringOutput', () => {
  it('should merge alarmData and formMonitoring into FormDataSend', () => {
    const alarmData = {
      name: 'Alarm 1',
      description: 'Test alarm',
      status: true,
      account: { id: 10, name: 'Account A' },
      categoryId: 2
    }
    const formMonitoring = {
      id: 1,
      timeConfirmation: 15,
      initialHour: '09:00',
      finalHour: '17:00',
      daysWeek: ['1', '3', '5'],
      daysRetention: 5
    }
    expect(formMonitoringOutput(alarmData, formMonitoring)).toEqual({
      name: 'Alarm 1',
      description: 'Test alarm',
      timeConfirmation: 15,
      initialHour: '09:00',
      finalHour: '17:00',
      daysWeek: [1, 3, 5],
      daysRetention: 5,
      status: true,
      accountId: 10,
      categoryId: 2
    })
  })

  it('should handle missing optional fields and empty daysWeek', () => {
    const alarmData = {
      name: 'Alarm 2',
      description: 'Desc',
      status: false,
      account: { id: 20, name: 'Account B' },
      categoryId: 3
    }
    const formMonitoring = {
      id: null,
      timeConfirmation: 0,
      initialHour: '',
      finalHour: '',
      daysWeek: [],
      daysRetention: 0
    }
    expect(formMonitoringOutput(alarmData, formMonitoring)).toEqual({
      name: 'Alarm 2',
      description: 'Desc',
      timeConfirmation: 0,
      initialHour: '',
      finalHour: '',
      daysWeek: [],
      daysRetention: 0,
      status: false,
      accountId: 20,
      categoryId: 3
    })
  })

  it('should convert categoryId to number and daysWeek to numbers', () => {
    const alarmData = {
      name: 'Alarm 3',
      description: 'Desc3',
      status: true,
      account: { id: 30, name: 'Account C' },
      categoryId: '4' as unknown as number
    }
    const formMonitoring = {
      id: 3,
      timeConfirmation: 20,
      initialHour: '10:00',
      finalHour: '20:00',
      daysWeek: ['2', '4'],
      daysRetention: 10
    }
    expect(formMonitoringOutput(alarmData, formMonitoring)).toEqual({
      name: 'Alarm 3',
      description: 'Desc3',
      timeConfirmation: 20,
      initialHour: '10:00',
      finalHour: '20:00',
      daysWeek: [2, 4],
      daysRetention: 10,
      status: true,
      accountId: 30,
      categoryId: 4
    })
  })
})
