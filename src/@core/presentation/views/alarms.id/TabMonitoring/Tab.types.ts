export type AlarmMonitoringFormData = {
  id: number | null
  timeConfirmation: number
  initialHour: string
  finalHour: string
  daysWeek: string[]
  daysRetention: number
}

export type MonitoringFormDataSend = {
  name: string
  description: string
  timeConfirmation: number
  initialHour: string
  finalHour: string
  daysWeek: number[]
  daysRetention: number
  status: boolean
  accountId: number
  categoryId: number
}
