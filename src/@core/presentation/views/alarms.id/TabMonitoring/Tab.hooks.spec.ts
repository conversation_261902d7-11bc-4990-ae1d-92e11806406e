import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { cleanup, renderHook, waitFor } from '@testing-library/react'

import { appCookie } from '@/@core/infra/memory/cookie'
import { alarmMock1 } from '@/__mock__/content/api-alarms.content'

import { useStateAlarmesIdPage } from '../page.hooks'
import { useLanguageTabMonitoring, useMethodTabMonitoring } from './Tab.hooks'

cleanup()

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/AlarmsApiV4/AlarmsApiV4')
const spyAlarmsApiV4 = jest.spyOn(
  require('@/@core/infra/api/AlarmsApiV4/AlarmsApiV4'),
  'alarmsApiV4'
)

describe('@core/presentation/views/alarms.id/TabMonitoring/Tab.hooks', () => {
  beforeEach(() => {
    appCookie.init()

    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      push: jest.fn(),
      query: {
        id: 123
      }
    }))
  })

  it('should check return the function onSubmit', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodTabMonitoring(),
        state: useStateAlarmesIdPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(() => result.current.state.setAlarmData({ id: 56 }))

    await waitFor(async () => {
      await result.current.method.onSubmit({
        id: 56,
        timeConfirmation: 10,
        initialHour: '08:00',
        finalHour: '18:00',
        daysWeek: ['1', '2', '3'],
        daysRetention: 5
      })
    })

    await waitFor(() => result.current.state.reset())

    /* request success **/
    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 200,
        data: alarmMock1
      })
    }))

    await waitFor(() => result.current.state.setAlarmData({ id: 56 }))

    await waitFor(async () => {
      await result.current.method.onSubmit({
        id: 56,
        timeConfirmation: 10,
        initialHour: '08:00',
        finalHour: '18:00',
        daysWeek: ['1', '2', '3'],
        daysRetention: 5
      })
    })
    expect(result.current.state.alarmData.id).toBe(alarmMock1.id)
    expect(result.current.state.alarmData.daysRetention).toBe(
      alarmMock1.daysRetention
    )

    await waitFor(() => result.current.state.reset())

    spyAlarmsApiV4.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({
        status: 200,
        data: alarmMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.onSubmit({
        id: 56,
        timeConfirmation: 10,
        initialHour: '08:00',
        finalHour: '18:00',
        daysWeek: ['0', '1', '2', '3', '4', '5', '6'],
        daysRetention: 1
      })
    })

    expect(result.current.state.tabMonitoring.monitoring.id).toBe(alarmMock1.id)
    expect(result.current.state.tabMonitoring.monitoring.daysRetention).toBe(
      alarmMock1.daysRetention
    )
  })
})

describe('src/@core/presentation/views/alarms.id/_components/TabData/Tab.hooks.spec.ts | useLanguageTabDat', () => {
  it('check the form texts', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabMonitoring()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    expect(result.current.language.form.input).toEqual({
      days: 'Todos',
      daysRetention: 'Dias de retenção do log',
      daysWeekWarning: 'Selecione um dia da semana no mínimo.',
      fimHour: 'Horário de fim',
      initialHour: 'Horário de início',
      preActivation: 'Pré acionamento'
    })
  })
})
