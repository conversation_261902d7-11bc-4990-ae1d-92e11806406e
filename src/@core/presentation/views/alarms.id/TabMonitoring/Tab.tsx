import { PageSection } from '@/@core/presentation/shared/pages'

import { useLanguageTabMonitoring } from './Tab.hooks'
import { SectionMonitoring } from './_components/SectionMonitoring'

export const TabMonitoring = () => {
  const languageTab = useLanguageTabMonitoring()

  return (
    <>
      <PageSection.Root className="mb-8 flex justify-center items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 w-full gap-4 max-w-[1920px]">
          <div className="col-span-2 space-y-[8px]">
            <PageSection.Content title={languageTab.title} />
          </div>
        </div>
      </PageSection.Root>
      <SectionMonitoring />
    </>
  )
}
