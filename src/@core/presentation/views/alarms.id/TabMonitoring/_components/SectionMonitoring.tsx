import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { useStateAlarmesIdPage } from '../../page.hooks'
import { useLanguageTabMonitoring, useMethodTabMonitoring } from '../Tab.hooks'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { AlarmMonitoringFormData } from '../Tab.types'
import { updateMonitoringInputFormat } from '../Tab.utils'

import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'

export const SectionMonitoring = () => {
  const languageTab = useLanguageTabMonitoring()
  const statePage = useStateAlarmesIdPage()
  const formFields = useFormFields()
  const systemLoading = useSystemLoadingStore()
  const methodTabData = useMethodTabMonitoring()
  const router = useRouter()

  useEffect(() => {
    if (
      !statePage.isEdit ||
      Object.keys(statePage.tabMonitoring.monitoring).length !== 0
    ) {
      formFields.setValues(statePage.tabMonitoring.monitoring)
    }
  }, [statePage.tabMonitoring.monitoring])

  useEffect(() => {
    if (statePage.isEdit || Object.keys(statePage.alarmData).length !== 0) {
      formFields.setValues(updateMonitoringInputFormat(statePage.alarmData))
    }
  }, [statePage.alarmData])

  const daysOfWeek = [
    { label: languageTab.form.daysWeek.sunday, value: '0' },
    { label: languageTab.form.daysWeek.monday, value: '1' },
    { label: languageTab.form.daysWeek.tuesday, value: '2' },
    { label: languageTab.form.daysWeek.wednesday, value: '3' },
    { label: languageTab.form.daysWeek.thursday, value: '4' },
    { label: languageTab.form.daysWeek.friday, value: '5' },
    { label: languageTab.form.daysWeek.saturday, value: '6' }
  ]

  const allSelected = formFields.values.daysWeek.length === daysOfWeek.length

  return (
    <>
      <form
        className="form-container mb-4"
        onSubmit={formFields.handleSubmit(() => {
          methodTabData.onSubmit(formFields.values)
        })}
      >
        {!formFields.values.daysWeek.length && (
          <div
            className={cn('flex items-center gap-2 border rounded-xl p-3 mt-3')}
          >
            <Badge.Root variant="alert">
              <Badge.Content>
                {languageTab.form.input.daysWeekWarning}
              </Badge.Content>
            </Badge.Root>
          </div>
        )}

        <div className="flex flex-wrap items-center gap-4 mb-4">
          <div className="border-r p-4">
            <Switch.Content
              label={languageTab.form.daysWeek.all}
              labelPosition="start"
              checked={allSelected}
              onChange={(value) => {
                if (value)
                  formFields.setValue(
                    'daysWeek',
                    daysOfWeek.map((d) => d.value)
                  )
                if (!value) formFields.setValue('daysWeek', [])
              }}
              className="mb-2"
              disabled={systemLoading.state.loading}
            />
          </div>
          {daysOfWeek.map((day) => (
            <Switch.Content
              key={day.value}
              label={day.label}
              labelPosition="start"
              checked={formFields.values.daysWeek.includes(day.value)}
              onChange={(value) => {
                const current = formFields.values.daysWeek
                if (value)
                  formFields.setValue('daysWeek', [...current, day.value])
                if (!value)
                  formFields.setValue(
                    'daysWeek',
                    current.filter((d) => d !== day.value)
                  )
              }}
              className="mb-2"
              disabled={systemLoading.state.loading}
            />
          ))}
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
          <Input.Root>
            <Input.Label htmlFor="initialHour">
              {languageTab.form.input.initialHour}
            </Input.Label>
            <Input.ContentTime
              className="w-full"
              value={formFields.values.initialHour ?? ''}
              onChange={(e) => {
                formFields.setValue('initialHour', e ? e.format('HH:mm') : '')
              }}
              views={['hours', 'minutes']}
              helperErrorText={formFields.errors.initialHour?.message}
              disabled={systemLoading.state.loading}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="fimHour">
              {languageTab.form.input.fimHour}
            </Input.Label>
            <Input.ContentTime
              className="w-full"
              value={formFields.values.finalHour ?? ''}
              onChange={(e) => {
                formFields.setValue('finalHour', e ? e.format('HH:mm') : '')
              }}
              views={['hours', 'minutes']}
              helperErrorText={formFields.errors.finalHour?.message}
              disabled={systemLoading.state.loading}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="preActivation">
              {languageTab.form.input.preActivation}
            </Input.Label>
            <Input.Content
              type="number"
              min={0}
              value={formFields.values.timeConfirmation}
              onChange={(e) => {
                formFields.setValue('timeConfirmation', Number(e.target.value))
              }}
              helperText={formFields.errors.timeConfirmation?.message}
              disabled={systemLoading.state.loading}
            />
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="daysRetention">
              {languageTab.form.input.daysRetention}
            </Input.Label>
            <Input.Content
              type="number"
              min={0}
              value={formFields.values.daysRetention}
              onChange={(e) => {
                formFields.setValue('daysRetention', Number(e.target.value))
              }}
              helperText={formFields.errors.daysRetention?.message}
              disabled={systemLoading.state.loading}
            />
          </Input.Root>
        </div>

        <div className="footer-form">
          <Button
            type="button"
            onClick={() => router.push('/alarms')}
            disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.cancel}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={systemLoading.state.loading}
          >
            {languageTab.form.btn.save}
          </Button>
        </div>
      </form>
    </>
  )
}

const useFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabMonitoring()

  const formSchema = z
    .object({
      id: z.union([z.number(), z.null()]),
      timeConfirmation: z.number(),
      initialHour: z.string(),
      finalHour: z.string(),
      daysWeek: z.array(z.string()),
      daysRetention: z.number()
    })
    .superRefine((values, ctx) => {
      if (!values.initialHour || values.initialHour.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: message,
          path: ['initialHour']
        })
      }
      if (!values.finalHour || values.finalHour.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: message,
          path: ['finalHour']
        })
      }
      if (values.daysWeek.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: message,
          path: ['daysWeek']
        })
      }
    })

  type FormSchema = z.infer<typeof formSchema>

  const parseInitialData = (
    data: Partial<AlarmMonitoringFormData>
  ): FormSchema => {
    return {
      id: data.id ?? null,
      timeConfirmation: data.timeConfirmation ?? 0,
      initialHour: data.initialHour ?? '',
      finalHour: data.finalHour ?? '',
      daysWeek: data.daysWeek ? data.daysWeek.map(String) : [],
      daysRetention: data.daysRetention ?? 0
    }
  }

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const setValues = (data: Partial<AlarmMonitoringFormData>) => {
    const dataParsed = parseInitialData(data)

    setValue('id', dataParsed.id)
    setValue('timeConfirmation', dataParsed.timeConfirmation)
    setValue('initialHour', dataParsed.initialHour)
    setValue('finalHour', dataParsed.finalHour)
    setValue('daysWeek', dataParsed.daysWeek)
    setValue('daysRetention', dataParsed.daysRetention)
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors }
}
