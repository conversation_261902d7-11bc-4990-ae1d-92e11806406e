import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { cleanup, renderHook, waitFor } from '@testing-library/react'

import { useLanguageAlarmesIdPage, useStateAlarmesIdPage } from './page.hooks'

import { observer } from '@/@core/domain/observer'
import { appCookie } from '@/@core/infra/memory/cookie'
import { AlarmFormDataMock } from '@/__mock__/content/api-alarms.content'

cleanup()

describe('src/@core/presentation/views/alarms.id/page | usePageLanguage', () => {
  it('check de page title', () => {
    const { result } = renderHook(() => useLanguageAlarmesIdPage(), {
      wrapper: AppStoreProvider
    })

    expect(result.current.page.title).toBe('Alarme')
  })
})

describe('src/@core/presentation/views/alarms.id/page | useStatePage', () => {
  beforeEach(() => {
    appCookie.init()
    observer.reset()
  })

  it('should exec method set and reset', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmesIdPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.set({
        tabData: {
          alarm: { ...AlarmFormDataMock }
        }
      })
    })
  })

  it('should exec method setTab and setTabMounted', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmesIdPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => {
      result.current.state.setTab('data')
    })

    expect(result.current.state.tab.active).toBe('data')

    await waitFor(() => {
      result.current.state.setTabMounted('tabData')
    })

    expect(!!result.current.state.tab.mounted?.tabData).toBeTruthy()

    await waitFor(() => result.current.state.reset())
  })

  it('should exec method setAlarmData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmesIdPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.setAlarmData({
        id: 123
      })

      expect(result.current.state.isEdit).toBeTruthy()
    })
  })

  it('should exec method setTabData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmesIdPage()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.setTabData({
        alarm: {
          id: 123
        }
      })

      expect(result.current.state.tabData.alarm.id).toEqual(123)
    })
  })

  it('should set initial data with set', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateAlarmesIdPage()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => result.current.state.reset())

    const initialData = {
      alarmData: { id: 999, name: 'Teste' },
      tab: { active: 'rules', mounted: { tabData: true } }
    }

    await waitFor(() => {
      result.current.state.set(initialData)
    })

    expect(result.current.state.alarmData.id).toBe(999)
    expect(result.current.state.tab.active).toBe('rules')
    expect(result.current.state.tab.mounted.tabData).toBe(true)
  })
})
