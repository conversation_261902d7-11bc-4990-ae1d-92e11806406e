import { IFormValues } from './Tab.types'
import { formOutput, formValidate, mergeFormValues } from './Tab.utils'

describe('src/@core/presentation/views/reports/TabGenerate/Tab.utils', () => {
  it('should check return the utility function formOutput', () => {
    const dataInput = {
      type_data: {
        consumption: {
          value: true,
          tariff_post: '',
          capacity: '',
          consumption: ''
        },
        demand: {
          value: false,
          tariff_post: '',
          capacity: '',
          consumption: ''
        },
        power_factor: { value: false, tariff_post: '', capacity: '' },
        access_log: { value: false, fields: [] }
      },
      entity_fields: { entity: 'account' },
      period_fields: { syntax_date: 'last_month' },
      entity_data_fields: {
        entity_data: [{ id: 9, name: 'Desenvolvimento' }],
        type_measurement: ''
      },
      entity_type_fields: { account_type: 'access_log' }
    }
    const dataOutput = {
      entity_data_fields: {
        entity_data: [9]
      },
      entity_fields: {
        entity: 'account'
      },
      entity_type_fields: {
        account_type: 'access_log'
      },
      period_fields: {
        syntax_date: 'last_month'
      },
      type_data: {
        consumption: {
          value: true
        }
      }
    }

    const result = formOutput(dataInput)

    expect(result).toEqual(dataOutput)
  })

  it('should check return the utility function formValidate', () => {
    const values = {
      entity_fields: { entity: 'account' },
      entity_type_fields: {
        type_measurement: '',
        account_type: 'access_log'
      },
      entity_data_fields: {
        entity_data: [{ id: 9, name: 'Desenvolvimento' }],
        entity_data_group: ''
      },
      period_fields: {
        initial_date: '',
        final_date: '',
        syntax_date: 'last_week'
      },
      aggregate_fields: {
        date_interval: '',
        date_interval_number: '',
        type: ''
      },
      type_data: {
        consumption: {
          value: true,
          tariff_post: '',
          capacity: '',
          consumption: ''
        },
        demand: {
          value: false,
          tariff_post: '',
          capacity: '',
          consumption: ''
        },
        power_factor: { value: false, tariff_post: '', capacity: '' },
        access_log: { value: true, fields: [] }
      }
    }
    const requiredField = 'Required field'
    const requiredFinalDate =
      'The end date field is mandatory when start date is filled in.'
    const requiredTypeData = 'The data type field is required'

    const options = { requiredField, requiredFinalDate, requiredTypeData }

    /** case error entity */
    const resultCaseEntity = formValidate(
      {
        ...values,
        entity_fields: { entity: '' }
      },
      options
    )
    expect(resultCaseEntity[0]).toBeTruthy()
    expect(resultCaseEntity[1]).toEqual({
      entity_fields: { entity: 'Required field' }
    })

    /** case error type_measurement */
    const resultCaseTypeMeasurement = formValidate(
      {
        ...values,
        entity_fields: { entity: 'company' },
        entity_type_fields: {
          type_measurement: ''
        }
      },
      options
    )
    expect(resultCaseTypeMeasurement[0]).toBeTruthy()
    expect(resultCaseTypeMeasurement[1]).toEqual({
      entity_type_fields: { type_measurement: 'Required field' }
    })

    /** case error account_type */
    const resultCaseAccountType = formValidate(
      {
        ...values,
        entity_fields: { entity: 'account' },
        entity_type_fields: {
          account_type: ''
        }
      },
      options
    )
    expect(resultCaseAccountType[0]).toBeTruthy()
    expect(resultCaseAccountType[1]).toEqual({
      entity_type_fields: { account_type: 'Required field' }
    })

    /** case error final_date */
    const resultCaseFinalDate = formValidate(
      {
        ...values,
        entity_fields: { entity: 'company' },
        entity_type_fields: {
          type_measurement: 'energy'
        },
        period_fields: {
          initial_date: '2025-01-01',
          final_date: ''
        }
      },
      options
    )
    expect(resultCaseFinalDate[0]).toBeTruthy()
    expect(resultCaseFinalDate[1]).toEqual({
      period_fields: {
        final_date:
          'The end date field is mandatory when start date is filled in.'
      }
    })

    /** case error syntax_date */
    const resultCaseFinalSyntax = formValidate(
      {
        ...values,
        entity_fields: { entity: 'company' },
        entity_type_fields: {
          type_measurement: 'energy'
        },
        period_fields: {
          initial_date: '',
          final_date: '',
          syntax_date: ''
        }
      },
      options
    )
    expect(resultCaseFinalSyntax[0]).toBeTruthy()
    expect(resultCaseFinalSyntax[1]).toEqual({
      period_fields: {
        initial_date: 'Required field'
      }
    })

    /** case error type_data */
    const resultCaseTypeData = formValidate(
      {
        ...values,
        entity_fields: { entity: 'company' },
        entity_type_fields: {
          type_measurement: 'energy'
        },
        type_data: {}
      },
      options
    )
    expect(resultCaseTypeData[0]).toBeTruthy()
    expect(resultCaseTypeData[1]).toEqual({
      type_data: {
        value: 'The data type field is required'
      }
    })

    /** case success */
    const resultCaseSuccess = formValidate(
      {
        ...values,
        entity_fields: { entity: 'company' },
        entity_type_fields: {
          type_measurement: 'energy'
        }
      },
      options
    )
    expect(resultCaseSuccess[0]).toBeFalsy()
    expect(resultCaseSuccess[1]).toEqual({})
  })

  it('should check return the utility function mergeFormValues', () => {
    const valuesPrev: IFormValues = {}
    const valuesData: IFormValues = {}

    const result = mergeFormValues(valuesPrev, valuesData)

    expect(result).toEqual({
      aggregate_fields: {},
      entity_data_fields: {},
      entity_fields: {},
      entity_type_fields: {},
      period_fields: {},
      type_data: {
        access_log: {
          fields: {}
        },
        consumption: {
          fields: {}
        },
        demand: {
          fields: {}
        },
        power_factor: {
          fields: {}
        }
      }
    })
  })
})
