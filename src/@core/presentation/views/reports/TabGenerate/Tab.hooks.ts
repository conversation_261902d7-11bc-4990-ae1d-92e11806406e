import { AxiosError } from 'axios'

import { IReport } from '@/@core/domain/Report'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import {
  reportsApiV3,
  reportsDownloadApiV3,
  reportsGeneratorApiV3
} from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { toastMessageSwitch } from '@/@core/utils/toast'
import { useStatePage } from '../page.hooks'
import { formOutput } from './Tab.utils'

export const useMethodsTabGenerate = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const statePage = useStatePage()
  const languageTab = useLanguageTabGenerate()

  const handleSubmitFilter = async (filter: object) => {
    try {
      systemLoading.setData({ loading: true })

      const { status, data } = await reportsGeneratorApiV3(http).generate(
        formOutput(filter)
      )

      if (status !== 200) {
        return
      }

      statePage.set({
        tabGenerate: {
          report: {
            ...statePage.tabGenerate.report,
            filter: filter
          },
          reportGenereted: data
        }
      })

      return {}
    } catch (error) {
      let errors: object | undefined

      const { status, response } = error as AxiosError<object>
      if (status === 422) {
        errors = response?.data
      }

      log.send(loggerRequest, {
        error: error,
        title:
          '@core/presentation/views/reports/TabGenerate/Tab.hooks/handleSubmitFilter'
      })

      return { errors }
    } finally {
      systemLoading.setData({ loading: false })
    }
  }

  const handleDownload = async (payload: object) => {
    try {
      systemLoading.setData({ loading: true })

      const { status, data } = await reportsDownloadApiV3(http).download(
        payload
      )
      if (status !== 200) return
      systemToast.addToast({
        message: data,
        type: 'success'
      })
    } catch (error) {
      systemToast.addToast({
        message: languageTab.form.errors.request,
        type: 'error'
      })

      log.send(loggerRequest, {
        error: error,
        title:
          '@core/presentation/views/reports/TabGenerate/Tab.hooks/handleDownload'
      })
    } finally {
      systemLoading.setData({ loading: false })
    }
  }

  const handleSaveFilter = async (reportId: number | null, payload: object) => {
    const toast = {
      message: '',
      type: 'error' as 'success' | 'error'
    }

    const {
      tabGenerate: { report }
    } = statePage

    try {
      systemLoading.setData({ loading: true })

      const { status, data }: { status: number; data: IReport | null } =
        reportId
          ? await reportsApiV3(http).update(reportId, payload)
          : await reportsApiV3(http).create(payload)

      const conditionalRequest = [201, 200].includes(status)

      toast.type = 'success'
      toast.message = toastMessageSwitch(
        languageTab.modalSave.form.messages,
        reportId,
        conditionalRequest
      )

      if (conditionalRequest && data)
        statePage.set({
          tabGenerate: {
            ...statePage.tabGenerate,
            report: {
              ...report,
              id: data.id,
              name: data.name,
              updatedAt: data?.updatedAt
            }
          }
        })

      return { status: conditionalRequest }
    } catch (error) {
      toast.type = 'error'
      toast.message = toastMessageSwitch(
        languageTab.modalSave.form.messages,
        report.id
      )

      log.send(loggerRequest, {
        error: error,
        title:
          '@core/presentation/views/reports/TabGenerate/Tab.hooks/handleDownload'
      })

      return { status: false }
    } finally {
      systemToast.addToast({
        message: toast.message,
        type: toast.type
      })

      systemLoading.setData({ loading: false })
    }
  }

  return { handleSubmitFilter, handleDownload, handleSaveFilter }
}
export const useLanguageTabGenerate = () => {
  const { lang } = useSystemLanguageStore().state

  const {
    pages: { reports },
    validationFields: { requiredField },
    errors,
    form: { selectAnOption },
    table: { withoutData },
    btn: { cancel, save, update, clean }
  } = languageByMode(lang)

  const { tabGenerate } = reports

  return {
    form: {
      ...tabGenerate.form,
      input: {
        ...tabGenerate.form.input
      },
      requiredField,
      selectAnOption,
      errors,
      btn: { cancel, save, update, clean }
    },
    table: {
      ...tabGenerate.table,
      withoutData
    },
    modalSave: {
      ...tabGenerate.modalSave,
      btn: { cancel, save }
    }
  }
}
