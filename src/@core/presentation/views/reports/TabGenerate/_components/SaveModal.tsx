import { forwardRef, useImperativeHandle, useRef, useState } from 'react'

import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Input } from '@/@core/presentation/shared/ui/input'

import { useStatePage } from '../../page.hooks'
import { useLanguageTabGenerate, useMethodsTabGenerate } from '../Tab.hooks'
import { formOutput } from '../Tab.utils'

const SaveModal = forwardRef<Partial<IModalRootRef>, {}>((props, ref) => {
  const modalRef = useRef<IModalRootRef>(null)

  const systemStore = useSystemStore()
  const statePage = useStatePage()
  const languageTab = useLanguageTabGenerate()
  const methodsTab = useMethodsTabGenerate()

  const [reportName, setReportName] = useState<string>('')

  const saveReport = async () => {
    const { report } = statePage.tabGenerate

    const payload = {
      ...formOutput(report.filter),
      name: reportName,
      model: false
    }

    const { status } = await methodsTab.handleSaveFilter(report.id, payload)

    if (!status) return

    systemStore.setUnmountComponent('reports-tabReport')
    systemStore.setUnmountComponent('reports-tabHistorical')

    modalRef.current?.close()
  }

  useImperativeHandle(ref, () => ({
    open: () => {
      setReportName(statePage.tabGenerate.report.name)
      modalRef.current?.open()
    },
    close: () => {
      setReportName('')
      modalRef.current?.close()
    }
  }))

  return (
    <Modal.Root ref={modalRef}>
      <Modal.Title>
        {!!statePage.tabGenerate.report.id
          ? languageTab.form.btn.update
          : languageTab.form.btn.save}
      </Modal.Title>
      <Modal.Content>
        <Input.Label htmlFor="name">
          {languageTab.modalSave.form.input.name}
        </Input.Label>
        <Input.Content
          name="name"
          placeholder={languageTab.modalSave.form.input.name}
          value={reportName}
          onChange={(e) => setReportName(e.target.value)}
        />
      </Modal.Content>

      <Modal.Footer>
        <Button
          type="button"
          onClick={() => {
            modalRef.current?.close()
            setReportName('')
          }}
        >
          {languageTab.modalSave.btn.cancel}
        </Button>
        <Button type="button" variant="primary" onClick={() => saveReport()}>
          {languageTab.modalSave.btn.save}
        </Button>
      </Modal.Footer>
    </Modal.Root>
  )
})

export default SaveModal
