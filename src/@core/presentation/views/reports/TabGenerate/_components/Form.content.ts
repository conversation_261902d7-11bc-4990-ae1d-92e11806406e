import { IFormValues } from '../Tab.types'

export const entityOptions = [
  { id: 'company', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 'equipment', name: 'Equipamento' },
  { id: 'account', name: '<PERSON><PERSON>' }
]
export const measurementOptions = [
  { id: 'energy', name: 'Energia' },
  { id: 'water', name: 'Água' },
  { id: 'gas', name: '<PERSON><PERSON>' }
]
export const measurementAccountsOptions = [
  { id: 'access_log', name: 'Log de acessos' }
]
export const entityDataGroupOptions = [
  /** */
  { id: 'equipment', name: 'Equipamento' }
]
export const syntaxDateOptions = [
  { id: 'now', name: '<PERSON><PERSON><PERSON>' },
  { id: 'today', name: '<PERSON><PERSON>' },
  { id: 'yesterday', name: 'Ontem' },
  { id: 'tomorrow', name: 'Amanh<PERSON>' },
  { id: 'last_week', name: 'Semana passada' },
  { id: 'this_week', name: 'Semana atual' },
  { id: 'last_month', name: '<PERSON><PERSON><PERSON> passado' },
  { id: 'this_month', name: '<PERSON><PERSON><PERSON> atual' }
]
export const dateIntervalOptions = [
  { id: 'hours', name: '<PERSON><PERSON>' },
  { id: 'days', name: 'Dias' },
  { id: 'weeks', name: 'Semanas' },
  { id: 'months', name: 'Meses' },
  { id: 'years', name: 'Anos' }
]
export const typeOptions = [
  { id: 'first', name: 'Primeiro' },
  { id: 'last', name: 'Ultimo' },
  { id: 'min', name: 'Menor' },
  { id: 'max', name: 'Maior' },
  { id: 'sum', name: 'Somar' },
  { id: 'avg', name: 'Média' },
  { id: 'count', name: 'Quantidade' }
]
export const tariffPostOptions = [
  { id: '', name: 'Todos' },
  { id: 'true', name: 'Ponta' },
  { id: 'false', name: 'Fora ponta' }
]
export const tariffWaterPostOptions = [
  /** */
  { id: 'true', name: 'consumo' }
]
export const capacityOptions = [
  { id: '', name: 'Todos' },
  { id: 'capacitivo', name: 'Capacitivo' },
  { id: 'indutivo', name: 'Indutivo' }
]
export const capacityWaterOptions = [
  { id: 'cubic', name: 'M³' },
  { id: 'normalized', name: 'Nm³' },
  { id: 'liters', name: 'litros' }
]
export const consumptionOptions = [
  { id: 'consumo_ativo', name: 'Ativo' },
  { id: 'consumo_reativo', name: 'Reativo' }
]
export const consumptionDemandOptions = [
  { id: 'demanda_ativa', name: 'Ativa' },
  { id: 'demanda_reativa', name: 'Reativa' }
]
export const consumptionWaterOptions = [
  /** */
  { id: 'consumo', name: 'Consumo' }
]
export const consumptionGasOptions = [
  { id: 'consumo_gflow_normalized', name: 'Consumo normalizado (Nm³)' },
  { id: 'consumo_gflow', name: 'Consumo (m³)' }
]
export const formData: IFormValues = {
  entity_fields: {
    entity: ''
  },
  entity_type_fields: {
    type_measurement: '',
    account_type: ''
  },
  entity_data_fields: {
    entity_data: [],
    entity_data_group: ''
  },
  period_fields: {
    initial_date: '',
    final_date: '',
    syntax_date: ''
  },
  aggregate_fields: {
    date_interval: '',
    date_interval_number: '1',
    type: ''
  },
  type_data: {
    consumption: {
      value: true,
      fields: {
        tariff_post: '',
        capacity: '',
        greatness: '',
        consumption: ['consumo_ativo']
      }
    },
    demand: {
      value: false,
      fields: {
        tariff_post: '',
        capacity: '',
        consumption: ['demanda_ativa']
      }
    },
    power_factor: {
      value: false,
      fields: {
        tariff_post: '',
        capacity: ''
      }
    },
    access_log: {
      value: false,
      fields: {}
    }
  }
}
