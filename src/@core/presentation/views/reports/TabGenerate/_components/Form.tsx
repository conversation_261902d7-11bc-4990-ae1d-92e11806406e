import dayjs from 'dayjs'
import { useEffect, useRef, useState } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { accountsApiV3 } from '@/@core/infra/api'
import { companiesApiV4 } from '@/@core/infra/api/CompaniesApiV4'
import { equipmentsApiV4 } from '@/@core/infra/api/EquipmentsApiV4'
import { http } from '@/@core/infra/http'
import { Button } from '@/@core/presentation/shared/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/@core/presentation/shared/ui/dropdown-menu'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Switch } from '@/@core/presentation/shared/ui/switch'
import {
  formatInputValue,
  formatInputValues,
  formatOutputValues,
  TagInput
} from '@/@core/presentation/shared/ui/tagInput'
import convertAlignmentToJSON from '@/@core/utils/convertAlignmentToJSON'

import { useStatePage } from '../../page.hooks'
import { useLanguageTabGenerate, useMethodsTabGenerate } from '../Tab.hooks'
import { IFormErrors, IFormValues } from '../Tab.types'
import { formOutput, formValidate, mergeFormValues } from '../Tab.utils'
import {
  capacityOptions,
  capacityWaterOptions,
  consumptionDemandOptions,
  consumptionGasOptions,
  consumptionOptions,
  consumptionWaterOptions,
  dateIntervalOptions,
  entityDataGroupOptions,
  entityOptions,
  formData,
  measurementAccountsOptions,
  measurementOptions,
  syntaxDateOptions,
  tariffPostOptions,
  typeOptions
} from './Form.content'
import { ContextProvider, useContextForm } from './Form.context'

const Form = () => {
  const isMounted = useRef<boolean>(false)

  const [values, setValuesData] = useState<IFormValues>({ ...formData })
  const [errors, setErrors] = useState<IFormErrors>({})

  const statePage = useStatePage()
  const languageTab = useLanguageTabGenerate()
  const methodsTab = useMethodsTabGenerate()

  const scrollTop = () => {
    document
      .querySelector('[data-content]')
      ?.parentElement?.scrollTo?.({ top: 0, behavior: 'smooth' })
  }
  const setValues = (data: IFormValues) => {
    setErrors({})

    setValuesData((prev) =>
      mergeFormValues(
        JSON.parse(JSON.stringify(prev)),
        JSON.parse(JSON.stringify(data))
      )
    )
  }
  const onSubmit = async () => {
    const [hasError, formErrors] = formValidate(values, {
      requiredField: languageTab.form.requiredField,
      requiredFinalDate: languageTab.form.requiredFinalDate,
      requiredTypeData: languageTab.form.requiredTypeData
    })

    setErrors(formErrors)
    if (hasError) return

    const result = await methodsTab.handleSubmitFilter(values)

    if (!result?.errors) return

    const objErrors = convertAlignmentToJSON(result?.errors)
    setErrors?.(objErrors)
    scrollTop()
  }
  const handleDownload = async (single_sheet: boolean) => {
    const [hasError, formErrors] = formValidate(values, {
      requiredField: languageTab.form.requiredField,
      requiredFinalDate: languageTab.form.requiredFinalDate,
      requiredTypeData: languageTab.form.requiredTypeData
    })
    setErrors(formErrors)
    if (hasError) return
    const payload = formOutput(values)
    await methodsTab.handleDownload({
      ...payload,
      file_type: 'xlsx',
      single_sheet
    })
  }

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true
      setValues(statePage.tabGenerate.report.filter)
      return
    }

    return () => {
      isMounted.current = false
      // statePage.set({
      //   tabGenerate: stateData.tabGenerate
      // })
    }
  }, [])

  useEffect(() => {
    const formEl = document.querySelector(
      '[data-atribute="reportForm"] form'
    ) as HTMLFormElement

    const tableEl = document.querySelector(
      '[data-atribute="reportTable"]'
    ) as HTMLDivElement

    if (!!tableEl?.style) tableEl.style.height = `${formEl.clientHeight}px`
  }, [values])

  const showFooter =
    [
      !!values?.entity_fields?.entity,
      !!values?.entity_type_fields?.type_measurement,
      !!values?.entity_type_fields?.account_type
    ].filter(Boolean).length === 2

  return (
    <ContextProvider values={values} errors={errors} setValues={setValues}>
      <form
        onSubmit={(e) => {
          e.preventDefault()
          onSubmit()
        }}
        className="border-[1px] border-primary rounded-[8px] p-4"
      >
        <h2 className="border-b-[1px] border-primary mb-2 pb-2 text-primary">
          {languageTab.form.title}
        </h2>

        <div className="report-form">
          {/* EntityFields */}
          <SectionEntityFields />

          {/* EntityTypeFields */}
          <SectionEntityTypeFields />
          <SectionEntityTypeFieldsAccount />

          {/* EntityDataFields */}
          <SectionEntityDataFieldsCompany />
          <SectionEntityDataFieldsEquipment />
          <SectionEntityDataFieldsAccount />

          {/* PeriodFields */}
          <SectionPeriodFields />

          {/* AggregateFields */}
          <SectionAggregateFields />

          {errors.type_data?.value && (
            <span className="text-[13px] leading-6 font-normal text-comerc-error-400 mb-4">
              {errors.type_data?.value}
            </span>
          )}

          {/* TypeDataConsumption */}
          <SectionTypeDataConsumptionEnergy />
          <SectionTypeDataConsumptionWater />
          <SectionTypeDataConsumptionGas />
          <SectionTypeDataConsumptionAccount />
        </div>

        <div
          className={cn(
            'flex justify-end items-center gap-2 duration-150 overflow-y-hidden',
            showFooter ? 'opacity-100 py-2' : 'opacity-0 max-h-0'
          )}
        >
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button type="button" variant="secondary-gray">
                Download
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => handleDownload(true)}
              >
                {languageTab.form.downloadCompact}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => handleDownload(false)}
              >
                {languageTab.form.downloadDetailed}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button type="submit" variant="primary">
            {languageTab.form.generateReport}
          </Button>
        </div>
      </form>
    </ContextProvider>
  )
}

export default Form

/** EntityFields */
const SectionEntityFields = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  const handleEntity = (val: string) => {
    setValues?.({
      ...formData,
      entity_fields: { entity: '' },
      entity_type_fields: {
        type_measurement: '',
        account_type: ''
      }
    })

    setTimeout(() => {
      setValues?.({
        ...formData,
        entity_fields: { entity: val },
        entity_type_fields: {
          type_measurement: '',
          account_type: ''
        }
      })
    }, 250)
  }

  return (
    <div className="report-group">
      <h3 className="report-group-title">{languageTab.form.entity_fields}</h3>

      <div className="report-group-fields">
        <TagInput.Root>
          <TagInput.Label htmlFor="entity_fields.entity">
            {languageTab.form.input['entity_fields.entity']}
          </TagInput.Label>
          <TagInput.Content
            name="entity_fields.entity"
            placeholder={languageTab.form.selectAnOption}
            value={values?.entity_fields?.entity}
            onChange={(values) => {
              const items = formatOutputValues(values)
              handleEntity(String(items?.[0]?.id ?? ''))
            }}
            options={entityOptions.map(formatInputValue)}
            helperText={errors?.entity_fields?.entity}
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>
      </div>
    </div>
  )
}

/** EntityTypeFields */
const SectionEntityTypeFields = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  if (
    !values?.entity_fields?.entity ||
    values?.entity_fields?.entity === 'account'
  ) {
    return null
  }

  const handleMeasurement = (val: string) => {
    let consumption = { ...formData.type_data?.consumption }
    let access_log = { ...formData.type_data?.access_log }

    access_log.value = false

    if (val === 'energy') {
      consumption.fields = {
        tariff_post: '',
        capacity: '',
        greatness: '',
        consumption: ['consumo_ativo']
      }
    }
    if (val === 'water') {
      consumption.fields = {
        tariff_post: '',
        capacity: '',
        greatness: 'cubic',
        consumption: ['consumo']
      }
    }
    if (val === 'gas') {
      consumption.fields = {
        tariff_post: '',
        capacity: '',
        greatness: '',
        consumption: ['consumo_gflow_normalized']
      }
    }

    setValues?.({
      ...formData,
      entity_fields: {
        ...values.entity_fields
      },
      entity_type_fields: {
        account_type: '',
        type_measurement: ''
      }
    })

    setTimeout(() => {
      setValues?.({
        ...formData,
        entity_fields: {
          ...values.entity_fields
        },
        entity_type_fields: {
          account_type: '',
          type_measurement: val
        },
        type_data: {
          consumption
        }
      })
    }, 250)
  }
  return (
    <div className="report-group">
      <h3 className="report-group-title">
        {languageTab.form.entity_type_fields}
      </h3>

      <div className="report-group-fields">
        <TagInput.Root>
          <TagInput.Label htmlFor="entity_type_fields.type_measurement">
            {languageTab.form.input['entity_type_fields.typeMeasurement']}
          </TagInput.Label>
          <TagInput.Content
            name="entity_type_fields.type_measurement"
            placeholder={languageTab.form.selectAnOption}
            value={values?.entity_type_fields?.type_measurement ?? ''}
            onChange={(values) => {
              const items = formatOutputValues(values)
              handleMeasurement(String(items?.[0]?.id ?? ''))
            }}
            options={measurementOptions.map(formatInputValue)}
            helperText={errors?.entity_type_fields?.type_measurement}
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>
      </div>
    </div>
  )
}
const SectionEntityTypeFieldsAccount = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  if (
    !values?.entity_fields?.entity ||
    values?.entity_fields?.entity !== 'account'
  ) {
    return null
  }

  const handleMeasurement = (val: string) => {
    let consumption = { ...formData.type_data?.consumption }
    let access_log = { ...formData.type_data?.access_log }

    consumption.value = false
    access_log.value = true

    setValues?.({
      ...formData,
      entity_fields: {
        ...values.entity_fields
      },
      entity_type_fields: {
        account_type: '',
        type_measurement: ''
      }
    })

    setTimeout(() => {
      setValues?.({
        ...formData,
        entity_fields: {
          ...values.entity_fields
        },
        entity_type_fields: {
          type_measurement: '',
          account_type: val
        },
        type_data: {
          consumption,
          access_log
        }
      })
    }, 250)
  }

  return (
    <div className="report-group">
      <h3 className="report-group-title">
        {languageTab.form.input['entity_type_fields.account_type']}
      </h3>

      <div className="report-group-fields">
        <TagInput.Root>
          <TagInput.Label htmlFor="entity_type_fields.account_type">
            {languageTab.form.input['entity_type_fields.account_type']}
          </TagInput.Label>
          <TagInput.Content
            name="entity_type_fields.account_type"
            placeholder={languageTab.form.selectAnOption}
            value={values?.entity_type_fields?.account_type}
            onChange={(values) => {
              const items = formatOutputValues(values)
              handleMeasurement(String(items?.[0]?.id ?? ''))
            }}
            options={measurementAccountsOptions.map(formatInputValue)}
            helperText={errors?.entity_type_fields?.account_type}
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>
      </div>
    </div>
  )
}

/** EntityDataFields */
const SectionEntityDataFieldsCompany = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  const show =
    !!values?.entity_type_fields?.type_measurement &&
    values?.entity_fields?.entity === 'company'

  if (!show) return null

  return (
    <div className="report-group">
      <h3 className="report-group-title">
        {languageTab.form.entity_data_fields}
      </h3>

      <div className="report-group-fields">
        <TagInput.Root>
          <TagInput.Label htmlFor="entity_data_fields.entity_data">
            {languageTab.form.input['entity_data_fields.entity_data']}
          </TagInput.Label>
          <TagInput.ContentApi
            name="entity_data_fields.entity_data"
            placeholder={languageTab.form.selectAnOption}
            value={
              values?.entity_data_fields?.entity_data
                ? formatInputValues(values?.entity_data_fields?.entity_data)
                : []
            }
            onChange={(values) => {
              const items = formatOutputValues(values)
              setValues?.({
                entity_data_fields: {
                  entity_data: items
                }
              })
            }}
            featchData={(params) => companiesApiV4(http).get(params)}
            helperText={errors?.entity_data_fields?.entity_data}
            isMulti
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>

        <TagInput.Root>
          <TagInput.Label htmlFor="entity_data_fields.entity_data_group">
            {languageTab.form.input['entity_data_fields.entity_data_group']}
          </TagInput.Label>
          <TagInput.Content
            name="entity_data_fields.entity_data_group"
            placeholder="Agrupar dados por:"
            value={values?.entity_data_fields?.entity_data_group}
            onChange={(values) => {
              const items = formatOutputValues(values)
              setValues?.({
                entity_data_fields: {
                  entity_data_group: String(items?.[0]?.id ?? '')
                }
              })
            }}
            options={entityDataGroupOptions.map(formatInputValue)}
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>
      </div>
    </div>
  )
}
const SectionEntityDataFieldsEquipment = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  const show =
    !!values?.entity_type_fields?.type_measurement &&
    values?.entity_fields?.entity === 'equipment'

  if (!show) return null

  return (
    <div className="report-group">
      <h3 className="report-group-title">
        {languageTab.form.entity_data_fields}
      </h3>

      <div className="report-group-fields">
        <TagInput.Root>
          <TagInput.Label htmlFor="entity_data_fields.entity_data">
            {languageTab.form.input['entity_data_fields.entity_data']}
          </TagInput.Label>
          <TagInput.ContentApi
            name="entity_data_fields.entity_data"
            placeholder={languageTab.form.selectAnOption}
            value={
              values?.entity_data_fields?.entity_data
                ? formatInputValues(values?.entity_data_fields?.entity_data)
                : []
            }
            onChange={(values) => {
              const items = formatOutputValues(values)
              setValues?.({
                entity_data_fields: {
                  entity_data: items
                }
              })
            }}
            featchData={(params) => equipmentsApiV4(http).get(params)}
            helperText={errors?.entity_data_fields?.entity_data}
            isMulti
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>
      </div>
    </div>
  )
}
const SectionEntityDataFieldsAccount = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  const show =
    (!!values?.entity_type_fields?.type_measurement ||
      !!values?.entity_type_fields?.account_type) &&
    values?.entity_fields?.entity === 'account'

  if (!show) return null

  return (
    <div className="report-group">
      <h3 className="report-group-title">
        {languageTab.form.entity_data_fields}
      </h3>

      <div className="report-group-fields">
        <TagInput.Root>
          <TagInput.Label htmlFor="entity_data_fields.entity_data">
            {languageTab.form.input['entity_data_fields.entity_data_account']}
          </TagInput.Label>
          <TagInput.ContentApi
            name="entity_data_fields.entity_data"
            placeholder={languageTab.form.selectAnOption}
            value={
              values?.entity_data_fields?.entity_data
                ? formatInputValues(values?.entity_data_fields?.entity_data)
                : []
            }
            onChange={(values) => {
              const items = formatOutputValues(values)
              setValues?.({
                entity_data_fields: {
                  entity_data: items
                }
              })
            }}
            featchData={(params) => accountsApiV3(http).get(params)}
            helperText={errors?.entity_data_fields?.entity_data}
            isMulti
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>
      </div>
    </div>
  )
}

/** PeriodFields */
const SectionPeriodFields = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  const show =
    values?.entity_type_fields?.type_measurement ||
    values?.entity_type_fields?.account_type

  if (!show) return null

  return (
    <div className="report-group">
      <h3 className="report-group-title">{languageTab.form.period_fields}</h3>

      <div className="report-group-fields">
        <Input.Root>
          <Input.Label htmlFor="period_fields.initial_date">
            {languageTab.form.input['period_fields.initial_date']}
          </Input.Label>
          <Input.ContentDate
            value={
              values?.period_fields?.initial_date
                ? dayjs(values?.period_fields?.initial_date).toDate()
                : undefined
            }
            onChange={(value) => {
              setValues?.({
                period_fields: {
                  initial_date: value ? dayjs(value).format('YYYY-MM-DD') : ''
                }
              })
            }}
            className="text-left"
            helperText={errors?.period_fields?.initial_date}
            isClearable
            disabled={systemLoading.state.loading}
          >
            {values?.period_fields?.initial_date
              ? dayjs(values?.period_fields?.initial_date).format('DD/MM/YYYY')
              : languageTab.form.input['period_fields.initial_date']}
          </Input.ContentDate>
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor="period_fields.final_date">
            {languageTab.form.input['period_fields.final_date']}
          </Input.Label>
          <Input.ContentDate
            value={
              values?.period_fields?.final_date
                ? dayjs(values?.period_fields?.final_date).toDate()
                : undefined
            }
            onChange={(value) => {
              setValues?.({
                period_fields: {
                  final_date: value ? dayjs(value).format('YYYY-MM-DD') : ''
                }
              })
            }}
            className="text-left"
            helperText={errors?.period_fields?.final_date}
            isClearable
            disabled={systemLoading.state.loading}
          >
            {values?.period_fields?.final_date
              ? dayjs(values?.period_fields?.final_date).format('DD/MM/YYYY')
              : languageTab.form.input['period_fields.final_date']}
          </Input.ContentDate>
        </Input.Root>

        <TagInput.Root>
          <TagInput.Label htmlFor="period_fields.syntax_date">
            {languageTab.form.input['period_fields.syntax_date']}
          </TagInput.Label>
          <TagInput.Content
            name="period_fields.syntax_date"
            placeholder={languageTab.form.selectAnOption}
            value={values?.period_fields?.syntax_date}
            onChange={(values) => {
              const items = formatOutputValues(values)
              setValues?.({
                period_fields: {
                  syntax_date: String(items?.[0]?.id ?? '')
                }
              })
            }}
            options={syntaxDateOptions.map(formatInputValue)}
            helperText={errors?.period_fields?.syntax_date}
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>
      </div>
    </div>
  )
}

/** AggregateFields */
const SectionAggregateFields = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, setValues } = useContextForm()

  const show =
    values?.entity_type_fields?.type_measurement ||
    values?.entity_type_fields?.account_type

  if (!show) return null

  return (
    <div className="report-group">
      <h3 className="report-group-title">
        {languageTab.form.aggregate_fields}
      </h3>

      <div className="report-group-fields">
        <TagInput.Root>
          <TagInput.Label htmlFor="aggregate_fields.date_interval">
            {languageTab.form.input['aggregate_fields.date_interval']}
          </TagInput.Label>
          <TagInput.Content
            name="aggregate_fields.date_interval"
            placeholder={languageTab.form.selectAnOption}
            value={values?.aggregate_fields?.date_interval}
            onChange={(values) => {
              const items = formatOutputValues(values)
              setValues?.({
                aggregate_fields: {
                  date_interval: String(items?.[0]?.id ?? '')
                }
              })
            }}
            options={dateIntervalOptions.map(formatInputValue)}
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>

        <Input.Root>
          <Input.Label htmlFor="aggregate_fields.date_interval_number">
            {languageTab.form.input['aggregate_fields.date_interval_number']}
          </Input.Label>
          <Input.Content
            name="aggregate_fields.date_interval_number"
            value={values?.aggregate_fields?.date_interval_number}
            onChange={(event) => {
              setValues?.({
                aggregate_fields: {
                  date_interval_number: event.target.value
                }
              })
            }}
            type="number"
            step={1}
            disabled={systemLoading.state.loading}
          />
        </Input.Root>

        <TagInput.Root>
          <TagInput.Label htmlFor="aggregate_fields.type">
            {languageTab.form.input['aggregate_fields.type']}
          </TagInput.Label>
          <TagInput.Content
            name="aggregate_fields.type"
            placeholder={languageTab.form.selectAnOption}
            value={values?.aggregate_fields?.type}
            onChange={(values) => {
              const items = formatOutputValues(values)
              setValues?.({
                aggregate_fields: {
                  type: String(items?.[0]?.id ?? '')
                }
              })
            }}
            options={typeOptions.map(formatInputValue)}
            isClearable
            disabled={systemLoading.state.loading}
          />
        </TagInput.Root>
      </div>
    </div>
  )
}

/** TypeDataConsumption */
const SectionTypeDataConsumptionEnergy = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  const show =
    values?.entity_fields?.entity &&
    ['company', 'equipment'].includes(values?.entity_fields?.entity) &&
    values?.entity_type_fields?.type_measurement &&
    values?.entity_type_fields?.type_measurement === 'energy'

  if (!show) return null

  return (
    <>
      <div className="report-group">
        <div className="flex justify-between">
          <h3 className="report-group-title">{languageTab.form.consumption}</h3>
          <Switch.Content
            checked={!!values?.type_data?.consumption?.value}
            onChange={(value) => {
              setValues?.({
                type_data: {
                  consumption: { value: value },
                  demand: { value: false },
                  power_factor: { value: false },
                  access_log: { value: false }
                }
              })
            }}
            disabled={systemLoading.state.loading}
          />
        </div>

        <div className="report-group-fields">
          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.consumption.tariff_post">
              {languageTab.form.input['type_data.consumption.tariff_post']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.consumption.tariff_post"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.consumption?.fields?.tariff_post}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    consumption: {
                      fields: {
                        tariff_post: String(items?.[0]?.id ?? '')
                      }
                    }
                  }
                })
              }}
              options={tariffPostOptions.map(formatInputValue)}
              isClearable
              disabled={systemLoading.state.loading}
              helperText={errors?.type_data?.consumption?.fields?.tariff_post}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.consumption.capacity">
              {languageTab.form.input['type_data.consumption.capacity']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.consumption.capacity"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.consumption?.fields?.capacity}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    consumption: {
                      fields: {
                        capacity: String(items?.[0]?.id ?? '')
                      }
                    }
                  }
                })
              }}
              options={capacityOptions.map(formatInputValue)}
              isClearable
              disabled={systemLoading.state.loading}
              helperText={errors?.type_data?.consumption?.fields?.capacity}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.consumption.consumption">
              {languageTab.form.input['type_data.consumption.consumption']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.consumption.consumption"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.consumption?.fields?.consumption}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    consumption: {
                      fields: {
                        consumption: items.map(({ id }) => String(id))
                      }
                    }
                  }
                })
              }}
              options={consumptionOptions.map(formatInputValue)}
              isClearable
              isMulti
              disabled={systemLoading.state.loading}
              helperText={errors?.type_data?.consumption?.fields?.consumption}
            />
          </TagInput.Root>
        </div>
      </div>

      <div className="report-group">
        <div className="flex justify-between">
          <h3 className="report-group-title">{languageTab.form.demand}</h3>
          <Switch.Content
            checked={!!values?.type_data?.demand?.value}
            onChange={(value) => {
              setValues?.({
                type_data: {
                  consumption: { value: false },
                  demand: { value: value },
                  power_factor: { value: false },
                  access_log: { value: false }
                }
              })
            }}
          />
        </div>

        <div className="report-group-fields">
          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.demand.tariff_post">
              {languageTab.form.input['type_data.demand.tariff_post']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.demand.tariff_post"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.demand?.fields?.tariff_post}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    demand: {
                      fields: {
                        tariff_post: String(items?.[0]?.id ?? '')
                      }
                    }
                  }
                })
              }}
              options={tariffPostOptions.map(formatInputValue)}
              isClearable
              helperText={errors?.type_data?.demand?.fields?.tariff_post}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.demand.capacity">
              {languageTab.form.input['type_data.demand.capacity']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.demand.capacity"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.demand?.fields?.capacity}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    demand: {
                      fields: {
                        capacity: String(items?.[0]?.id ?? '')
                      }
                    }
                  }
                })
              }}
              options={capacityOptions.map(formatInputValue)}
              isClearable
              helperText={errors?.type_data?.demand?.fields?.capacity}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.demand.consumption">
              {languageTab.form.input['type_data.demand.consumption']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.demand.consumption"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.demand?.fields?.consumption}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    demand: {
                      fields: {
                        consumption: items.map(({ id }) => String(id))
                      }
                    }
                  }
                })
              }}
              options={consumptionDemandOptions.map(formatInputValue)}
              isClearable
              isMulti
              helperText={errors?.type_data?.demand?.fields?.consumption}
            />
          </TagInput.Root>
        </div>
      </div>

      <div className="report-group">
        <div className="flex justify-between">
          <h3 className="report-group-title">
            {languageTab.form.power_factor}
          </h3>
          <Switch.Content
            checked={!!values?.type_data?.power_factor?.value}
            onChange={(value) => {
              setValues?.({
                type_data: {
                  consumption: { value: false },
                  demand: { value: false },
                  power_factor: { value: value },
                  access_log: { value: false }
                }
              })
            }}
          />
        </div>

        <div className="report-group-fields">
          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.power_factor.tariff_post">
              {languageTab.form.input['type_data.power_factor.tariff_post']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.power_factor.tariff_post"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.power_factor?.fields?.tariff_post}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    power_factor: {
                      fields: {
                        tariff_post: String(items?.[0]?.id ?? '')
                      }
                    }
                  }
                })
              }}
              options={tariffPostOptions.map(formatInputValue)}
              isClearable
              helperText={errors?.type_data?.power_factor?.fields?.tariff_post}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.power_factor.capacity">
              {languageTab.form.input['type_data.power_factor.capacity']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.power_factor.capacity"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.power_factor?.fields?.capacity}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    power_factor: {
                      fields: {
                        capacity: String(items?.[0]?.id ?? '')
                      }
                    }
                  }
                })
              }}
              options={capacityOptions.map(formatInputValue)}
              isClearable
              helperText={errors?.type_data?.power_factor?.fields?.capacity}
            />
          </TagInput.Root>
        </div>
      </div>
    </>
  )
}
const SectionTypeDataConsumptionWater = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  const show =
    values?.entity_fields?.entity &&
    ['company', 'equipment'].includes(values?.entity_fields?.entity) &&
    values?.entity_type_fields?.type_measurement &&
    values?.entity_type_fields?.type_measurement === 'water'

  if (!show) return null

  return (
    <>
      <div className="report-group">
        <div className="flex justify-between">
          <h3 className="report-group-title">{languageTab.form.consumption}</h3>
          <Switch.Content
            checked={!!values?.type_data?.consumption?.value}
            onChange={(value) => {
              setValues?.({
                type_data: {
                  consumption: { value: false },
                  demand: { value: value },
                  power_factor: { value: false },
                  access_log: { value: false }
                }
              })
            }}
            disabled={systemLoading.state.loading}
          />
        </div>

        <div className="report-group-fields">
          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.consumption.consumption">
              {languageTab.form.input['type_data.consumption.consumption']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.consumption.consumption"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.consumption?.fields?.consumption}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    consumption: {
                      fields: {
                        consumption: items.map(({ id }) => String(id))
                      }
                    }
                  }
                })
              }}
              options={consumptionWaterOptions.map(formatInputValue)}
              isClearable
              isMulti
              disabled={systemLoading.state.loading}
              helperText={errors?.type_data?.consumption?.fields?.consumption}
            />
          </TagInput.Root>

          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.consumption.greatness">
              {languageTab.form.input['type_data.consumption.greatness']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.consumption.greatness"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.consumption?.fields?.greatness}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    consumption: {
                      fields: {
                        greatness: String(items?.[0]?.id ?? '')
                      }
                    }
                  }
                })
              }}
              options={capacityWaterOptions.map(formatInputValue)}
              isClearable
              disabled={systemLoading.state.loading}
              helperText={errors?.type_data?.consumption?.fields?.greatness}
            />
          </TagInput.Root>
        </div>
      </div>
    </>
  )
}
const SectionTypeDataConsumptionGas = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, errors, setValues } = useContextForm()

  const show =
    values?.entity_fields?.entity &&
    ['company', 'equipment'].includes(values?.entity_fields?.entity) &&
    values?.entity_type_fields?.type_measurement &&
    values?.entity_type_fields?.type_measurement === 'gas'

  if (!show) return null

  return (
    <>
      <div className="report-group">
        <div className="flex justify-between">
          <h3 className="report-group-title">{languageTab.form.consumption}</h3>
          <Switch.Content
            checked={!!values?.type_data?.consumption?.value}
            onChange={(value) => {
              setValues?.({
                type_data: {
                  consumption: { value: false },
                  demand: { value: false },
                  power_factor: { value: value },
                  access_log: { value: false }
                }
              })
            }}
            disabled={systemLoading.state.loading}
          />
        </div>

        <div className="report-group-fields">
          <TagInput.Root>
            <TagInput.Label htmlFor="type_data.consumption.consumption">
              {languageTab.form.input['type_data.consumption.consumption']}
            </TagInput.Label>
            <TagInput.Content
              name="type_data.consumption.consumption"
              placeholder={languageTab.form.selectAnOption}
              value={values?.type_data?.consumption?.fields?.consumption}
              onChange={(values) => {
                const items = formatOutputValues(values)
                setValues?.({
                  type_data: {
                    consumption: {
                      fields: {
                        consumption: items.map(({ id }) => String(id))
                      }
                    }
                  }
                })
              }}
              options={consumptionGasOptions.map(formatInputValue)}
              isClearable
              isMulti
              disabled={systemLoading.state.loading}
              helperText={errors?.type_data?.consumption?.fields?.consumption}
            />
          </TagInput.Root>
        </div>
      </div>
    </>
  )
}
const SectionTypeDataConsumptionAccount = () => {
  const systemLoading = useSystemLoadingStore()
  const languageTab = useLanguageTabGenerate()

  const { values, setValues } = useContextForm()

  const show =
    values?.entity_fields?.entity === 'account' &&
    values?.entity_type_fields?.account_type

  if (!show) return null

  return (
    <>
      <div className="report-group">
        <div className="flex justify-between">
          <h3 className="report-group-title">
            {languageTab.form.consumption_account}
          </h3>

          <Switch.Content
            checked={!!values?.type_data?.access_log?.value}
            onChange={(value) => {
              setValues?.({
                type_data: {
                  consumption: { value: false },
                  demand: { value: false },
                  power_factor: { value: false },
                  access_log: { value: value }
                }
              })
            }}
            disabled={systemLoading.state.loading}
          />
        </div>
      </div>
    </>
  )
}
