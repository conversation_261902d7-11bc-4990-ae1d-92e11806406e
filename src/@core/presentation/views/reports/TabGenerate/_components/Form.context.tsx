import { createContext, ReactNode, useContext } from 'react'

import { IFormErrors, IFormValues } from '../Tab.types'

type IContextForm = {
  setValues: (data: IFormValues) => void
  values: IFormValues
  errors: IFormErrors
}

const ContextForm = createContext<Partial<IContextForm>>({})

type IContextFormProps = IContextForm & { children: ReactNode }

export const useContextForm = () => useContext(ContextForm)

export const ContextProvider = ({ children, ...value }: IContextFormProps) => {
  return <ContextForm.Provider value={value}>{children}</ContextForm.Provider>
}
