import { Table } from '@/@core/presentation/shared/ui/table'

import { useStatePage } from '../../page.hooks'
import { useLanguageTabGenerate } from '../Tab.hooks'

export default function TableReport() {
  const { reportGenereted } = useStatePage().tabGenerate
  const languageTab = useLanguageTabGenerate()

  if (!reportGenereted)
    return (
      <div className="report-table h-[300px] flex items-center justify-center relative overflow-hidden bg-[#70707012] shadow-inner rounded-xl">
        <div className="absolute left-0 top-0 h-full w-full bg-reports-hero bg-no-repeat bg-bottom bg-cover opacity-60 grayscale rotate-180 pointer-events-none" />
        <div className="absolute right-0 top-0 h-full w-full bg-reports-hero bg-no-repeat bg-bottom bg-cover opacity-60 grayscale pointer-events-none" />
        <span className="relative z-10 text-center mx-auto max-w-[150px] text-[#707070]">
          {languageTab.table.infoGenerateReport}
        </span>
      </div>
    )

  return (
    <Table.Root className="report-table p-0">
      <Table.Info className="flex flex-col items-start px-2">
        <Table.InfoTitle className="text-2xl">
          {languageTab.table.titleInfo}
        </Table.InfoTitle>
        <div className="flex justify-between w-full text-primary bg-comerc-grayLight-200 dark:bg-comerc-grayLight-800 duration-150 p-2">
          <div>
            <b className="font-acuminPro-Semibold">
              {languageTab.table.infoOrderDate}:
            </b>{' '}
            {reportGenereted.information.requestDate}
          </div>
          <div>
            <b className="font-acuminPro-Semibold">
              {languageTab.table.infoRequestedPeriod}:
            </b>{' '}
            {reportGenereted.information.requestedPeriod.initial} -{' '}
            {reportGenereted.information.requestedPeriod.final}
          </div>
        </div>
        <Table.InfoTitle className="text-2xl">
          {languageTab.table.titleTable}
        </Table.InfoTitle>
      </Table.Info>

      <Table.Header>
        <Table.Row>
          {reportGenereted.table.header.map(({ label }) => (
            <Table.Head key={label}>{label}</Table.Head>
          ))}
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {reportGenereted.table.body.map((row, i) => (
          <Table.Row key={i}>
            {row.map((value, idx) => (
              <Table.Cell key={idx} className="px-2 py-1">
                {value ?? ''}
              </Table.Cell>
            ))}
          </Table.Row>
        ))}

        <Table.Row hide={!!reportGenereted.table.body.length}>
          <Table.Cell colSpan={reportGenereted.table.header.length}>
            {languageTab.table.withoutData}
          </Table.Cell>
        </Table.Row>
      </Table.Body>
    </Table.Root>
  )
}
