import '@/__mock__/logging/logger'

import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { cleanup, renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { reportsGeneratorMock1 } from '@/__mock__/content/api-reports-generator.content'
import { useStatePage } from '../page.hooks'
import { useLanguageTabGenerate, useMethodsTabGenerate } from './Tab.hooks'

cleanup()

jest.mock('@/@core/infra/api/ReportsGeneratorApiV3')

const spyReportsGeneratorApiV3 = jest.spyOn(
  require('@/@core/infra/api/ReportsGeneratorApiV3'),
  'reportsGeneratorApiV3'
)

jest.mock('@/@core/infra/api/ReportsDownloadApiV3')

const spyReportsDownloadApiV3 = jest.spyOn(
  require('@/@core/infra/api/ReportsDownloadApiV3'),
  'reportsDownloadApiV3'
)

jest.mock('@/@core/infra/api/ReportsApiV3')

const spyReportsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ReportsApiV3'),
  'reportsApiV3'
)

describe('src/@core/presentation/views/reports/TabGenerate/Tab | useLanguageTabGenerate', () => {
  it('check de tab title', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabGenerate()
      }),
      { wrapper: AppStoreProvider }
    )
    expect(result.current.language.form.entity_fields).toBe('Grupo')
    expect(result.current.language.form.entity_data_fields).toBe(
      'Dados da entidade'
    )
  })
})

describe('src/@core/presentation/views/reports/TabGenerate/Tab | useMethodsTabGenerate', () => {
  it('should exec method handleSubmitFilter', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodsTabGenerate()
      }),
      { wrapper: AppStoreProvider }
    )

    /** request error */
    await waitFor(() => result.current.state.reset())

    spyReportsGeneratorApiV3.mockImplementation(() => ({
      generate: jest.fn().mockRejectedValue({
        status: 422,
        data: { entity_data: { entity: ['campo obrigatórios'] } }
      })
    }))
    await waitFor(async () => {
      result.current.method.handleSubmitFilter({})
    })
    expect(result.current.state.tabGenerate.reportGenereted).toBeNull()

    /** request no data */
    await waitFor(() => result.current.state.reset())

    spyReportsGeneratorApiV3.mockImplementation(() => ({
      generate: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(async () => {
      result.current.method.handleSubmitFilter({})
    })
    expect(result.current.state.tabGenerate.reportGenereted).toBeNull()

    /** request successful with data */
    await waitFor(() => result.current.state.reset())

    spyReportsGeneratorApiV3.mockImplementation(() => ({
      generate: jest.fn().mockResolvedValue({
        status: 200,
        data: reportsGeneratorMock1
      })
    }))
    await waitFor(async () => {
      result.current.method.handleSubmitFilter({})
    })
    expect(result.current.state.tabGenerate.reportGenereted).toEqual(
      reportsGeneratorMock1
    )
  })

  it('should exec method handleDownload', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodsTabGenerate(),
        toast: useSystemToastStore()
      }),
      { wrapper: AppStoreProvider }
    )

    /** request error */
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyReportsDownloadApiV3.mockImplementation(() => ({
      download: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      result.current.method.handleDownload({})
    })
    expect(result.current.toast.state.toasts?.[0]?.type).toBe('error')
    expect(result.current.toast.state.toasts?.[0]?.message).toBe(
      'Ocorreu um erro, tente novamente mais tarde'
    )

    /** request no data */
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyReportsDownloadApiV3.mockImplementation(() => ({
      download: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(async () => {
      result.current.method.handleDownload({})
    })
    expect(result.current.toast.state.toasts?.[0]?.type).toBeUndefined()
    expect(result.current.toast.state.toasts?.[0]?.message).toBeUndefined()

    /** request successful with data */
    const message = 'Sucesso. Processando o relatório para o envio por email.'
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyReportsDownloadApiV3.mockImplementation(() => ({
      download: jest.fn().mockResolvedValue({
        status: 200,
        data: message
      })
    }))
    await waitFor(async () => {
      result.current.method.handleDownload({})
    })
    expect(result.current.toast.state.toasts?.[0]?.type).toBe('success')
    expect(result.current.toast.state.toasts?.[0]?.message).toBe(message)
  })

  it('should exec method handleDownload', async () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodsTabGenerate(),
        toast: useSystemToastStore()
      }),
      { wrapper: AppStoreProvider }
    )

    /** request error */
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyReportsApiV3.mockImplementation(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      result.current.method.handleSaveFilter(null, {})
    })
    expect(result.current.toast.state.toasts?.[0]?.type).toBe('error')
    expect(result.current.toast.state.toasts?.[0]?.message).toBe(
      'Erro ao criar relatório'
    )

    /** request create success */
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyReportsApiV3.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({ status: 200 })
    }))
    await waitFor(async () => {
      result.current.method.handleSaveFilter(null, {})
    })
    expect(result.current.toast.state.toasts?.[0]?.type).toBe('success')
    expect(result.current.toast.state.toasts?.[0]?.message).toBe(
      'Relatório criado com sucesso'
    )

    /** request update success */
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyReportsApiV3.mockImplementation(() => ({
      update: jest.fn().mockResolvedValue({ status: 200 })
    }))
    await waitFor(async () => {
      result.current.method.handleSaveFilter(22, {})
    })
    expect(result.current.toast.state.toasts?.[0]?.type).toBe('success')
    expect(result.current.toast.state.toasts?.[0]?.message).toBe(
      'Relatório atualizado com sucesso'
    )
  })
})

describe('src/@core/presentation/views/reports/TabGenerate/Tab | useMethodsTabGenerate', () => {
  it('should exec method handleSubmitFilter', () => {
    const { result } = renderHook(
      () => ({
        state: useStatePage(),
        method: useMethodsTabGenerate()
      }),
      { wrapper: AppStoreProvider }
    )
  })
  it('should exec method handleSaveFilter', () => {
    const { result } = renderHook(
      () => ({
        method: useMethodsTabGenerate(),
        toast: useSystemToastStore()
      }),
      { wrapper: AppStoreProvider }
    )
  })
})

// 24-40,49-66,75-113
