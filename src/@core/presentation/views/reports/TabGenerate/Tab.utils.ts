import { IFormErrors, IFormValues } from './Tab.types'

export const formValidate = (
  values: IFormValues,
  {
    requiredField,
    requiredFinalDate,
    requiredTypeData
  }: {
    requiredField: string
    requiredFinalDate: string
    requiredTypeData: string
  }
): [boolean, IFormErrors] => {
  let error: IFormErrors = {}

  const { entity_fields, entity_type_fields, period_fields, type_data } = values

  /** entity */
  if (!entity_fields?.entity) {
    error = {
      entity_fields: { entity: requiredField }
    }
    return [true, error]
  }

  /** type_measurement */
  if (
    !Object.keys(error).length &&
    entity_fields?.entity !== 'account' &&
    !entity_type_fields?.type_measurement
  ) {
    error = {
      entity_type_fields: { type_measurement: requiredField }
    }
    return [true, error]
  }

  /** account_type */
  if (
    !Object.keys(error).length &&
    entity_fields?.entity === 'account' &&
    !entity_type_fields?.account_type
  ) {
    error = {
      entity_type_fields: { account_type: requiredField }
    }
    return [true, error]
  }

  if (
    !Object.keys(error).length &&
    period_fields?.initial_date &&
    !period_fields?.final_date
  ) {
    error = {
      period_fields: {
        final_date: requiredFinalDate
      }
    }
    return [true, error]
  }

  if (
    !Object.keys(error).length &&
    !period_fields?.final_date &&
    !period_fields?.syntax_date
  ) {
    error = {
      period_fields: { initial_date: requiredField }
    }
    return [true, error]
  }

  if (
    !Object.keys(error).length &&
    [
      !!type_data?.consumption?.value,
      !!type_data?.demand?.value,
      !!type_data?.power_factor?.value,
      !!type_data?.access_log?.value
    ].filter(Boolean).length === 0
  ) {
    error = {
      type_data: {
        value: requiredTypeData
      }
    }
    return [true, error]
  }

  return [false, error]
}
export const formOutput = (values: IFormValues) => {
  let data: IFormValues = JSON.parse(JSON.stringify(values))

  function removeKeysInObject({ ...obj }): Record<string, Object> {
    for (const key in obj) {
      const typeConstructor = obj[key]

      const isObject =
        !!typeConstructor && typeConstructor.constructor === Object

      if (isObject) {
        obj[key] = removeKeysInObject(obj[key])
      }

      const hasContent = ['', 'null', null].includes(obj[key])

      if (!isObject && hasContent) {
        delete obj[key]
      }

      if (key === 'entity_data') {
        obj[key] = obj[key]?.map(({ id }: { id: number }) => id)
      }
    }
    return obj
  }

  if (!data.aggregate_fields?.date_interval) {
    delete data.aggregate_fields
  }

  if (data?.type_data) {
    data.type_data = Object.entries(data.type_data).reduce(
      (acc: any, [level, data]: [string, any]) => {
        if (data.value) {
          acc[level] = data
        }
        return acc
      },
      {}
    )
  }

  return removeKeysInObject(data)
}
export const mergeFormValues = (
  prev: IFormValues,
  data: IFormValues
): IFormValues => ({
  ...prev,
  entity_fields: {
    ...prev?.entity_fields,
    ...data?.entity_fields
  },
  entity_type_fields: {
    ...prev?.entity_type_fields,
    ...data?.entity_type_fields
  },
  entity_data_fields: {
    ...prev?.entity_data_fields,
    ...data?.entity_data_fields
  },
  period_fields: {
    ...prev?.period_fields,
    ...data?.period_fields
  },
  aggregate_fields: {
    ...prev?.aggregate_fields,
    ...data?.aggregate_fields
  },
  type_data: {
    consumption: {
      ...prev?.type_data?.consumption,
      ...data?.type_data?.consumption,
      fields: {
        ...prev?.type_data?.consumption?.fields,
        ...data?.type_data?.consumption?.fields
      }
    },
    demand: {
      ...prev?.type_data?.demand,
      ...data?.type_data?.demand,
      fields: {
        ...prev?.type_data?.demand?.fields,
        ...data?.type_data?.demand?.fields
      }
    },
    power_factor: {
      ...prev?.type_data?.power_factor,
      ...data?.type_data?.power_factor,
      fields: {
        ...prev?.type_data?.power_factor?.fields,
        ...data?.type_data?.power_factor?.fields
      }
    },
    access_log: {
      ...prev?.type_data?.access_log,
      ...data?.type_data?.access_log,
      fields: {}
    }
  }
})
