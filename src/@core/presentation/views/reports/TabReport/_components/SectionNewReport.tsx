import { Button } from '@/@core/presentation/shared/ui/button'
import { stateData } from '../../page.content'
import { useStatePage } from '../../page.hooks'
import { useLanguageTabLatest } from '../Tab.hooks'

const SectionNewReport = () => {
  const statePage = useStatePage()
  const languageTab = useLanguageTabLatest()

  return (
    <div className="hero-container">
      <div className="hero-container-gradient" />
      <div className="hero-container-overlay" />
      <div className="hero-container-content">
        <h2 className="hero-container-title">{languageTab.panel.title}</h2>
        <p className="mb-2">{languageTab.panel.subTitle}</p>
        <Button
          variant="secondary-gray"
          onClick={() =>
            statePage.set({
              tab: { active: 'generate' },
              tabGenerate: stateData.tabGenerate
            })
          }
        >
          Relatório em branco
        </Button>
      </div>
    </div>
  )
}

export default SectionNewReport
