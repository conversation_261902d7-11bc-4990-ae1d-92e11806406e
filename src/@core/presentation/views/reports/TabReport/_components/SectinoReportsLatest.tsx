import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { useStatePage } from '@/@core/presentation/views/reports/page.hooks'

import { useMethodPage } from '../../page.hooks'
import { useLanguageTabLatest } from '../Tab.hooks'

const SectinoReportsLatest = () => {
  const systemLoading = useSystemLoadingStore()

  const statePage = useStatePage()
  const methodPage = useMethodPage()
  const languageTab = useLanguageTabLatest()

  return (
    <div className="reports-container">
      <div className="reports-container-card">
        <div className="reports-container-header">
          <h2 className="reports-container-title">
            {languageTab.latest.title}
          </h2>
          <button
            className="reports-container-link"
            onClick={() => statePage.setTab('history')}
          >
            {languageTab.latest.seeMore}
          </button>
        </div>
        {statePage.tabReport.table.items.map((report) => (
          <button
            className={cn(
              'flex items-center w-full h-[60px] gap-2 p-2 mb-2 rounded-md text-primary',
              'shadow-md shadow-comerc-grayLight-200 disabled:shadow-none dark:shadow-none',
              'border-[1px] border-primary',
              'disabled:bg-black/5 hover:bg-black/5 dark:bg-black/5 disabled:dark:bg-white/5 hover:dark:bg-white/5 '
            )}
            key={report.id}
            onClick={() => methodPage.openReportById(report.id)}
            disabled={systemLoading.state.loading}
          >
            <Icon
              icon="fileCheck02"
              width="25"
              height="25"
              fill="none"
              className="icon-style"
              strokeWidth="1.66667"
            />
            <div>
              <p className="text-left xt-sm text-comerc-grayLight-600 dark:text-comerc-grayLight-300">
                {report.name}
              </p>
              <p className="text-left xt-xs text-comerc-grayLight-600 dark:text-comerc-grayLight-300">
                {report.updatedAt}
              </p>
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

export default SectinoReportsLatest
