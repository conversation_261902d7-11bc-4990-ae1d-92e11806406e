import { alarmStatusOptions } from '@/@core/content/alarmStatusOptions.content'
import { mapAlarmStatusBySlug } from '@/@core/content/mapColorsStatusAlarm.content'
import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import {
  listTypeSubTypeGroupedCookie,
  pageMonitoringCookie
} from '@/@core/infra/memory/cookie'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Checkbox } from '@/@core/presentation/shared/ui/checkbox'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon, IconName } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { InputDate } from '@/@core/presentation/shared/ui/inputData'
import { RadioGroup } from '@/@core/presentation/shared/ui/radioGroup'
import { Table } from '@/@core/presentation/shared/ui/table'
import { TableMobile } from '@/@core/presentation/shared/ui/tableMobile'
import { TagInput } from '@/@core/presentation/shared/ui/tagInput'
import { handleKeyEnter } from '@/@core/utils/handleInputSearch'
import { mergeSortOrderData } from '@/@core/utils/handleSorteColumn'
import { useDebounceFunction } from '@/hooks/useDebouce'
import { zodResolver } from '@hookform/resolvers/zod'
import { FC, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import {
  useLanguagePage,
  useMethodPage,
  useStatePageMonitoring
} from '../page.hooks'
import { handleTypeChange, validateFinalDate } from '../page.utils'

export const MonitoringTable = () => {
  const statePage = useStatePageMonitoring()
  const methodPage = useMethodPage()
  const languagePage = useLanguagePage()
  const systemLoading = useSystemLoadingStore()
  const systemLoadingStore = useSystemLoadingStore()
  const listTypeSubTypeGrouped = listTypeSubTypeGroupedCookie.get()
  const searchFields = pageMonitoringCookie.get()

  const handleSortColumn = async (props: {
    key: 'equipamento_id' | 'status_id' | 'last_message'
  }) => {
    const { sort, order } = mergeSortOrderData(props.key, searchFields)
    pageMonitoringCookie.set({ order, sort })
    await methodPage.getData()
  }

  const handleInput = useDebounceFunction(() => {
    methodPage.getData()
  }, 250)

  return (
    <Table.Root>
      <Table.Info className="flex flex-col gap-y-6">
        <div className="flex mr-auto gap-3">
          <Table.InfoTitle>{languagePage.page.title}</Table.InfoTitle>

          <Table.InfoBadge>
            {statePage.total}
            <span className="hidden md:inline-block md:ml-1">
              {languagePage.table.totalRegisters}
            </span>
          </Table.InfoBadge>
        </div>

        <AlarmCount />

        <div className="flex flex-wrap ml-auto gap-3">
          <div className="hidden xl:block space-x-2">
            <ModalMeasurementCount />
            <ModalMonitoringDownload />
          </div>
          <TagInput.Root className="w-full sm:w-[250px]">
            <TagInput.Content
              placeholder={languagePage.table.search.fieldStatus}
              value={searchFields.statusId ? String(searchFields.statusId) : ''}
              onChange={(items) => {
                pageMonitoringCookie.set({
                  statusId: Number(items?.[0]?.value),
                  page: 1
                })
                handleInput()
              }}
              options={alarmStatusOptions}
              disabled={systemLoadingStore.state.loading}
            />
          </TagInput.Root>

          <TagInput.Root className="w-full sm:w-[250px]">
            <TagInput.Content
              placeholder={languagePage.table.search.fieldType}
              name={languagePage.table.search.fieldType}
              value={`${searchFields.typeId}.${searchFields.subtypeId}`}
              onChange={(args: unknown) => {
                const { typeId, subtypeId } = handleTypeChange(args)
                pageMonitoringCookie.set({ typeId, subtypeId, page: 1 })
                handleInput()
              }}
              options={listTypeSubTypeGrouped.list}
              disabled={systemLoading.state.loading}
            />
          </TagInput.Root>

          <Input.Root className="w-full sm:w-[250px]">
            <Input.Content
              slotStart={
                <Icon
                  icon="searchLg"
                  className="icon-menu-primary"
                  height="24"
                  width="24"
                  viewBox="0 0 20 20"
                />
              }
              placeholder={languagePage.table.search.fieldQuery}
              type="text"
              defaultValue={searchFields?.q ?? ''}
              onChange={(e) => {
                pageMonitoringCookie.set({ q: e.target.value, page: 1 })
              }}
              onKeyUp={(e) => handleKeyEnter(e.key, methodPage.getData)}
            />
          </Input.Root>
        </div>
      </Table.Info>

      <Table.Header>
        <Table.Row>
          <Table.Head
            className="cursor-pointer"
            onClick={() => handleSortColumn({ key: 'equipamento_id' })}
          >
            {languagePage.table.columns.equipmentId}
            <Table.CellIcon
              field="equipmentId"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head>{languagePage.table.columns.equipmentName}</Table.Head>
          <Table.Head>{languagePage.table.columns.serialNumber}</Table.Head>
          <Table.Head
            className="cursor-pointer"
            onClick={() => handleSortColumn({ key: 'status_id' })}
          >
            {languagePage.table.columns.status}
            <Table.CellIcon
              field="status_id"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head
            className="cursor-pointer"
            onClick={() => handleSortColumn({ key: 'last_message' })}
          >
            {languagePage.table.columns.lastReading}
            <Table.CellIcon
              field="last_message"
              sort={searchFields.sort}
              order={searchFields.order}
            />
          </Table.Head>
          <Table.Head>{languagePage.table.columns.lastAlarm}</Table.Head>
          <Table.Head>{languagePage.table.columns.template}</Table.Head>
          <Table.Head>{languagePage.table.columns.company}</Table.Head>
          <Table.Head>{languagePage.table.columns.version}</Table.Head>
          <Table.Head>{languagePage.table.columns.location}</Table.Head>
          <Table.Head>{languagePage.table.columns.connection}</Table.Head>
          <Table.Head>{languagePage.table.columns.actions}</Table.Head>
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {statePage.items.map((monitoring) => (
          <Table.Row key={monitoring.equipmentId}>
            <Table.Cell>{monitoring.equipmentId}</Table.Cell>
            <Table.Cell>{monitoring.equipmentName}</Table.Cell>
            <Table.Cell>{monitoring.code}</Table.Cell>
            <Table.Cell className="min-w-24">
              <Badge.Root
                className={cn(
                  mapAlarmStatusBySlug[monitoring.status ?? 'default']
                    .badgeClassName,
                  'flex justify-center',
                  'min-w-[100px]'
                )}
              >
                <Badge.Content>
                  {mapAlarmStatusBySlug[monitoring.status ?? 'default'].label}
                </Badge.Content>
              </Badge.Root>
            </Table.Cell>
            <Table.Cell>{monitoring.lastMessage}</Table.Cell>
            <Table.Cell>{monitoring.lastAlarm}</Table.Cell>
            <Table.Cell>
              {monitoring.typeName} - {monitoring.subTypeName}
            </Table.Cell>
            <Table.Cell>{monitoring.companyName}</Table.Cell>
            <Table.Cell>{monitoring.version}</Table.Cell>
            <Table.Cell>{monitoring.location}</Table.Cell>
            <Table.Cell>{monitoring.connection}</Table.Cell>
            <Table.Cell width={80}></Table.Cell>
          </Table.Row>
        ))}
        <Table.RowLoading status={systemLoading.state.loading} colSpan={2} />
      </Table.Body>

      <Table.Mobile>
        {statePage.items.map((monitoring) => (
          <TableMobile.Item key={monitoring.equipmentId}>
            <TableMobile.Head />
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.equipmentName}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.equipmentId}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.equipmentName}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.equipmentName}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.serialNumber}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.code}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.status}</TableMobile.Cell>
              <TableMobile.Cell>
                <Badge.Root
                  className={cn(
                    mapAlarmStatusBySlug[monitoring.status ?? 'default']
                      .badgeClassName,
                    'min-w-28'
                  )}
                >
                  <Badge.Content>
                    {mapAlarmStatusBySlug[monitoring.status ?? 'default'].label}
                  </Badge.Content>
                </Badge.Root>
              </TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.lastReading}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.lastMessage}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.lastAlarm}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.lastAlarm}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.template}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.typeName} - {monitoring.subTypeName}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.company}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.companyName}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.version}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.version}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.location}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.location}</TableMobile.Cell>
            </TableMobile.Row>
            <TableMobile.Row>
              <TableMobile.Cell>{languagePage.table.columns.connection}</TableMobile.Cell>
              <TableMobile.Cell>{monitoring.connection}</TableMobile.Cell>
            </TableMobile.Row>
          </TableMobile.Item>
        ))}
      </Table.Mobile>

      <Table.Paginate
        status={systemLoading.state.loading}
        currentPage={searchFields.page}
        lastPage={statePage.lastPage}
        handleChangePage={(page) => {
          pageMonitoringCookie.set({ page })
          methodPage.getData()
        }}
      />
    </Table.Root>
  )
}

const AlarmCount: FC = () => {
  const { monitoringCount } = useStatePageMonitoring()

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 w-full gap-4">
      {monitoringCount.map(({ slug, status, count }) => {
        const { icon, iconClassName } =
          mapAlarmStatusBySlug[slug] || mapAlarmStatusBySlug['desativado']

        return (
          <div
            className={cn('flex items-center mx-auto gap-2', iconClassName)}
            key={slug}
            title={status}
          >
            <Icon
              icon={icon as IconName}
              height="50"
              width="50"
              viewBox="0 0 25 25"
              className="min-w-[50px] min-h-[50px]"
            />
            <span className="hidden xl:block">{status}</span>
            <span className="text-xl ">{count}</span>
          </div>
        )
      })}
    </div>
  )
}

const ModalMeasurementCount: FC = () => {
  const [openDialog, setOpenDialog] = useState(false)
  const languagePage = useLanguagePage()
  const statePage = useStatePageMonitoring()
  const methodPage = useMethodPage()
  const systemLoading = useSystemLoadingStore()
  const fields = useFieldsModalMeasurementCount()
  const listTypeSubTypeGrouped = listTypeSubTypeGroupedCookie.get()
  const searchFields = pageMonitoringCookie.get()

  useEffect(() => {
    if (openDialog) {
      pageMonitoringCookie.set({
        modalMeasurementCount: {
          ...searchFields,
          statusId: searchFields.statusId,
          typeId: searchFields.typeId,
          q: searchFields.q
        }
      })

      statePage.setModalMeasurementCount({
        items: statePage.items,
        lastPage: statePage.lastPage
      })
      return
    }
    fields.reset()
  }, [openDialog])

  const onSubmit = async () => {
    const result = await methodPage.createMonitoringMeasurementCount({
      initialDate: fields.values.initialDate,
      finalDate: fields.values.finalDate,
      equipmentIds: fields.values.equipments
    })

    if (result) setOpenDialog(false)
  }

  const handleInputMonitoring = useDebounceFunction(() => {
    methodPage.getMonitoring()
  }, 250)

  const handleCheckedAll = (checked: boolean) => {
    const equipmentIdsOld = fields.values.equipments
    const equipmentIdsNew = statePage.modalMeasurementCount.items.map(
      (el) => el.equipmentId!
    )

    const equipmentIdsMerge = checked
      ? Array.from(new Set([...equipmentIdsOld, ...equipmentIdsNew]))
      : equipmentIdsOld.filter((id) => !equipmentIdsNew.includes(id))

    fields.setValue('equipments', equipmentIdsMerge)
  }

  const updateMonitoringFilter = (
    updatedFields: Partial<typeof searchFields.modalMeasurementCount>
  ) => {
    pageMonitoringCookie.set({
      modalMeasurementCount: {
        ...searchFields.modalMeasurementCount,
        ...updatedFields
      }
    })
    handleInputMonitoring()
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger
        type="button"
        title={languagePage.table.modalMeasurementCount.title}
      >
        <Button variant="icon-only">
          <Button.Icon>
            <Icon
              icon="refreshccw05"
              width="24"
              height="24"
              className="icon-menu-primary"
            />
          </Button.Icon>
        </Button>
      </Dialog.Trigger>

      <Dialog.Content className="max-w-5xl max-h-[96vh] overflow-y-scroll">
        <Dialog.Header>
          <Dialog.Title>
            {languagePage.table.modalMeasurementCount.title}
          </Dialog.Title>
        </Dialog.Header>

        <Dialog.Description hidden />

        <form
          onSubmit={fields.handleSubmit(onSubmit)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault()
            }
          }}
          className="flex flex-col lg:flex-row gap-4"
          id="measurementCounts-form"
        >
          <div className="flex-1">
            <div
              className={cn(
                'duration-200',
                fields.errors.equipments?.message
                  ? 'h-fit'
                  : 'h-0 overflow-hidden'
              )}
            >
              {fields.errors.equipments?.message}
            </div>

            <Table.Root classNameWrapper="block">
              <Table.Header>
                <Table.Row>
                  <Table.Head>
                    <Checkbox.Root size="sm">
                      <Checkbox.Content
                        onCheckedChange={(checked) =>
                          handleCheckedAll(!!checked)
                        }
                      />
                    </Checkbox.Root>
                  </Table.Head>
                  <Table.Head>
                    {languagePage.table.modalMeasurementCount.table.name}
                  </Table.Head>
                  <Table.Head>
                    {languagePage.table.modalMeasurementCount.table.company}
                  </Table.Head>
                </Table.Row>
              </Table.Header>

              <Table.Body>
                {statePage.modalMeasurementCount.items.map((item) => (
                  <Table.Row key={item.equipmentId}>
                    <Table.Cell>
                      <Checkbox.Root size="sm">
                        <Checkbox.Content
                          checked={fields.values.equipments.includes(
                            item.equipmentId
                          )}
                          onCheckedChange={(checked) => {
                            checked
                              ? fields.handleEquipmentIdsAdd(item.equipmentId)
                              : fields.handleEquipmentIdsRemove(
                                item.equipmentId
                              )
                          }}
                        />
                      </Checkbox.Root>
                    </Table.Cell>
                    <Table.Cell>{item.equipmentName}</Table.Cell>
                    <Table.Cell>{item.companyName}</Table.Cell>
                  </Table.Row>
                ))}
                <Table.RowLoading
                  status={systemLoading.state.loading}
                  colSpan={3}
                  className="min-h-min"
                />
              </Table.Body>

              <Table.Paginate
                status={systemLoading.state.loading}
                lastPage={statePage.modalMeasurementCount.lastPage}
                currentPage={searchFields.modalMeasurementCount.page!}
                handleChangePage={(page) => {
                  updateMonitoringFilter({
                    page
                  })
                }}
              />
            </Table.Root>
          </div>

          <div className="lg:w-[325px]">
            <section className="flex flex-col gap-4 mb-5">
              <h3 className="text-2xl">
                {languagePage.table.modalMeasurementCount.search.title}
              </h3>
              <TagInput.Root>
                <TagInput.Label htmlFor="companies">
                  {languagePage.table.modalMeasurementCount.search.inputStatus}
                </TagInput.Label>
                <TagInput.Content
                  placeholder={languagePage.table.search.fieldStatus}
                  value={
                    searchFields.modalMeasurementCount.statusId
                      ? String(searchFields.modalMeasurementCount.statusId)
                      : ''
                  }
                  onChange={(items) =>
                    updateMonitoringFilter({
                      statusId: Number(items?.[0]?.value),
                      page: 1
                    })
                  }
                  options={alarmStatusOptions}
                  disabled={systemLoading.state.loading}
                />
              </TagInput.Root>

              <TagInput.Root className="min-w-[200px]">
                <TagInput.Content
                  placeholder={languagePage.table.search.fieldType}
                  name={languagePage.table.search.fieldType}
                  value={`${searchFields.modalMeasurementCount.typeId}.${searchFields.modalMeasurementCount.subtypeId}`}
                  onChange={(args: unknown) => {
                    const { typeId, subtypeId } = handleTypeChange(args)
                    updateMonitoringFilter({
                      typeId,
                      subtypeId,
                      page: 1
                    })
                  }}
                  options={listTypeSubTypeGrouped.list}
                  disabled={systemLoading.state.loading}
                />
              </TagInput.Root>
              <Input.Root>
                <Input.Content
                  slotStart={
                    <Icon
                      icon="searchLg"
                      className="icon-menu-primary"
                      height="24"
                      width="24"
                      viewBox="0 0 20 20"
                    />
                  }
                  placeholder={languagePage.table.search.fieldQuery}
                  type="text"
                  defaultValue={searchFields.modalMeasurementCount?.q ?? ''}
                  onChange={(e) => {
                    pageMonitoringCookie.set({
                      modalMeasurementCount: {
                        ...searchFields.modalMeasurementCount,
                        q: e.target.value,
                        page: 1
                      }
                    })
                  }}
                  onKeyUp={(e) =>
                    handleKeyEnter(e.key, methodPage.getMonitoring)
                  }
                />
              </Input.Root>
            </section>

            <section className="flex flex-col mt-10 gap-4">
              <h3 className="text-2xl">
                {languagePage.table.modalMeasurementCount.form.title}
              </h3>
              <InputDate.Root>
                <InputDate.Label>
                  {
                    languagePage.table.modalMeasurementCount.form
                      .inputInitialDate
                  }
                </InputDate.Label>
                <InputDate.Date
                  onChange={(value) => {
                    fields.setValue('initialDate', value.dateFormated)
                  }}
                  helperTextProps={{
                    className: 'text-comerc-error-500 text-[14px]'
                  }}
                  selected={fields.values.initialDate}
                  disabled={systemLoading.state.loading}
                  helperText={fields.errors.initialDate?.message}
                />
              </InputDate.Root>
              <InputDate.Root>
                <InputDate.Label>
                  {languagePage.table.modalMeasurementCount.form.inputFinalDate}
                </InputDate.Label>
                <InputDate.Date
                  onChange={(value) => {
                    const isValid = validateFinalDate(
                      fields.values.initialDate,
                      value.dateFormated
                    )
                    if (!isValid) {
                      fields.setError('finalDate', {
                        message:
                          'A data final não pode exceder um mês da data inicial.'
                      })
                      return
                    }
                    fields.clearErrors('finalDate')
                    fields.setValue('finalDate', value.dateFormated)
                  }}
                  selected={fields.values.finalDate}
                  disabled={systemLoading.state.loading}
                  helperText={fields.errors.finalDate?.message}
                  helperTextProps={{
                    className: 'text-comerc-error-500 text-[14px]'
                  }}
                />
              </InputDate.Root>
            </section>
          </div>
        </form>
        <Dialog.Footer>
          <Button
            type="button"
            variant="secondary-gray"
            onClick={() => setOpenDialog(false)}
          >
            {languagePage.table.cancel}
          </Button>
          <Button type="submit" variant="primary" form="measurementCounts-form">
            {languagePage.table.confirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}

const useFieldsModalMeasurementCount = () => {
  const languagePage = useLanguagePage()

  const formSchema = z.object({
    initialDate: z
      .string({ message: languagePage.table.validationFields.requiredField })
      .min(1, languagePage.table.validationFields.requiredField),
    finalDate: z
      .string({ message: languagePage.table.validationFields.requiredField })
      .min(1, languagePage.table.validationFields.requiredField),
    equipments: z
      .array(z.number())
      .min(
        1,
        languagePage.table.validationFields.MustContainLeastItems(
          1,
          languagePage.table.modalMeasurementCount.table.MustContainLeastText
        )
      )
  })
  const {
    handleSubmit,
    setValue,
    watch,
    setError,
    clearErrors,
    formState: { errors },
    reset
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      initialDate: undefined,
      finalDate: undefined,
      equipments: []
    }
  })

  const values = watch()

  const handleEquipmentIdsAdd = (id: number) => {
    const equipments = [...values.equipments, id]
    setValue('equipments', equipments)
  }
  const handleEquipmentIdsRemove = (id: number) => {
    const equipments = values.equipments.filter((el) => el !== id)
    setValue('equipments', equipments)
  }

  return {
    handleSubmit,
    setValue,
    setError,
    values,
    errors,
    clearErrors,
    handleEquipmentIdsAdd,
    handleEquipmentIdsRemove,
    reset
  }
}

const ModalMonitoringDownload: FC = () => {
  const [openDialog, setOpenDialog] = useState(false)
  const languagePage = useLanguagePage()
  const fields = useFieldsModalMonitoringDownload()
  const methodPage = useMethodPage()

  const onSubmit = ({ detailedVersion }: { detailedVersion: boolean }) => {
    methodPage.downloadMonitoring(detailedVersion)
  }

  return (
    <Dialog.Root open={openDialog} onOpenChange={setOpenDialog}>
      <Dialog.Trigger
        type="button"
        title={languagePage.table.modalMonitoringDownload.title}
      >
        <Button variant="icon-only">
          <Button.Icon>
            <Icon
              icon="file"
              width="24"
              height="24"
              className="icon-menu-primary"
            />
          </Button.Icon>
        </Button>
      </Dialog.Trigger>

      <Dialog.Content>
        <Dialog.Header>
          <Dialog.Title>
            {languagePage.table.modalMonitoringDownload.title}
          </Dialog.Title>
        </Dialog.Header>

        <Dialog.Description hidden />

        <form
          onSubmit={fields.handleSubmit(onSubmit)}
          id="monitoringDownload-form"
        >
          <RadioGroup.Root
            defaultValue="option-one"
            className="flex items-center pr-40"
            onValueChange={(value) =>
              fields.setValue('detailedVersion', value === 'option-two')
            }
          >
            <RadioGroup.Content
              value="option-one"
              id={
                languagePage.table.modalMonitoringDownload.form.inputProbeList
              }
            />
            <RadioGroup.Label
              htmlFor={
                languagePage.table.modalMonitoringDownload.form.inputProbeList
              }
            >
              {languagePage.table.modalMonitoringDownload.form.inputProbeList}
            </RadioGroup.Label>

            <RadioGroup.Content
              className="ml-auto"
              value="option-two"
              id={
                languagePage.table.modalMonitoringDownload.form.inputCountList
              }
            />
            <RadioGroup.Label
              htmlFor={
                languagePage.table.modalMonitoringDownload.form.inputCountList
              }
            >
              {languagePage.table.modalMonitoringDownload.form.inputCountList}
            </RadioGroup.Label>
          </RadioGroup.Root>
        </form>
        <Dialog.Footer>
          <Button
            type="button"
            variant="secondary-gray"
            onClick={() => setOpenDialog(false)}
          >
            {languagePage.table.cancel}
          </Button>
          <Button
            type="submit"
            variant="primary"
            form="monitoringDownload-form"
          >
            {languagePage.table.confirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}

const useFieldsModalMonitoringDownload = () => {
  const schema = z.object({
    detailedVersion: z.boolean()
  })

  const { register, handleSubmit, setValue, watch } = useForm({
    resolver: zodResolver(schema),
    defaultValues: { detailedVersion: false }
  })

  return {
    register,
    handleSubmit,
    setValue,
    watch
  }
}
