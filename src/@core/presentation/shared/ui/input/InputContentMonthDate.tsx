import { format, parse } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { FC, ReactNode, useState } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { CalendarMonth } from '../calendar'

interface InputContentMonthDateProps {
  value?: Date
  onChange: (val: Date | undefined) => void
  slotStart?: ReactNode
  slotEnd?: ReactNode
  className?: string
  disabled?: boolean
  helperText?: string
  helperTextProps?: { className?: string }
}

export const InputContentMonthDate: FC<InputContentMonthDateProps> = ({
  value: month,
  onChange,
  slotStart,
  slotEnd,
  disabled = false,
  helperText,
  helperTextProps
}) => {
  const [open, setOpen] = useState(false)

  const label = month
    ? format(month, 'LLLL yyyy', { locale: ptBR })
    : 'Selecione um mês'

  return (
    <>
      <Dialog.Root open={open} onOpenChange={setOpen}>
        <Dialog.Trigger asChild>
          <button
            className={cn(
              'border rounded flex items-center gap-2 w-full h-10 px-3',
              { 'text-gray-400 bg-gray-100': disabled }
            )}
            disabled={disabled}
          >
            {slotStart}
            <span className="flex-1 text-left">{label}</span>
            {slotEnd}
          </button>
        </Dialog.Trigger>

        <Dialog.Content forceMount size="sm">
          <CalendarMonth
            value={
              month ? format(month, 'yyyy-MM-dd', { locale: ptBR }) : undefined
            }
            onChange={(iso) => {
              const dt = parse(iso, 'yyyy-MM-dd', new Date())
              onChange(dt)
              setOpen(false)
            }}
          />
        </Dialog.Content>
      </Dialog.Root>

      {helperText && (
        <span
          data-inputtext
          className={cn('text-sm text-red-500', helperTextProps?.className)}
        >
          {helperText}
        </span>
      )}
    </>
  )
}
