import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Calendar } from '@/@core/presentation/shared/ui/calendar'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { TimePickerProps } from '@mui/x-date-pickers'
import { Dayjs } from 'dayjs'
import { FC, ReactNode, useState } from 'react'

interface InputContentDateProps {
  value?: Date | undefined
  onChange: (val: Date | undefined) => void
  children?: ReactNode
  slotStart?: ReactNode
  slotEnd?: ReactNode
  className?: string
  disabled?: TimePickerProps<Dayjs>['disabled']
  helperText?: string
  helperTextProps?: {
    className?: string
  }
  textClassName?: string
}
export const InputContentDate: FC<InputContentDateProps> = ({
  value: month,
  onChange,
  children,
  slotStart,
  slotEnd,
  className,
  textClassName,
  disabled = false,
  helperText,
  helperTextProps,
  ...rest
}) => {
  const [open, setOpen] = useState(false)

  // rest.

  return (
    <>
      <Dialog.Root open={open} onOpenChange={setOpen}>
        <Dialog.Trigger asChild>
          <button
            className={cn(
              'border-[1px] rounded-[8px]',
              'w-full flex items-center gap-2',
              'h-[40px] px-[12px] py-[8px]',
              { 'text-comerc-gray-500 bg-comerc-grayLight-50': disabled },
              'dark:text-comerc-grayLight-50',
              className
            )}
          >
            {slotStart}
            <span className={cn('inline-block w-full', textClassName)}>
              {children}
            </span>
            {slotEnd}
          </button>
        </Dialog.Trigger>

        <Dialog.Content size="sm">
          <Dialog.Title hidden />
          <Dialog.Description hidden />

          <Calendar
            mode="single"
            onSelect={(val) => {
              onChange(val)
              setOpen(false)
            }}
            month={month}
            selected={month}
          />
        </Dialog.Content>
      </Dialog.Root>

      {helperText && (
        <span
          data-inputtext
          className={cn(
            'text-[16px] leading-6 font-normal',
            helperTextProps?.className
          )}
        >
          {helperText}
        </span>
      )}
    </>
  )
}
