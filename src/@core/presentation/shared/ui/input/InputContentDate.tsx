import { Dayjs } from 'dayjs'
import { FC, ReactNode, useState } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import { Calendar } from '@/@core/presentation/shared/ui/calendar'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import {
  Icon as IconOld,
  faXmarkSolid
} from '@/@core/presentation/shared/ui/icon'
import { TimePickerProps } from '@mui/x-date-pickers'

interface InputContentDateProps {
  value?: Date | undefined
  onChange: (val: Date | undefined) => void
  children?: ReactNode
  slotStart?: ReactNode
  slotEnd?: ReactNode
  className?: string
  disabled?: TimePickerProps<Dayjs>['disabled']
  helperText?: string
  helperTextProps?: {
    className?: string
  }
  textClassName?: string
  isClearable?: boolean
}
export const InputContentDate: FC<InputContentDateProps> = ({
  value: month,
  onChange,
  children,
  slotStart,
  slotEnd,
  className,
  textClassName,
  disabled = false,
  helperText,
  helperTextProps,
  isClearable,
  ...rest
}) => {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Dialog.Root open={open} onOpenChange={setOpen}>
        <div
          className={cn(
            'w-full flex items-center gap-2',
            'border-[1px] dark:border-comerc-grayLight-800 rounded-[8px]',
            'h-[40px] px-[12px]'
          )}
        >
          <Dialog.Trigger asChild>
            <button
              className={cn(
                'w-full flex items-center gap-2 rounded-[8px]',
                'h-[40px] py-[8px]',
                { 'text-comerc-gray-500 ': disabled },
                'dark:text-comerc-grayLight-50',
                className
              )}
            >
              {slotStart}
              <span
                className={cn(
                  'inline-block w-full text-comerc-grayLight-500 dark:text-comerc-grayLight-400',
                  textClassName
                )}
              >
                {children}
              </span>
              {slotEnd}
            </button>
          </Dialog.Trigger>

          {!!(isClearable && month) && (
            <span
              className="w-[20px] h-[20px] flex [&>*]:m-auto cursor-pointer text-sm text-comerc-grayLight-500"
              onClick={() => onChange(undefined)}
            >
              <IconOld icon={faXmarkSolid} className="text-lg font-bold" />
            </span>
          )}
        </div>

        <Dialog.Content size="sm">
          <Dialog.Title hidden />
          <Dialog.Description hidden />

          <Calendar
            mode="single"
            onSelect={(val) => {
              onChange(val)
              setOpen(false)
            }}
            month={month}
            selected={month}
          />
        </Dialog.Content>
      </Dialog.Root>

      {helperText && (
        <span
          data-inputtext
          className={cn(
            'text-[13px] leading-6 font-normal text-comerc-error-400',
            helperTextProps?.className
          )}
        >
          {helperText}
        </span>
      )}
    </>
  )
}
