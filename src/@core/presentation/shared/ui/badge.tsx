import { cva, type VariantProps } from 'class-variance-authority'
import React, { FC } from 'react'

import { cn } from '@/@core/framework/plugins/shadcn/utils'

const badgeRootVariants = cva(
  [
    'flex items-center gap-2 justify-center',
    'rounded-full font-acuminPro-Regular border-[1px]'
  ],
  {
    variants: {
      variant: {
        default:
          'bg-comerc-gray-100 border-comerc-gray-100 text-comerc-gray-700',
        'default-outline':
          'bg-transparent border-comerc-gray-800 text-comerc-gray-700',
        'default-filled':
          'bg-comerc-gray-700 border-comerc-gray-700 text-white',

        success:
          'bg-comerc-vibra-brandComerc-50 border-comerc-vibra-brandComerc-200 text-comerc-vibra-brandComerc-700',
        'success-outline':
          'bg-transparent border-comerc-success-800 text-comerc-success-700',
        'success-filled':
          'bg-comerc-success-600 border-comerc-success-600 text-white',

        error:
          'bg-comerc-error-50 border-comerc-error-50 text-comerc-error-700',
        'error-outline':
          'bg-transparent border-comerc-error-600 text-comerc-error-700',
        'error-filled':
          'bg-comerc-error-600 border-comerc-error-600 text-white',

        alert:
          'bg-comerc-warning-50 border-comerc-warning-200 text-comerc-warning-700',
        'warning-outline':
          'bg-transparent border-comerc-warning-600 text-comerc-warning-700',
        'warning-filled':
          'bg-comerc-warning-600 border-comerc-warning-600 text-white',

        processing:
          'bg-comerc-tertiary-50 border-comerc-tertiary-50 text-comerc-tertiary-700',
        'processing-outline':
          'bg-transparent border-comerc-tertiary-600 text-comerc-tertiary-700',
        'processing-filled':
          'bg-comerc-tertiary-600 border-comerc-tertiary-600 text-white'
      },
      size: {
        sm: 'py-[2px] px-[8px] text-[12px] leading-[18px]',
        md: 'py-[2px] px-[10px] text-[14px] leading-[20px]',
        lg: 'py-[4px] px-[12px] text-[16px] leading-[24px]'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'md'
    }
  }
)
export interface BadgeRootProps
  extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof badgeRootVariants> { }
const BadgeRoot: FC<BadgeRootProps> = (args) => {
  const { variant, className, size, ...props } = args
  return (
    <div
      className={cn(badgeRootVariants({ variant, size }), className)}
      {...props}
    />
  )
}

const BadgeContent: FC<React.HTMLAttributes<HTMLDivElement>> = ({
  children,
  className
}) => {
  return <div className={cn('text-center', className)}>{children}</div>
}

export interface BadgeIconProps {
  children: React.ReactNode
  className?: string
}
const BadgeIcon: React.FC<BadgeIconProps> = ({ children, className }) => {
  return (
    <span className={cn('inline-flex [&>*]:m-auto', className)}>
      {children}
    </span>
  )
}

const Badge = {
  Root: BadgeRoot,
  Content: BadgeContent,
  Icon: BadgeIcon
}

export { Badge }
