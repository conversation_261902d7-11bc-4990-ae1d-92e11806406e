import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { forwardRef, useImperativeHandle, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Button } from '@/@core/presentation/shared/ui/button'
import { CalendarMonth } from '@/@core/presentation/shared/ui/calendar'
import { Checkbox } from '@/@core/presentation/shared/ui/checkbox'
import { defineValuesFormFields } from '@/@core/utils/formFields'

import { useLanguageModal, useMethodsModal } from './Modal.hooks'
import {
  IApportionmentExportDataModalProps,
  IApportionmentExportDataModalRef,
  IApportionmentExportDataValues
} from './Modal.types'

export const ApportionmentExportDataModal = forwardRef<
  IApportionmentExportDataModalRef,
  IApportionmentExportDataModalProps
>((props, ref) => {
  const modalRef = useRef<IModalRootRef>(null)

  const systemLoading = useSystemLoadingStore()

  const languageModal = useLanguageModal()
  const methodsModal = useMethodsModal()

  const formFields = useFormFields()

  const handleOpen = () => {
    formFields.reset()
    modalRef.current?.open()
  }
  const handleClose = () => {
    formFields.reset()
    modalRef.current?.close()
  }

  const onSubmit = async () => {
    const status = await methodsModal.handleSubmit({
      apportionmentId: props.apportionmentId,
      date: formFields.values.date,
      sendCostCenters: formFields.values.sendCostCenters
    })

    if (!status) return

    handleClose()
  }

  useImperativeHandle(ref, () => ({
    open: handleOpen,
    close: handleClose
  }))

  return (
    <Modal.Root ref={modalRef}>
      <Modal.Title>{languageModal.title}</Modal.Title>

      <Modal.Content>
        <pre className="max-h-[200px] border overflow-y-scroll hidden">
          {JSON.stringify(formFields.values, null, 2)}
        </pre>

        <form
          onSubmit={formFields.handleSubmit(onSubmit)}
          id="formApportionmentExportDataModal"
          className="flex flex-col items-center gap-4"
        >
          {props.hasPeriods && (
            <>
              <CalendarMonth
                defaultEnabled={false}
                className="min-h-[300px]"
                allowedDates={props.periods}
                onChange={(dateString) =>
                  formFields.setValue('date', dateString)
                }
              />

              <Checkbox.Root className="min-h-[32px]">
                <Checkbox.Content
                  id="sendCostCenters"
                  checked={formFields.values.sendCostCenters}
                  onCheckedChange={() =>
                    formFields.setValue(
                      'sendCostCenters',
                      !formFields.values.sendCostCenters
                    )
                  }
                  disabled={systemLoading.state.modalLoading}
                />
                <Checkbox.Label
                  htmlFor="sendCostCenters"
                  className="cursor-pointer"
                >
                  <span className="text-[16px]">
                    {languageModal.form.input.sendCostCenters}
                  </span>
                </Checkbox.Label>
              </Checkbox.Root>
            </>
          )}
          {!props.hasPeriods && (
            <div
              className={cn(
                'flex items-center gap-2 border rounded-xl',
                'p-3 mt-3'
              )}
            >
              <Badge.Root variant="warning-outline">
                <Badge.Content>
                  {languageModal.form.warningWithoutPeriod}
                </Badge.Content>
              </Badge.Root>
              {languageModal.form.textWithoutPeriod}
            </div>
          )}
        </form>
      </Modal.Content>
      <Modal.Footer>
        <Button
          type="button"
          disabled={systemLoading.state.modalLoading}
          onClick={handleClose}
        >
          {languageModal.form.btn.cancel}
        </Button>
        {props.hasPeriods && (
          <Button
            type="submit"
            variant="primary"
            form="formApportionmentExportDataModal"
            disabled={
              !formFields.values.date || systemLoading.state.modalLoading
            }
          >
            {languageModal.form.btn.confirm}
          </Button>
        )}
      </Modal.Footer>
    </Modal.Root>
  )
})

const formValuesExportDataInitial: IApportionmentExportDataValues = {
  date: '',
  sendCostCenters: false
}
const useFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageModal()

  const {
    formState: { errors },
    ...form
  } = useForm({
    resolver: zodResolver(
      z.object({
        date: z.string().min(1, { message }),
        sendCostCenters: z.boolean()
      })
    ),
    defaultValues: { ...formValuesExportDataInitial }
  })

  const values = form.watch()

  const setValues = (payload: Partial<IApportionmentExportDataValues>) => {
    defineValuesFormFields(form.setValue, payload, formValuesExportDataInitial)
  }

  return { ...form, values, errors, setValues }
}
