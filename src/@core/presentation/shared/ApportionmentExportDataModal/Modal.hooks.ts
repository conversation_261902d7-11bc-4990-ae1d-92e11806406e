import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { apportionmentsExportExcelApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { ISubmitPayload } from './Modal.types'

export const useMethodsModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const languageModal = useLanguageModal()

  const handleSubmit = async (payload: ISubmitPayload) => {
    const {
      form: { messages }
    } = languageModal

    try {
      systemLoading.setData({ modalLoading: true })

      const {
        status,
        data: [message]
      } = await apportionmentsExportExcelApiV3(http).create(payload)

      const conditionalRequest = status === 200

      systemToast.addToast({
        message
      })

      return conditionalRequest
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          '@core/presentation/shared/ApportionmentExportDataModal/Modal/handleSubmit'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })

      return false
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  return { handleSubmit }
}

export const useLanguageModal = () => {
  const { lang } = useSystemLanguageStore().state

  const { validationFields, btn, modal } = languageByMode(lang)
  const { cancel, confirm } = btn
  const { requiredField } = validationFields
  const { title, form } = modal.apportionmentExportData

  return {
    title,
    form: {
      input: {
        date: form.input.date,
        sendCostCenters: form.input.sendCostCenters
      },
      messages: form.messages,
      warningWithoutPeriod: form.warningWithoutPeriod,
      textWithoutPeriod: form.textWithoutPeriod,
      requiredField,
      btn: { cancel, confirm }
    }
  }
}
