export interface IModalData {
  tab: string
  costCenter: Partial<ICostCenterData>
}
export interface ICostCenterData {
  id: number
  name: string
  email: string
  apportionmentId: number
  apportionmentTariffTypeId: number
  companyId: number
}
export interface ICostCenterModalRef {
  handler?: (data: Partial<ICostCenterData>) => void
  open?: () => void
  close?: () => void
}
export interface ICostCenterModalProps {
  tabDataSubmitSuccess?: () => Promise<void>
  handleCreatedTabData?: () => Promise<void>
  apportionmentId: number
}
