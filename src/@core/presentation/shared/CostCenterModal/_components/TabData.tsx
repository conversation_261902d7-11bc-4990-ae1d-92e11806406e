import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { forwardRef, useEffect, useImperativeHandle } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useListApportionmentTariffTypes from '@/@core/framework/store/hook/useListApportionmentTariffTypes'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Checkbox } from '@/@core/presentation/shared/ui/checkbox'
import { Input } from '@/@core/presentation/shared/ui/input'
import { defineValuesFormFields } from '@/@core/utils/formFields'

import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { ICostCenterData } from '../Modal.types'
import {
  useLanguageTabDataModal,
  useMethodsTabDataModal
} from './TabData.hooks'
import { ITabDataProps, ITabDataRef, ITabDataValues } from './TabData.types'

export type { ITabDataProps, ITabDataRef, ITabDataValues }

const formValuesTabDataInitial: ITabDataValues = {
  id: null,
  name: '',
  email: '',
  apportionmentTariffTypeId: null
}

export const TabData = forwardRef<ITabDataRef, ITabDataProps>((props, ref) => {
  const systemLoading = useSystemLoadingStore()
  const systemStore = useSystemStore()

  const languageTabModal = useLanguageTabDataModal()
  const methodsTabModal = useMethodsTabDataModal()

  const formFields = useTabDataFormFields()

  const listApportionmentTariffTypes = useListApportionmentTariffTypes()

  const handler = async (values: Partial<ICostCenterData>) => {
    defineValuesFormFields(
      formFields.setValue,
      { ...values },
      formValuesTabDataInitial
    )

    if (systemStore.state.mountComponent?.['costCenter-tabData']) return

    await methodsTabModal.getDependencies()

    systemStore.setMountComponent('costCenter-tabData')
  }

  useImperativeHandle(ref, () => ({
    handler: handler
  }))

  useEffect(() => {
    if (!!props.costCenter.id) handler(props.costCenter)
  }, [])

  const onSubmit = async () => {
    const payload = {
      id: formFields.values.id,
      name: formFields.values.name,
      email: formFields.values.email,
      apportionmentId: props.apportionmentId,
      apportionmentTariffTypeId: Number(
        formFields.values.apportionmentTariffTypeId
      )
    }
    const { status, data } = await methodsTabModal.handleSubmit(payload)

    /** error */
    if (!status) return

    if (!formFields.values.id) {
      data?.id && formFields.setValues({ id: data.id })

      data && props?.handleCreated?.(data)
    }
  }

  return (
    <>
      <form
        onSubmit={formFields.handleSubmit(onSubmit)}
        className="grid gap-2"
        id="form-tabData"
      >
        <Input.Root>
          <Input.Label htmlFor="name">
            {languageTabModal.form.input.inputName}
          </Input.Label>

          <Input.Content
            id="name"
            name="name"
            value={formFields.values.name}
            onChange={({ target }) => formFields.setValue('name', target.value)}
            disabled={systemLoading.state.modalLoading}
            helperText={formFields.errors.name?.message}
          />
        </Input.Root>

        <Input.Root>
          <Input.Label htmlFor="email">
            {languageTabModal.form.input.inputEmail}
          </Input.Label>

          <Input.Content
            id="email"
            name="email"
            value={formFields.values.email}
            onChange={({ target }) =>
              formFields.setValue('email', target.value)
            }
            disabled={systemLoading.state.modalLoading}
            helperText={formFields.errors.email?.message}
          />
        </Input.Root>

        <div className="grid gap-[6px]">
          <Input.Label>
            {languageTabModal.form.input.inputTariffType}
          </Input.Label>

          <Checkbox.List
            className="min-h-4"
            helperText={formFields.errors.apportionmentTariffTypeId?.message}
          >
            {listApportionmentTariffTypes.state.list.map((item) => (
              <Checkbox.Root key={item.id} className="space-x-1">
                <Checkbox.Content
                  name="apportionmentTariffTypeId"
                  id={`checkbox-key-${item.id.toString()}`}
                  checked={
                    formFields.values?.apportionmentTariffTypeId === item.id
                  }
                  onCheckedChange={(e) => {
                    formFields.setValue('apportionmentTariffTypeId', item.id)
                  }}
                />
                <Checkbox.Label htmlFor={`checkbox-key-${item.id.toString()}`}>
                  {item.name}
                </Checkbox.Label>
              </Checkbox.Root>
            ))}
          </Checkbox.List>
        </div>

        {!systemLoading.state.modalLoading &&
          !listApportionmentTariffTypes.state.list.length && (
            <p>{languageTabModal.form.input.inputTariffType}</p>
          )}
      </form>

      <div className="footer-form">
        <Button type="button" onClick={() => props?.btnCancel?.()}>
          {languageTabModal.form.btn.cancel}
        </Button>
        <Button type="submit" variant="primary" form="form-tabData">
          {languageTabModal.form.btn.save}
        </Button>
      </div>
    </>
  )
})

const useTabDataFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabDataModal()

  const {
    formState: { errors, isSubmitting },
    ...form
  } = useForm({
    resolver: zodResolver(
      z
        .object({
          id: z.number().nullable(),
          name: z.string().min(1, { message }),
          email: z.string().min(1, { message }),
          apportionmentTariffTypeId: z.number().nullable()
        })
        .superRefine((values, ctx) => {
          if (!values.apportionmentTariffTypeId)
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message,
              path: ['apportionmentTariffTypeId']
            })
        })
    ),
    defaultValues: { ...formValuesTabDataInitial }
  })
  const values = form.watch()

  const setValues = (payload: Partial<ITabDataValues>) => {
    defineValuesFormFields(form.setValue, payload, formValuesTabDataInitial)
  }

  return { ...form, values, errors, isSubmitting, setValues }
}
