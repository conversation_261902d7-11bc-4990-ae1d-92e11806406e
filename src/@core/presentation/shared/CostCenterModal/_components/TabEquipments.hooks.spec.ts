import '__mock__/logging/logger'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { costCenterEquipmentResponseMock1 } from '@/__mock__/content/api-cost-centers-equipments'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { renderHookWithRedux } from '@/utils/setupTest'
import { renderHook, waitFor } from '@testing-library/react'
import {
  formDataOutput,
  useLanguageTabEquipmentsModal,
  useMethodsTabEquipmentsModal,
  useStateTabEquipmentsModal
} from './TabEquipments.hooks'

jest.mock('@/@core/infra/api/CostCentersEquipmentsApiV3')

const spyCostCentersEquipmentsApiV3 = jest.spyOn(
  require('@/@core/infra/api/CostCentersEquipmentsApiV3'),
  'costCentersEquipmentsApiV3'
)

describe('@core/presentation/shared/CostCenterModal/_components/TabEquipments | useMethodsTabEquipmentsModal', () => {
  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabEquipmentsModal(),
        method: useMethodsTabEquipmentsModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /* request error **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersEquipmentsApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.table.items).toHaveLength(0)

    /* request successful without data **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersEquipmentsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 204,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.table.items).toHaveLength(0)

    /* request success **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersEquipmentsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [costCenterEquipmentResponseMock1],
          total: 1,
          lastPage: 1
        }
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })
    expect(result.current.state.table.items).toHaveLength(1)
  })

  it('should check return the function handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabEquipmentsModal(),
        method: useMethodsTabEquipmentsModal(),
        toast: useSystemToastStore()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /* request error **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })
    spyCostCentersEquipmentsApiV3.mockImplementationOnce(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(1)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover equipamento do centro de custo'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request success **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })
    spyCostCentersEquipmentsApiV3.mockImplementationOnce(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(1)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Equipamento do centro de custo removido com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabEquipmentsModal(),
        method: useMethodsTabEquipmentsModal(),
        toast: useSystemToastStore()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    const payload = {
      id: null,
      costCenterId: 123,
      equipmentId: 1,
      usageLimit: 80,
      apportionmentGroupId: 2,
      costCompositionTypeId: 11
    }

    /* request CREATE error **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyCostCentersEquipmentsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao cadastrar equipamento do centro de custo'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request CREATE success **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyCostCentersEquipmentsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: costCenterEquipmentResponseMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Equipamento do centro de custo adicionado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')

    /* request UPDATE error **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyCostCentersEquipmentsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar equipamento do centro de custo'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE success **/
    await waitFor(() => {
      result.current.state.reset()
      result.current.toast.reset()
    })

    spyCostCentersEquipmentsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: costCenterEquipmentResponseMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Equipamento do centro de custo atualizado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })
})

describe('@core/presentation/shared/CostCenterModal/_components/TabEquipments | useLanguageTabEquipmentsModal', () => {
  it('check the form texts', () => {
    const { result } = renderHookWithRedux(() => ({
      language: useLanguageTabEquipmentsModal()
    }))

    expect(result.current.language.form.input).toEqual({
      composition: 'Composition',
      costCompositionType: 'Type',
      equipment: 'Equipment',
      group: 'Group'
    })
  })
})

describe('@core/presentation/shared/CostCenterModal/_components/TabEquipments | utils', () => {
  const dataInput = {
    id: 1,
    equipments: [{ id: 12, name: 'equipamento 12' }],
    costCompositionTypeId: '1',
    usageLimit: '',
    costCenterId: 25
  }
  const dataOutput = {
    costCenterId: 25,
    costCompositionTypeId: 1,
    equipmentId: 12,
    id: 1
  }

  it('should check return the function formDataOutput with full dataInpu', () => {
    const result = formDataOutput({
      ...dataInput,
      groups: [{ id: 1, name: 'group 1' }],
      usageLimit: ''
    })

    expect(result).toEqual({
      ...dataOutput,
      apportionmentGroupId: 1,
      usageLimit: 0
    })
  })

  it('should check return the function formDataOutput with partial dataInpu', () => {
    const result = formDataOutput({
      ...dataInput,
      groups: [],
      usageLimit: '80'
    })

    expect(result).toEqual({
      ...dataOutput,
      apportionmentGroupId: null,
      usageLimit: 80
    })
  })
})
