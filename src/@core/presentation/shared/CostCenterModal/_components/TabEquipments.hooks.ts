import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { costCentersEquipmentsApiV3 } from '@/@core/infra/api/CostCentersEquipmentsApiV3'
import { http } from '@/@core/infra/http'
import { tabCostCenterEquipmentsCookie } from '@/@core/infra/memory/cookie'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'

import {
  IStateTabEquipments,
  IStateTabEquipmentsData,
  ISubmitPayload,
  ITabEquipmentsValues
} from './TabEquipments.types'

const stateData: IStateTabEquipmentsData = {
  table: {
    items: [],
    lastPage: 0,
    total: 0
  }
}

export const useStateTabEquipmentsModal = create<IStateTabEquipments>(
  (set) => ({
    ...stateData,
    set: (initialData) => set((state) => ({ ...state, ...initialData })),
    reset: () => set((state) => ({ ...state, ...stateData }))
  })
)

export const useMethodsTabEquipmentsModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const stateModal = useStateTabEquipmentsModal()
  const languageModal = useLanguageTabEquipmentsModal()

  const fetchData = async () => {
    try {
      systemLoading.setData({ modalLoading: true })

      const search = tabCostCenterEquipmentsCookie.get()

      const { data } = await costCentersEquipmentsApiV3(http).get({
        ...search
      })

      stateModal.set({
        table: {
          items: data.items,
          total: data.total,
          lastPage: data.lastPage
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'ApportionmentCostCenterModal/_components/TabEquipments/handleSubmit'
      })

      systemToast.addToast({
        message: languageModal.errors.request,
        type: 'error'
      })
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleSubmit = async (payload: ISubmitPayload) => {
    const { messages } = languageModal.form

    try {
      const { status } = payload.id
        ? await costCentersEquipmentsApiV3(http).update(payload.id, payload)
        : await costCentersEquipmentsApiV3(http).create(payload)

      const conditionalRequest = [200, 201].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'ApportionmentCostCenterModal/_components/TabEquipments/handleSubmit'
      })

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id),
        type: 'error'
      })
      return { status: false }
    }
  }
  const handleDelete = async (costCenterEquipmentId: number) => {
    const { messages } = languageModal.table.modalDelete

    try {
      const { status } = await costCentersEquipmentsApiV3(http).delete(
        costCenterEquipmentId
      )

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(messages, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title:
          'ApportionmentCostCenterModal/_components/TabEquipments/handleDelete'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })
      return { status: false }
    }
  }

  return { fetchData, handleSubmit, handleDelete }
}
export const useLanguageTabEquipmentsModal = () => {
  const { lang } = useSystemLanguageStore().state

  const { validationFields, btn, tab, errors } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields
  const { form, table } = tab.costCenterTabEquipments

  return {
    form: {
      input: {
        equipment: form.input.equipment,
        costCompositionType: form.input.costCompositionType,
        composition: form.input.composition,
        group: form.input.group
      },
      messages: form.messages,
      requiredField,
      btn: { cancel, save, add, clean }
    },
    table: {
      columns: table.columns,
      modalDelete: {
        ...table.modalDelete,
        btn: { cancel, confirm }
      }
    },
    errors
  }
}

/** UTILS */
export const formDataOutput = (
  data: ITabEquipmentsValues & {
    costCenterId: number
  }
): ISubmitPayload => {
  return {
    id: data.id,
    equipmentId: data.equipments[0].id,
    costCompositionTypeId: Number(data.costCompositionTypeId),
    usageLimit: data.usageLimit ? Number(data.usageLimit) : 0,
    apportionmentGroupId: data.groups?.[0]?.id ?? null,
    costCenterId: data.costCenterId
  }
}
