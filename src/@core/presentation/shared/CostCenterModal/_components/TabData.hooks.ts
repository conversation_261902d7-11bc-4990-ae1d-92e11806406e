import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { costCentersApiV3 } from '@/@core/infra/api/CostCentersApiV3'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { ListApportionmentTariffTypesService } from '@/@core/services/listApportionmentTariffTypesService'
import { toastMessageSwitch, toastTypeSwitch } from '@/@core/utils/toast'

import { ISubmitPayload } from './TabData.types'

export const useMethodsTabDataModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const languageModal = useLanguageTabDataModal()

  const listApportionmentTariffTypesService =
    ListApportionmentTariffTypesService()

  const handleSubmit = async (payload: ISubmitPayload) => {
    const {
      form: { messages }
    } = languageModal

    try {
      systemLoading.setData({ modalLoading: true })

      const { status, data } = payload.id
        ? await costCentersApiV3(http).update(payload.id, payload)
        : await costCentersApiV3(http).create(payload)

      const conditionalRequest = [201, 200].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest, data }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'ApportionmentCostCenterModal/_components/TabData/handleSubmit'
      })

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id),
        type: 'error'
      })

      return { status: false, data: null }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const getDependencies = async () => {
    try {
      systemLoading.setData({ modalLoading: true })

      /** update */
      await listApportionmentTariffTypesService.handler()
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'CostCenterModal/_components/TabData/getDependencies'
      })
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  return { handleSubmit, getDependencies }
}
export const useLanguageTabDataModal = () => {
  const { lang } = useSystemLanguageStore().state

  const { validationFields, btn, tab } = languageByMode(lang)
  const { cancel, save } = btn
  const { requiredField } = validationFields
  const { title, form } = tab.costCenterTabData

  return {
    title,
    form: {
      input: {
        inputName: form.input.inputName,
        inputEmail: form.input.inputEmail,
        inputTariffType: form.input.inputTariffType
      },
      messages: form.messages,
      requiredField,
      btn: { cancel, save }
    }
  }
}
