import { ICostCenter } from '@/@core/domain/CostCenters'
import { ICostCenterData } from '../Modal.types'

export interface ITabDataRef {
  handler: (p: Partial<ICostCenterData>) => void
}
export interface ITabDataProps {
  costCenter: Partial<ICostCenterData>
  handleCreated?: (p: ICostCenter) => void
  btnCancel?: () => void
  apportionmentId: number
}
export interface ITabDataValues {
  id: number | null
  name: string
  email: string
  apportionmentTariffTypeId: number | null
}

export type ISubmitPayload = {
  id: number | null
  apportionmentId: number
  apportionmentTariffTypeId: number
  name: string
  email: string
}
