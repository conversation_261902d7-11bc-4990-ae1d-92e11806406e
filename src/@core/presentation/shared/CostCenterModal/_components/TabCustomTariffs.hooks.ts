import { create } from 'zustand'

import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { costCentersTariffsApiV3 } from '@/@core/infra/api/CostCentersTariffsApiV3'
import { http } from '@/@core/infra/http'
import { tabCustomTariffsCookie } from '@/@core/infra/memory/cookie'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'

import {
  IStateTabCustomTariffs,
  IStateTabCustomTariffsData,
  ISubmitPayload,
  ITabCustomTariffsValues
} from './TabCustomTariffs.types'

const stateData: IStateTabCustomTariffsData = {
  table: {
    items: [],
    lastPage: 0,
    total: 0
  }
}

export const useStateTabCustomTariffsModal = create<IStateTabCustomTariffs>(
  (set) => ({
    ...stateData,
    set: (initialData) => set((state) => ({ ...state, ...initialData })),
    reset: () => set((state) => ({ ...state, ...stateData }))
  })
)

export const useMethodsTabCustomTariffsModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const stateModal = useStateTabCustomTariffsModal()
  const languageModal = useLanguageTabCustomTariffsModal()

  const fetchData = async () => {
    try {
      systemLoading.setData({ modalLoading: true })

      const search = tabCustomTariffsCookie.get()

      const { data } = await costCentersTariffsApiV3(http).get({
        ...search,
        costCenterId: Number(search.costCenterId)
      })

      stateModal.set({
        table: {
          items: data.items,
          total: data.total,
          lastPage: data.lastPage
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'CostCenterModal/_components/TabCustomTariffs/fetchData'
      })
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleSubmit = async (payload: ISubmitPayload) => {
    const { messages } = languageModal.form

    try {
      systemLoading.setData({ modalLoading: true })

      const { status, data } = payload.id
        ? await costCentersTariffsApiV3(http).update(payload.id, payload)
        : await costCentersTariffsApiV3(http).create(payload)

      const conditionalRequest = [200, 201].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest, data }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'CostCenterModal/_components/TabCustomTariffs/handleSubmit'
      })

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id),
        type: 'error'
      })

      return { status: false, data: null }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleDelete = async (customTariffId: number) => {
    const { messages } = languageModal.table.modalDelete

    try {
      systemLoading.setData({ modalLoading: true })

      const { status } = await costCentersTariffsApiV3(http).delete(
        customTariffId
      )

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(messages, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'CostCenterModal/_components/TabCustomTariffs/handleDelete'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })
      return { status: false }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  return {
    fetchData,
    handleSubmit,
    handleDelete
  }
}

export const useLanguageTabCustomTariffsModal = () => {
  const { lang } = useSystemLanguageStore().state

  const { validationFields, btn, tab } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields
  const { form, table } = tab.costCenterTabCustomTariffs

  return {
    form: {
      input: {
        vigencyStart: form.input.vigencyStart,
        vigencyEnd: form.input.vigencyEnd,
        value: form.input.value
      },
      messages: form.messages,
      requiredField,
      btn: { cancel, save, add, clean }
    },
    table: {
      columns: table.columns,
      modalDelete: {
        ...table.modalDelete,
        btn: { cancel, confirm }
      }
    }
  }
}

/** UTILS */
export const formDataOutput = (
  data: Omit<ITabCustomTariffsValues, 'value'> & {
    value: string
    costCenterId: number
  }
): ISubmitPayload => {
  return {
    id: data.id,
    vigencyStart: data.vigencyStart,
    vigencyEnd: data.vigencyEnd,
    value: +data.value,
    costCenterId: data.costCenterId
  }
}
