import { ICostCenterData } from '../Modal.types'

export interface ITabCustomAdditionaisProps {
  costCenter: Partial<ICostCenterData>
}

/** STATE */
export interface IStateTabCustomAdditionaisData {
  table: {
    items: ITabCusomAdditionaisModal[]
    total: number
    lastPage: number
  }
}
export interface IStateTabCustomAdditionais
  extends IStateTabCustomAdditionaisData {
  set: (p: IStateTabCustomAdditionaisData) => void
  reset: () => void
}

/** FORM */
export interface ISubmitPayload {
  id: number | null
  period: string
  value: number
  costCenterId: number
}
export type ITabCustomAdditionaisValues = {
  id: number | null
  period: string
  value: string
}

/** TABLE */
export type ITabCusomAdditionaisModal = {
  id: number
  period: string
  value: number
  costCenterId: number
}
