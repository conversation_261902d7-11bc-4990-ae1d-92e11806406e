import { zodResolver } from '@hookform/resolvers/zod'
import dayjs from 'dayjs'
import { useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import { tabCustomTariffsCookie } from '@/@core/infra/memory/cookie'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Dialog } from '@/@core/presentation/shared/ui/dialog'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { defineValuesFormFields } from '@/@core/utils/formFields'

import { useNumberFormatter } from '@/@core/framework/hooks/useNumberFormatter/hook'
import { ICostCenterData } from '../Modal.types'
import {
  formDataOutput,
  useLanguageTabCustomTariffsModal,
  useMethodsTabCustomTariffsModal,
  useStateTabCustomTariffsModal
} from './TabCustomTariffs.hooks'
import {
  ITabCustomTariffsModal,
  ITabCustomTariffsValues
} from './TabCustomTariffs.types'

export type { ITabCustomTariffsValues }

const formValuesTabCustomTariffsInitial: ITabCustomTariffsValues = {
  id: null,
  vigencyStart: '',
  vigencyEnd: '',
  value: ''
}

export const TabCustomTariffs = (props: {
  costCenter: Partial<ICostCenterData>
}) => {
  const isMounted = useRef(false)

  const systemLoading = useSystemLoadingStore()
  const systemStore = useSystemStore()

  const stateTabModal = useStateTabCustomTariffsModal()
  const methodsTabModal = useMethodsTabCustomTariffsModal()
  const languageTabModal = useLanguageTabCustomTariffsModal()
  const { currencyFormat } = useNumberFormatter()

  const searchFields = tabCustomTariffsCookie.get()

  const formFields = useTabCustomTariffsFormFields()

  const handler = async () => {
    if (systemStore.state.mountComponent?.['costCenter-tabCustomTariffs'])
      return

    await methodsTabModal.fetchData()

    systemStore.setMountComponent('costCenter-tabCustomTariffs')
  }

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true
      handler()
      return
    }
    return () => {
      isMounted.current = false
    }
  }, [])

  const onSubmit = async () => {
    const costCenterId = Number(props.costCenter.id)

    const { status } = await methodsTabModal.handleSubmit(
      formDataOutput({ ...formFields.values, costCenterId })
    )
    if (!status) return

    await methodsTabModal.fetchData()

    formFields.reset()
  }

  return (
    <>
      <div className="form-container my-4">
        <form
          className="grid lg:grid-cols-3 gap-2"
          id="form-tabCustomTariffs"
          onSubmit={formFields.handleSubmit(onSubmit)}
        >
          <Input.Root>
            <Input.Label>
              {languageTabModal.form.input.vigencyStart}
            </Input.Label>

            <Input.ContentDate
              value={
                formFields.values.vigencyStart
                  ? dayjs(formFields.values.vigencyStart).toDate()
                  : undefined
              }
              onChange={(value) =>
                formFields.setValue(
                  'vigencyStart',
                  dayjs(value).format('YYYY-MM-DD')
                )
              }
              slotEnd={
                <Icon
                  icon="calendar"
                  className="icon-menu-primary"
                  height="24"
                  width="24"
                  viewBox="0 0 20 20"
                />
              }
              helperText={formFields.errors.vigencyStart?.message}
              disabled={systemLoading.state.loading}
            >
              {formFields.values.vigencyStart
                ? dayjs(formFields.values.vigencyStart).format('DD/MM/YYYY')
                : ''}
            </Input.ContentDate>
          </Input.Root>

          <Input.Root>
            <Input.Label>{languageTabModal.form.input.vigencyEnd}</Input.Label>
            <Input.ContentDate
              value={
                formFields.values.vigencyEnd
                  ? dayjs(formFields.values.vigencyEnd).toDate()
                  : undefined
              }
              onChange={(value) =>
                formFields.setValue(
                  'vigencyEnd',
                  dayjs(value).format('YYYY-MM-DD')
                )
              }
              slotEnd={
                <Icon
                  icon="calendar"
                  className="icon-menu-primary"
                  height="24"
                  width="24"
                  viewBox="0 0 20 20"
                />
              }
              helperText={formFields.errors.vigencyEnd?.message}
              disabled={systemLoading.state.loading}
            >
              {formFields.values.vigencyEnd
                ? dayjs(formFields.values.vigencyEnd).format('DD/MM/YYYY')
                : ''}
            </Input.ContentDate>
          </Input.Root>

          <Input.Root>
            <Input.Label htmlFor="value">
              {languageTabModal.form.input.value}
            </Input.Label>
            <Input.Content
              id="value"
              name="value"
              value={formFields.values.value}
              onChange={({ target }) => {
                formFields.setValue('value', target.value)
              }}
              disabled={systemLoading.state.loading}
              helperText={formFields.errors.value?.message}
            />
          </Input.Root>
        </form>

        <div className="footer-form">
          <Button type="button" onClick={() => formFields.reset()}>
            {formFields.values.id
              ? languageTabModal.form.btn.cancel
              : languageTabModal.form.btn.clean}
          </Button>

          <Button type="submit" variant="primary" form="form-tabCustomTariffs">
            {formFields.values.id
              ? languageTabModal.form.btn.save
              : languageTabModal.form.btn.add}
          </Button>
        </div>
      </div>

      <Table.Root classNameWrapper="block">
        <Table.Header>
          <Table.Row>
            <Table.Head>
              {languageTabModal.table.columns.vigencyStart}
            </Table.Head>
            <Table.Head>{languageTabModal.table.columns.vigencyEnd}</Table.Head>
            <Table.Head>{languageTabModal.table.columns.value}</Table.Head>
            <Table.Head>{languageTabModal.table.columns.actions}</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {stateTabModal.table.items.map((customTariff) => (
            <Table.Row key={customTariff.id}>
              <Table.Cell>
                {dayjs(customTariff.vigencyStart).format('MM/DD/YYYY')}
              </Table.Cell>
              <Table.Cell>
                {dayjs(customTariff.vigencyEnd).format('MM/DD/YYYY')}
              </Table.Cell>
              <Table.Cell>
                {customTariff.value
                  ? currencyFormat(customTariff.value, {
                      locale: 'pt-BR',
                      currency: 'BRL'
                    })
                  : ''}
              </Table.Cell>
              <Table.Cell width={80} role="td-actions">
                <div className="flex items-center gap-2">
                  <ButtonDelete
                    customTariffId={customTariff.id}
                    resetForm={formFields.reset}
                  />
                  <ButtonEdit
                    data={customTariff}
                    onClick={formFields.setValues}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
          <Table.RowLoading status={systemLoading.state.loading} colSpan={4} />
        </Table.Body>

        <Table.Paginate
          status={systemLoading.state.loading}
          currentPage={searchFields.page}
          lastPage={stateTabModal.table.lastPage}
          handleChangePage={(page) => {
            tabCustomTariffsCookie.set({
              ...tabCustomTariffsCookie.get(),
              page
            })
            methodsTabModal.fetchData()
          }}
        />
      </Table.Root>
    </>
  )
}
const ButtonDelete = ({
  customTariffId,
  resetForm
}: {
  customTariffId: number
  resetForm: Function
}) => {
  const [open, setOpen] = useState<boolean>(false)

  const languageModal = useLanguageTabCustomTariffsModal()
  const methodsTabModal = useMethodsTabCustomTariffsModal()

  const handleClickConfirm = async () => {
    const { status } = await methodsTabModal.handleDelete(customTariffId)

    if (!status) return

    await methodsTabModal.fetchData()

    setOpen(false)

    resetForm()
  }

  return (
    <Dialog.Root open={open} onOpenChange={setOpen}>
      <Dialog.Trigger className="table-td-action hover:cursor-pointer">
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </Dialog.Trigger>
      <Dialog.Content size={'lg2'}>
        <Dialog.Header>
          <Dialog.Title>{languageModal.table.modalDelete.title}</Dialog.Title>
        </Dialog.Header>

        {languageModal.table.modalDelete.textInfo}

        <Dialog.Footer>
          <Button type="button" onClick={() => setOpen(false)}>
            {languageModal.table.modalDelete.btn.cancel}
          </Button>

          <Button
            type="button"
            variant="error-primary"
            onClick={handleClickConfirm}
          >
            {languageModal.table.modalDelete.btn.confirm}
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}
const ButtonEdit = ({
  data,
  onClick
}: {
  data: ITabCustomTariffsModal
  onClick: (payload: Partial<ITabCustomTariffsValues>) => void
}) => {
  return (
    <button
      className="table-td-action hover:cursor-pointer"
      onClick={() => {
        onClick({ ...data, value: data.value.toString() })
      }}
    >
      <Icon
        icon="edit"
        className="icon-menu-primary"
        height="20"
        width="20"
        viewBox="0 0 20 20"
      />
    </button>
  )
}
const useTabCustomTariffsFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabCustomTariffsModal()

  const {
    formState: { errors, isSubmitting },
    ...form
  } = useForm({
    resolver: zodResolver(
      z.object({
        id: z.number().nullable(),
        vigencyStart: z.string().min(1, { message }),
        vigencyEnd: z.string().min(1, { message }),
        value: z.string().min(1, { message })
      })
    ),
    defaultValues: { ...formValuesTabCustomTariffsInitial }
  })

  const values = form.watch()

  const setValues = (payload: Partial<ITabCustomTariffsValues>) => {
    defineValuesFormFields(
      form.setValue,
      payload,
      formValuesTabCustomTariffsInitial
    )
  }

  return { ...form, values, errors, isSubmitting, setValues }
}
