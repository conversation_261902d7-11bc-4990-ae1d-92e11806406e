/** STATE */
export interface IStateTabCustomTariffsData {
  table: {
    items: ITabCustomTariffsModal[]
    total: number
    lastPage: number
  }
}
export interface IStateTabCustomTariffs extends IStateTabCustomTariffsData {
  set: (p: IStateTabCustomTariffsData) => void
  reset: () => void
}

/** FORM */
export type ISubmitPayload = {
  id: number | null
  costCenterId: number
  vigencyStart: string
  vigencyEnd: string
  value: number
}

export type ITabCustomTariffsValues = {
  id: number | null
  vigencyStart: string
  vigencyEnd: string
  value: string
}

/** TABLE */
export type ITabCustomTariffsModal = {
  id: number
  vigencyStart: string
  vigencyEnd: string
  value: number
}
