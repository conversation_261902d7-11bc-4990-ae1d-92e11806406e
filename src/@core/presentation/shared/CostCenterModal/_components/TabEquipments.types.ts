/** STATE */
export interface IStateTabEquipmentsData {
  table: {
    items: ITabEquipmentsModal[]
    total: number
    lastPage: number
  }
}
export interface IStateTabEquipments extends IStateTabEquipmentsData {
  set: (p: IStateTabEquipmentsData) => void
  reset: () => void
}

/** FORM */
export type ITabEquipmentsValues = {
  id: number | null
  equipments: { id: number; name: string }[]
  costCompositionTypeId: string
  usageLimit: string
  groups: { id: number; name: string }[]
}
/** TABLE */
export type ITabEquipmentsModal = {
  id: number
  equipment: {
    id: number
    name: string
  }
  usageLimit: number

  costCenterId: number
  group: {
    id: number
    name: string
  } | null
  costCompositionType: {
    id: number
    name: string
  }
}
export type ISubmitPayload = {
  id: number | null
  costCenterId: number
  equipmentId: number
  usageLimit: number
  apportionmentGroupId: number | null
  costCompositionTypeId: number
}
