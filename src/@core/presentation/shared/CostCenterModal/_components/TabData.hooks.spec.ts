import '__mock__/logging/logger'

import { renderHook, waitFor } from '@testing-library/react'

import useListApportionmentTariffTypes from '@/@core/framework/store/hook/useListApportionmentTariffTypes'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { appCookie } from '@/@core/infra/memory/cookie'
import { apportionmentTariffTypesResponseMock1 } from '@/__mock__/content/api-apportionment-tariff-types'
import { costCenterResponseMock1 } from '@/__mock__/content/api-cost-centers.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import {
  useLanguageTabDataModal,
  useMethodsTabDataModal
} from './TabData.hooks'

jest.mock('@/@core/infra/api/CostCentersApiV3')
const spyCostCentersApiV3 = jest.spyOn(
  require('@/@core/infra/api/CostCentersApiV3'),
  'costCentersApiV3'
)

jest.mock('@/@core/infra/api/ApportionmentTariffTypesV3')
const spyApportionmentTariffTypesV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentTariffTypesV3'),
  'apportionmentTariffTypesV3'
)

describe('@core/presentation/shared/CostCenterModal/_components/TabData.hooks | useMethodsTabDataModal', () => {
  beforeEach(() => {
    appCookie.init()
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodsTabDataModal(),
        toast: useSystemToastStore()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    const payload = {
      id: null,
      apportionmentId: 123,
      apportionmentTariffTypeId: 1,
      name: 'name 1',
      email: '<EMAIL>'
    }

    /* request CREATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyCostCentersApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao criar centro de custo'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request CREATE success **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyCostCentersApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: costCenterResponseMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Centro de custo criado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')

    /* request UPDATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyCostCentersApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar centro de custo'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE success **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyCostCentersApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: costCenterResponseMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Centro de custo atualizado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })

  it('should check return the function getDependencies', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodsTabDataModal(),
        listApportionmentTariffTypes: useListApportionmentTariffTypes()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => {
      result.current.listApportionmentTariffTypes.set({ list: [] })
    })

    spyApportionmentTariffTypesV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({ status: 500, data: null })
    }))
    await waitFor(async () => {
      await result.current.method.getDependencies()
    })
    expect(result.current.listApportionmentTariffTypes.state.list).toHaveLength(
      0
    )

    spyApportionmentTariffTypesV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: [apportionmentTariffTypesResponseMock1]
      })
    }))
    await waitFor(async () => {
      await result.current.method.getDependencies()
    })
    expect(result.current.listApportionmentTariffTypes.state.list).toHaveLength(
      1
    )
  })
})

describe('@core/presentation/shared/CostCenterModal/_components/TabData.hooks | useLanguageTabDataModal', () => {
  it('check the form texts', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabDataModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    expect(result.current.language.form.input).toEqual({
      inputEmail: 'E-mail',
      inputName: 'Nome',
      inputTariffType: 'Tarifa Padrão'
    })
  })
})
