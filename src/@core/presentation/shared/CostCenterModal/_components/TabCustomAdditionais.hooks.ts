import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { http } from '@/@core/infra/http'
import { tabCostCenterCustomAdditionaisCookie } from '@/@core/infra/memory/cookie'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import {
  toastMessageSwitch,
  toastRequestMessageSwitch,
  toastTypeSwitch
} from '@/@core/utils/toast'

import { ICostCentersAdditionalConsumptions } from '@/@core/domain/CostCentersAdditionalConsumptions'
import { costCentersAdditionalConsumptionsApiV3 } from '@/@core/infra/api/CostCentersAdditionalConsumptionsApiV3'
import { create } from 'zustand'
import {
  IStateTabCustomAdditionais,
  IStateTabCustomAdditionaisData,
  ISubmitPayload,
  ITabCustomAdditionaisValues
} from './TabCustomAdditionais.types'

const stateData: IStateTabCustomAdditionaisData = {
  table: {
    items: [],
    lastPage: 0,
    total: 0
  }
}

export const useStateTabCustomAdditionalModal =
  create<IStateTabCustomAdditionais>((set) => ({
    ...stateData,
    set: (initialData) => set((state) => ({ ...state, ...initialData })),
    reset: () => set((state) => ({ ...state, ...stateData }))
  }))

export const useMethodsTabCustomAdditionalModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const stateModal = useStateTabCustomAdditionalModal()
  const languageModal = useLanguageTabCustomAdditionalModal()

  const fetchData = async () => {
    try {
      systemLoading.setData({ modalLoading: true })

      const search = tabCostCenterCustomAdditionaisCookie.get()

      const { data } = await costCentersAdditionalConsumptionsApiV3(http).get({
        ...search,
        costCenterId: search.costCenterId
      })

      stateModal.set({
        table: {
          items: data.items,
          total: data.total,
          lastPage: data.lastPage
        }
      })
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'CostCenterModal/_components/TabCustomAdditionais//handleSubmit'
      })
      systemToast.addToast({
        message: languageModal.errors.request,
        type: 'error'
      })
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }
  const handleSubmit = async (payload: ISubmitPayload) => {
    const { messages } = languageModal.form

    try {
      const { status } = payload.id
        ? await costCentersAdditionalConsumptionsApiV3(http).update(
            payload.id,
            payload
          )
        : await costCentersAdditionalConsumptionsApiV3(http).create(payload)

      const conditionalRequest = [200, 201].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'CostCenterModal/_components/TabCustomAdditionais/handleSubmit'
      })

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id),
        type: 'error'
      })
      return { status: false }
    }
  }

  const handleDelete = async (customAdditionalId: number) => {
    const { messages } = languageModal.table.modalDelete

    try {
      systemLoading.setData({ modalLoading: true })

      const { status } = await costCentersAdditionalConsumptionsApiV3(
        http
      ).delete(customAdditionalId)

      const conditionalRequest = status === 204

      systemToast.addToast({
        message: toastRequestMessageSwitch(messages, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'CostCenterModal/_components/TabCustomAdditionais/handleDelete'
      })

      systemToast.addToast({
        message: messages.errorMessage,
        type: 'error'
      })
      return { status: false }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  return { fetchData, handleSubmit, handleDelete }
}

export const useLanguageTabCustomAdditionalModal = () => {
  const { lang } = useSystemLanguageStore().state

  const { validationFields, btn, tab, errors } = languageByMode(lang)
  const { cancel, save, confirm, add, clean } = btn
  const { requiredField } = validationFields
  const { form, table } = tab.costCenterTabCustomAdditionais

  return {
    form: {
      input: {
        period: form.input.period,
        value: form.input.value
      },
      messages: form.messages,
      requiredField,
      btn: { cancel, save, add, clean }
    },
    table: {
      columns: table.columns,
      modalDelete: {
        ...table.modalDelete,
        btn: { cancel, confirm }
      }
    },
    errors
  }
}

export const formDataInput = (
  data: Omit<ICostCentersAdditionalConsumptions, 'costCenterId'>
): ITabCustomAdditionaisValues => {
  return {
    id: data.id,
    period: data.period,
    value: data.value.toString()
  }
}

export const formDataOutput = (
  data: ITabCustomAdditionaisValues & {
    costCenterId: number
  }
): ISubmitPayload => {
  return {
    id: data.id,
    period: data.period,
    value: Number(data.value),
    costCenterId: data.costCenterId
  }
}
