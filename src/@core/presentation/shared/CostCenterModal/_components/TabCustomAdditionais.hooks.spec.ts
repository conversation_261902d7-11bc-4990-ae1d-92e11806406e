import { renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { appCookie } from '@/@core/infra/memory/cookie'
import {
  costCentersAdditionalConsumptionsMock1,
  costCentersAdditionalConsumptionsMock2
} from '@/__mock__/content/api-cost-centers-additional-consumptions'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import {
  formDataInput,
  formDataOutput,
  useLanguageTabCustomAdditionalModal,
  useMethodsTabCustomAdditionalModal,
  useStateTabCustomAdditionalModal
} from './TabCustomAdditionais.hooks'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/CostCentersAdditionalConsumptionsApiV3')

const spyCostCentersAdditionalConsumptionsApiV3 = jest.spyOn(
  require('@/@core/infra/api/CostCentersAdditionalConsumptionsApiV3'),
  'costCentersAdditionalConsumptionsApiV3'
)

describe('@core/presentation/shared/CostCenterModal/_components/TabCustomAdditionais.hooks | useStateTabCustomAdditionalModal', () => {
  it('should exec method set and reset', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabCustomAdditionalModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    await waitFor(() => {
      result.current.state.set({
        table: {
          items: [
            costCentersAdditionalConsumptionsMock1,
            costCentersAdditionalConsumptionsMock2
          ],
          lastPage: 1,
          total: 1
        }
      })
    })

    expect(result.current.state.table.items).toHaveLength(2)
    expect(result.current.state.table.lastPage).toBe(1)
    expect(result.current.state.table.total).toBe(1)

    await waitFor(() => result.current.state.reset())

    expect(result.current.state.table.items).toHaveLength(0)
    expect(result.current.state.table.lastPage).toBe(0)
    expect(result.current.state.table.total).toBe(0)
  })
})

describe('@core/presentation/shared/CostCenterModal/_components/TabCustomAdditionais.hooks | useMethodsTabCustomAdditionalModal', () => {
  beforeEach(() => {
    appCookie.init()

    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      query: { id: 120 }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabCustomAdditionalModal(),
        method: useMethodsTabCustomAdditionalModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    /* fetchData with error **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersAdditionalConsumptionsApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({ status: 500, data: null })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.table.items).toHaveLength(0)

    /* request successful without data **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersAdditionalConsumptionsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({ status: 200, data: { items: [] } })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.table.items).toHaveLength(0)

    /* fetchData successful **/
    await waitFor(() => result.current.state.reset())

    spyCostCentersAdditionalConsumptionsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [
            costCentersAdditionalConsumptionsMock1,
            costCentersAdditionalConsumptionsMock2
          ],
          total: 2,
          limit: 15,
          page: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    expect(result.current.state.table.items).toHaveLength(2)
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsTabCustomAdditionalModal()
      }),
      { wrapper: AppStoreProvider }
    )

    const payload = {
      id: null,
      period: '2025-01-01',
      value: 2,
      costCenterId: 1
    }

    /* request CREATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyCostCentersAdditionalConsumptionsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })

    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao cadastrar periodo'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyCostCentersAdditionalConsumptionsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar periodo'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE success **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyCostCentersAdditionalConsumptionsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: costCentersAdditionalConsumptionsMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Periodo atualizado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })

  it('should check return the function handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodsTabCustomAdditionalModal(),
        toast: useSystemToastStore()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request error **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyCostCentersAdditionalConsumptionsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({ status: 500 })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover periodo'
    )

    /* request success **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyCostCentersAdditionalConsumptionsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({ status: 204 })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Periodo removido com sucesso'
    )
  })
})

describe('@core/presentation/shared/CostCenterModal/_components/TabCustomAdditionais.hooks | useLanguageTabCustomAdditionalModal', () => {
  it('check the form texts', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabCustomAdditionalModal()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.form.input).toEqual({
      period: 'Periodo',
      value: 'Valor'
    })
  })
})

describe('@core/presentation/shared/CostCenterModal/_components/TabCustomAdditionais.hooks | utils', () => {
  it('should check return the function formDataOutput', () => {
    const dataInput = {
      id: 1,
      period: '2025-01-01',
      value: '2',
      costCenterId: 1
    }
    const dataOutput = {
      id: 1,
      period: '2025-01-01',
      value: 2,
      costCenterId: 1
    }
    const result = formDataOutput(dataInput)

    expect(result).toEqual(dataOutput)
  })

  it('should check return the function formDataInput payload', () => {
    const dataInput = {
      id: 1,
      period: '2025-01-01',
      value: 2
    }
    const dataOutput = {
      id: 1,
      period: '2025-01-01',
      value: '2'
    }
    const result = formDataInput(dataInput)

    expect(result).toEqual(dataOutput)
  })
})
