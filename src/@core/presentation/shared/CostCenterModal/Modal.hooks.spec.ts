import { renderHookWithRedux } from '@/utils/setupTest'
import { useLanguageModal } from './Modal.hooks'

describe('@core/presentation/shared/CostCenterModal/Modal.hooks | useLanguageModal', () => {
  it('check de modal title', () => {
    const { result } = renderHookWithRedux(() => ({
      language: useLanguageModal()
    }))

    expect(result.current.language.titleNew).toBe('New cost center')
  })
})
