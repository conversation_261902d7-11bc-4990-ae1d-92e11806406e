import { FC, forwardRef, useImperativeHandle, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { cn } from '@/@core/framework/plugins/shadcn/utils'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { Badge } from '@/@core/presentation/shared/ui/badge'
import { Button } from '@/@core/presentation/shared/ui/button'
import { CalendarMonth } from '@/@core/presentation/shared/ui/calendar'
import { zodResolver } from '@hookform/resolvers/zod'

import {
  useLanguageModal,
  useMethodsModal
} from './ApportionmentCalculate.hook'

interface IApportionmentCalculateData {
  date: string
}

export interface IApportionmentCalculateModalRef {
  open: () => void
}

interface IApportionmentCalculateModalModalProps {
  handleSubmitSuccess?: () => Promise<void>
  apportionmentId: number
  hasPeriods: boolean
  periods: string[]
}

export const ApportionmentCalculateModal = forwardRef<
  IApportionmentCalculateModalRef,
  IApportionmentCalculateModalModalProps
>((props, ref) => {
  const modalRef = useRef<IModalRootRef>(null)

  const systemLoading = useSystemLoadingStore()
  const languageModal = useLanguageModal()
  const methodsModal = useMethodsModal()

  const { handleSubmit, setValue, values, reset } = useFormFields()

  const openModal = () => {
    modalRef.current?.open()
  }
  const handleClose = () => {
    reset()
    modalRef.current?.close()
  }

  const onSubmit = async () => {
    const status = await methodsModal.onSubmit({
      apportionmentId: props.apportionmentId,
      date: values.date
    })

    if (!status) return

    handleClose()
  }

  useImperativeHandle(ref, () => ({
    open: openModal,
    close: handleClose
  }))

  return (
    <Modal.Root ref={modalRef} handleClose={handleClose}>
      <Modal.Title>{languageModal.title}</Modal.Title>
      <Modal.Content>
        <LoadingOverlay loading={systemLoading.state.modalLoading} />

        <form
          onSubmit={handleSubmit(onSubmit)}
          id="formApportionmentCalculateModal"
          className="flex flex-col gap-4"
        >
          {props.hasPeriods && (
            <CalendarMonth
              defaultEnabled={false}
              className="mx-auto min-h-[300px]"
              allowedDates={props.periods}
              onChange={(dateString) => setValue('date', dateString)}
            />
          )}
          {!props.hasPeriods && (
            <div
              className={cn(
                'flex items-center gap-2 border rounded-xl',
                'p-3 mt-3'
              )}
            >
              <Badge.Root variant="warning-outline">
                <Badge.Content>
                  {languageModal.form.warningWithoutPeriod}
                </Badge.Content>
              </Badge.Root>
              {languageModal.form.withoutPeriodText}
            </div>
          )}
        </form>
      </Modal.Content>
      <Modal.Footer>
        <Button
          type="button"
          disabled={systemLoading.state.modalLoading}
          onClick={handleClose}
        >
          {languageModal.form.btn.cancel}
        </Button>
        {props.hasPeriods && (
          <Button
            type="submit"
            variant="primary"
            form="formApportionmentCalculateModal"
            disabled={!values.date || systemLoading.state.modalLoading}
          >
            {languageModal.form.btn.save}
          </Button>
        )}
      </Modal.Footer>
    </Modal.Root>
  )
})

const LoadingOverlay: FC<{ loading: boolean }> = ({ loading }) => {
  return (
    <div
      className={cn(
        'absolute inset-0 flex items-center justify-center bg-white/70 dark:bg-comerc-grayLight-950/70 transition-opacity',
        loading ? 'z-20 opacity-100' : 'z-[-1] opacity-0 pointer-events-none'
      )}
    >
      <i
        className="animate-spin w-16 h-16 rounded-full border-t-2 border-[#1c904e]"
        data-testid="loading"
      />
    </div>
  )
}

const useFormFields = () => {
  const formSchema = z.object({
    date: z.string()
  })

  type FormSchema = z.infer<typeof formSchema>

  const parseInitialData = (
    data: Partial<IApportionmentCalculateData>
  ): FormSchema => ({
    date: data.date ?? ''
  })

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: parseInitialData({})
  })

  const defineValues = (dataParsed: Partial<IApportionmentCalculateData>) => {
    Object.entries(dataParsed).forEach(([key, value]) => {
      setValue(key as keyof IApportionmentCalculateData, value)
    })
  }

  const reset = () => {
    defineValues(parseInitialData({}))
  }
  const setValues = (data: Partial<IApportionmentCalculateData> = {}) => {
    defineValues(data)
  }

  const values = watch()

  return { handleSubmit, setValue, setValues, values, errors, reset }
}
