import { renderHookWithRedux } from '@/utils/setupTest'
import { cleanup, waitFor } from '@testing-library/react'

import {
  useLanguageModal,
  useMethodsModal
} from './ApportionmentCalculate.hook'

cleanup()

jest.mock('@/@core/infra/api/ApportiomentReprocessApiV3')

const spyApportiomentReprocessApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportiomentReprocessApiV3'),
  'apportiomentReprocessApiV3'
)

describe('src/@core/presentation/shared/ApportionmentCalculateModal | useLanguageModal', () => {
  it('check the title', () => {
    const { result } = renderHookWithRedux(() => ({
      languageModal: useLanguageModal()
    }))

    expect(result.current.languageModal.title).toBe('Calculate apportionment')
  })
})

describe('src/@core/presentation/shared/ApportionmentCalculateModal | useMethodsModal', () => {
  it('should exec method getApportionmentsPeriods', async () => {
    const { result } = renderHookWithRedux(() => ({
      method: useMethodsModal()
    }))

    const payload: {
      date: string
    } = {
      date: '2023-02-01'
    }

    let status: boolean | undefined

    /** request error */
    spyApportiomentReprocessApiV3.mockImplementation(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(() => {
      result.current.method
        .onSubmit({ apportionmentId: 1, date: payload.date })
        .catch((response) => (status = response.status))
    })
    expect(status).toBeFalsy()

    /** create request success */
    spyApportiomentReprocessApiV3.mockImplementation(() => ({
      create: jest.fn().mockResolvedValue({
        status: 201,
        data: null
      })
    }))
    await waitFor(() => {
      result.current.method
        .onSubmit({ apportionmentId: 1, date: payload.date })
        .then((response) => (status = response))
    })
    expect(status).toBeTruthy()
  })
})
