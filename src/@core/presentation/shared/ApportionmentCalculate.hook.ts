import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { apportiomentReprocessApiV3 } from '@/@core/infra/api/ApportiomentReprocessApiV3'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'

export const useMethodsModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const languageModal = useLanguageModal()
  const log = useLog()

  const onSubmit = async (payload: {
    apportionmentId: number
    date: string
  }) => {
    try {
      systemLoading.setData({ loading: true })

      await apportiomentReprocessApiV3(http).create(
        { date: payload.date },
        payload.apportionmentId
      )

      systemToast.addToast({
        message: languageModal.form.messages.successMessage,
        type: 'success'
      })

      return true
    } catch (error) {
      log.send(loggerRequest, {
        error,
        title: `Erro no onSubmit rateio - @/@core/presentation/shared/ApportionmentCalculateModal.tsx`
      })

      systemToast.addToast({
        message: languageModal.form.messages.errorMessage,
        type: 'error'
      })

      return false
    } finally {
      systemLoading.setData({ loading: false })
    }
  }

  return { onSubmit }
}

export const useLanguageModal = () => {
  const { lang } = useSystemLanguageStore().state

  const { validationFields, modal, btn, errors } = languageByMode(lang)
  const { cancel, save } = btn
  const { requiredField } = validationFields
  const { apportionmentCalculate } = modal

  return {
    title: apportionmentCalculate.title,
    form: {
      ...apportionmentCalculate.form,
      requiredField,
      errors,
      btn: { cancel, save }
    }
  }
}
