export interface IModalData {
  tab: string
  apportionment: Partial<IApportionmentData>
}
export interface IApportionmentData {
  id: number | null
  name: string
  description: string
  company: { id: number; name: string } | undefined
  apportionmentType: { id: number; name: string }
  apportionmentTariffType: { id: number; name: string }
  /** subir de acordo com o uso, o que sobrar remover */
  // costCenterIds: number[]
  apportionmentMeasureUnit: {
    id: number
    name: string
  }
  mixedConsumption: boolean
}
export interface IApportionmentModalRef {
  handler?: (data: Partial<IApportionmentData>) => void
  open?: () => void
  close?: () => void
}
export interface IApportionmentModalProps {
  tabDataSubmitSuccess?: () => Promise<void>
  handleCreatedTabData?: () => Promise<void>
}
