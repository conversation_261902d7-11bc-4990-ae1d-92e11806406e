import { IApportionment } from '@/@core/domain/Apportionment'
import { IApportionmentData } from '../Modal.types'

export interface ITabDataRef {
  handler: (p: Partial<IApportionmentData>) => void
}
export interface ITabDataProps {
  apportionment: Partial<IApportionmentData>
  handleCreated?: (p: IApportionment) => void
  btnCancel?: () => void
  // apportionmentId: number
}
export interface ITabDataValues {
  id: number | null
  name: string
  description: string
  companies: { id: number; name: string }[]
  apportionmentType: string
  apportionmentTariffType: string
  apportionmentMeasureUnit: string
  mixedConsumption: boolean
}

export type ISubmitPayload = {
  id: number | null
  name: string
  description: string
  companyId: number
  apportionmentTypeId: number
  apportionmentTariffTypeId: number
  apportionmentMeasureUnitId: number
  mixedConsumption: boolean
}
