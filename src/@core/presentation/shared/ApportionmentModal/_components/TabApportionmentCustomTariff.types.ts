/** STATE */
export interface IStateTabApportionmentCustomTariffsData {
  table: {
    items: ITabApportionmentCustomTariffsModal[]
    total: number
    lastPage: number
  }
}
export interface IStateTabApportionmentCustomTariffs
  extends IStateTabApportionmentCustomTariffsData {
  set: (p: IStateTabApportionmentCustomTariffsData) => void
  reset: () => void
}

/** FORM */
export type ISubmitPayload = {
  id: number | null
  vigencyStart: string
  vigencyEnd: string
  value: number
  apportionmentId: number
}

export type ITabApportionmentCustomTariffsValues = {
  id: number | null
  vigencyStart: string
  vigencyEnd: string
  value: string
}

/** TABLE */
export type ITabApportionmentCustomTariffsModal = {
  id: number
  vigencyStart: string
  vigencyEnd: string
  value: number
}
