/** STATE */
export interface IStateTabApportionmentPeriodData {
  table: {
    items: ITabApportionmentPeriodModal[]
    total: number
    lastPage: number
  }
}
export interface IStateTabApportionmentPeriod
  extends IStateTabApportionmentPeriodData {
  set: (p: IStateTabApportionmentPeriodData) => void
  reset: () => void
}

/** FORM */
export type ISubmitPayload = {
  id: number | null
  period: string
  periodStart: string
  periodEnd: string
  apportionmentId: number
}
export type ITabApportionmentPeriodValues = {
  id: number | null
  period: string
  periodStart: string
  periodEnd: string
}

/** TABLE */
export type ITabApportionmentPeriodModal = {
  id: number
  period: string
  periodStart: string
  periodEnd: string
}
