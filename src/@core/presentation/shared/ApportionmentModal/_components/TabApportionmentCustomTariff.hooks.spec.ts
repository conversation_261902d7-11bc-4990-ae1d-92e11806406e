import '@/__mock__/logging/logger'

import { renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { appCookie } from '@/@core/infra/memory/cookie'
import { apportionmentPeriodMock1 } from '@/__mock__/content/api-apportionments-periods.content'
import { apportionmentsTariffsResponseMock1 } from '@/__mock__/content/api-apportionments-tariffs.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import {
  formDataInput,
  formDataOutput,
  useLanguageTabApportionmnetCustomTariffsModal,
  useMethodsTabApportionmentCustomTariffsModal,
  useStateTabApportionmentCustomTariffsModal
} from './TabApportionmentCustomTariff.hooks'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/ApportionmentsTariffsApiV3')
const spyApportionmentsTariffsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsTariffsApiV3'),
  'apportionmentsTariffsApiV3'
)

describe('src/@core/presentation/shared/ApportionmentModal/_components/TabApportionmentCustomTariff | useLanguageTabApportionmnetCustomTariffsModal', () => {
  it('check de page title', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabApportionmnetCustomTariffsModal()
      }),
      { wrapper: AppStoreProvider }
    )
    expect(result.current.language.form.input).toEqual({
      value: 'Valor',
      vigencyEnd: 'Fim da vigência',
      vigencyStart: 'Início da vigência'
    })
  })
})

describe('src/@core/presentation/shared/ApportionmentModal/_components/TabApportionmentCustomTariff | useMethodsTabApportionmentCustomTariffsModal', () => {
  beforeEach(() => {
    appCookie.init()

    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      query: { id: 120 }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabApportionmentCustomTariffsModal(),
        method: useMethodsTabApportionmentCustomTariffsModal()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyApportionmentsTariffsApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    const resultError = result.current.state.table

    expect(resultError.items).toHaveLength(0)
    expect(resultError.lastPage).toBe(0)
    expect(resultError.total).toBe(0)

    /* request success with data **/
    spyApportionmentsTariffsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [apportionmentsTariffsResponseMock1],
          total: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(() => {
      result.current.method.fetchData()
    })

    const resultData = result.current.state.table

    expect(resultData.items).toHaveLength(1)
    expect(resultData.lastPage).toBe(1)
    expect(resultData.total).toBe(1)
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsTabApportionmentCustomTariffsModal()
      }),
      { wrapper: AppStoreProvider }
    )

    const payload = {
      id: null,
      value: 2,
      vigencyStart: '2025-01-01',
      vigencyEnd: '2025-01-01',
      apportionmentId: 2
    }

    /* request CREATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsTariffsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })

    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao cadastrar tarifa customizada'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsTariffsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar tarifa customizada'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE success **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsTariffsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: apportionmentPeriodMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Tarifa customizada atualizado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })

  it('should check return the function handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsTabApportionmentCustomTariffsModal()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request error **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyApportionmentsTariffsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover tarifa customizada'
    )

    /* request success **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyApportionmentsTariffsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Tarifa customizada removido com sucesso'
    )
  })
})

describe('src/@core/presentation/shared/ApportionmentModal/_components/TabApportionmentCustomTariff | utils', () => {
  it('should check return the function formDataOutput', () => {
    const dataInput = {
      id: 1,
      value: '2',
      vigencyStart: '2025-01-01',
      vigencyEnd: '2025-01-20',
      apportionmentId: 1
    }
    const dataOutput = {
      id: 1,
      value: 2,
      vigencyStart: '2025-01-01',
      vigencyEnd: '2025-01-20',
      apportionmentId: 1
    }
    const result = formDataOutput(dataInput)

    expect(result).toEqual(dataOutput)
  })

  it('should check return the function formDataInput payload', () => {
    const dataInput = {
      id: 1,
      value: '2',
      vigencyStart: '2025-01-01',
      vigencyEnd: '2025-01-20'
    }
    const dataOutput = {
      id: 1,
      value: '2',
      vigencyStart: '2025-01-01',
      vigencyEnd: '2025-01-20'
    }
    const result = formDataInput(dataInput)

    expect(result).toEqual(dataOutput)
  })
})
