import '@/__mock__/logging/logger'

import { renderHook, waitFor } from '@testing-library/react'

import useListApportionmentsFeeDivisionTypes from '@/@core/framework/store/hook/useListApportionmentsFeeDivisionTypes'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { appCookie } from '@/@core/infra/memory/cookie'
import { apportionmentAdditionalFeesMock1 } from '@/__mock__/content/api-apportionments-additional-fees.content'
import {
  apportionmentFeeDivisionTypeMock1,
  apportionmentFeeDivisionTypeMock2
} from '@/__mock__/content/api-apportionments-fee-division-types.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import {
  formDataInput,
  formDataOutput,
  useLanguageTabApportionmentAdditionalFeesModal,
  useMethodsTabApportionmentAdditionalFeesModal,
  useStateTabApportionmentAdditionalFeesModal
} from './TabApportionmentAdditionalFees.hooks'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/ApportionmentsAdditionalFeesApiV3')
const spyApportionmentsAdditionalFeesApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsAdditionalFeesApiV3'),
  'apportionmentsAdditionalFeesApiV3'
)

jest.mock('@/@core/infra/api/ApportionmentsFeeDivisionTypesApiV3')
const spyApportionmentsFeeDivisionTypesApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsFeeDivisionTypesApiV3'),
  'apportionmentsFeeDivisionTypesApiV3'
)

describe('src/@core/presentation/shared/ApportionmentModal/_components/TabApportionmentAdditionalFees | useLanguageTabApportionmentAdditionalFeesModal', () => {
  it('check de page title', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabApportionmentAdditionalFeesModal()
      }),
      { wrapper: AppStoreProvider }
    )
    expect(result.current.language.form.input).toEqual({
      name: 'Nome',
      period: 'Período',
      type: 'Tipo',
      value: 'Valor'
    })
  })
})

describe('src/@core/presentation/shared/ApportionmentModal/_components/TabApportionmentAdditionalFees | useMethodsTabApportionmentAdditionalFeesModal', () => {
  beforeEach(() => {
    appCookie.init()

    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      query: { id: 120 }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabApportionmentAdditionalFeesModal(),
        method: useMethodsTabApportionmentAdditionalFeesModal()
      }),
      { wrapper: AppStoreProvider }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyApportionmentsAdditionalFeesApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    const resultError = result.current.state.table

    expect(resultError.items).toHaveLength(0)
    expect(resultError.lastPage).toBe(0)
    expect(resultError.total).toBe(0)

    /* request success with data **/
    spyApportionmentsAdditionalFeesApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [apportionmentAdditionalFeesMock1],
          total: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(() => {
      result.current.method.fetchData()
    })

    const resultData = result.current.state.table

    expect(resultData.items).toHaveLength(1)
    expect(resultData.lastPage).toBe(1)
    expect(resultData.total).toBe(1)
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsTabApportionmentAdditionalFeesModal()
      }),
      { wrapper: AppStoreProvider }
    )

    const payload = {
      id: null,
      name: 'NAME TEST',
      period: '2025-01-01',
      value: 3,
      divisionTypeId: 4,
      apportionmentId: 5
    }

    /* request CREATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsAdditionalFeesApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })

    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao cadastrar taxa adicional'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsAdditionalFeesApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar taxa adicional'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE success **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsAdditionalFeesApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: apportionmentAdditionalFeesMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Taxa adicional atualizada com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })

  it('should check return the function handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsTabApportionmentAdditionalFeesModal()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request error **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyApportionmentsAdditionalFeesApiV3.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({
        status: 500
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover taxa adicional'
    )

    /* request success **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyApportionmentsAdditionalFeesApiV3.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({
        status: 204
      })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Taxa adicional removida com sucesso'
    )
  })

  it('should check return the function getDependencies', async () => {
    const { result } = renderHook(
      () => ({
        listApportionmentsFeeDivisionTypes:
          useListApportionmentsFeeDivisionTypes(),
        method: useMethodsTabApportionmentAdditionalFeesModal()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApportionmentsFeeDivisionTypesApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        data: [
          apportionmentFeeDivisionTypeMock1,
          apportionmentFeeDivisionTypeMock2
        ]
      })
    }))

    expect(
      result.current.listApportionmentsFeeDivisionTypes.state.list
    ).toHaveLength(0)

    await waitFor(async () => {
      await result.current.method.getDependencies()
    })

    expect(
      result.current.listApportionmentsFeeDivisionTypes.state.list
    ).toHaveLength(2)
  })
})

describe('src/@core/presentation/shared/ApportionmentModal/_components/TabApportionmentAdditionalFees | utils', () => {
  it('should check return the function formDataOutput', () => {
    const dataInput = {
      id: 1,
      name: 'NAME TEST',
      period: '2025-01-01',
      value: '2',
      divisionTypeId: '1',
      apportionmentId: 120
    }
    const dataOutput = {
      id: 1,
      name: 'NAME TEST',
      period: '2025-01-01',
      value: 2,
      divisionTypeId: 1,
      apportionmentId: 120
    }
    const result = formDataOutput(dataInput)

    expect(result).toEqual(dataOutput)
  })

  it('should check return the function formDataInput payload', () => {
    const dataInput = {
      id: 1,
      name: 'NAME TEST',
      period: '2025-01-01',
      value: 2,
      divisionType: { id: 1, name: 'DIVISION TEST' },
      apportionmentId: 120
    }
    const dataOutput = {
      id: 1,
      name: 'NAME TEST',
      period: '2025-01-01',
      value: '2',
      divisionTypeId: '1'
    }
    const result = formDataInput(dataInput)

    expect(result).toEqual(dataOutput)
  })
})
