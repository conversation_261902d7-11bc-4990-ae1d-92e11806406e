import { IApportionmentAdditionalFees } from '@/@core/domain/ApportionmentAdditionalFees'

/** STATE */
export interface IStateTabApportionmentAdditionalFeesData {
  table: {
    items: IApportionmentAdditionalFees[]
    total: number
    lastPage: number
  }
}
export interface IStateTabApportionmentAdditionalFees
  extends IStateTabApportionmentAdditionalFeesData {
  set: (p: IStateTabApportionmentAdditionalFeesData) => void
  reset: () => void
}

/** FORM */
export type ISubmitPayload = {
  id: number | null
  name: string
  period: string
  value: number
  divisionTypeId: number
  apportionmentId: number
}
export type ITabApportionmentAdditionalFeesValues = {
  id: number | null
  name: string
  period: string
  value: string
  divisionTypeId: string
}

/** TABLE */
export type ITabApportionmentAdditionalFeesModal = {}
