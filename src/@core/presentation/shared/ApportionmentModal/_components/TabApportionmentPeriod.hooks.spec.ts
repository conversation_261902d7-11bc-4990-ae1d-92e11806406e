import '@/__mock__/logging/logger'

import { renderHook, waitFor } from '@testing-library/react'

import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { appCookie } from '@/@core/infra/memory/cookie'
import { apportionmentPeriodMock1 } from '@/__mock__/content/api-apportionments-periods.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import {
  formDataInput,
  formDataOutput,
  useLanguageTabApportionmentPeriodModal,
  useMethodsTabApportionmentPeriodModal,
  useStateTabApportionmentPeriodModal
} from './TabApportionmentPeriod.hooks'

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/ApportionmentsPeriodsApiV3')
const spyApportionmentsPeriodsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsPeriodsApiV3'),
  'apportionmentsPeriodsApiV3'
)

describe('src/@core/presentation/shared/ApportionmentModal/_components/TabApportionmentPeriod | useLanguageTabApportionmentPeriodModal', () => {
  it('check de page title', () => {
    const { result } = renderHook(
      () => useLanguageTabApportionmentPeriodModal(),
      {
        wrapper: AppStoreProvider
      }
    )
    expect(result.current.form.input).toEqual({
      period: 'Período',
      periodEnd: 'Fim do período',
      periodStart: 'Início do período'
    })
  })
})

describe('src/@core/presentation/shared/ApportionmentModal/_components/TabApportionmentPeriod | useMethodsTabApportionmentPeriodModal', () => {
  beforeEach(() => {
    appCookie.init()

    spyUseRouter.mockReset()

    spyUseRouter.mockImplementation(() => ({
      query: { id: 120 }
    }))
  })

  it('should check return the function fetchData', async () => {
    const { result } = renderHook(
      () => ({
        state: useStateTabApportionmentPeriodModal(),
        method: useMethodsTabApportionmentPeriodModal()
      }),
      {
        wrapper: AppStoreProvider
      }
    )

    await waitFor(() => result.current.state.reset())

    /* request error **/
    spyApportionmentsPeriodsApiV3.mockImplementation(() => ({
      get: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))
    await waitFor(async () => {
      await result.current.method.fetchData()
    })

    const resultError = result.current.state.table

    expect(resultError.items).toHaveLength(0)
    expect(resultError.lastPage).toBe(0)
    expect(resultError.total).toBe(0)

    /* request success with data **/
    spyApportionmentsPeriodsApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [apportionmentPeriodMock1],
          total: 1,
          lastPage: 1
        }
      })
    }))

    await waitFor(() => {
      result.current.method.fetchData()
    })

    const resultData = result.current.state.table

    expect(resultData.items).toHaveLength(1)
    expect(resultData.lastPage).toBe(1)
    expect(resultData.total).toBe(1)
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsTabApportionmentPeriodModal()
      }),
      { wrapper: AppStoreProvider }
    )

    const payload = {
      id: null,
      period: '2025-01-01',
      periodStart: '2025-01-01',
      periodEnd: '2025-01-01',
      apportionmentId: 2
    }

    /* request CREATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsPeriodsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })

    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao cadastrar equipamento do rateio'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsPeriodsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar equipamento do rateio'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE success **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsPeriodsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: apportionmentPeriodMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Equipamento do rateio atualizado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })

  it('should check return the function handleDelete', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodsTabApportionmentPeriodModal(),
        toast: useSystemToastStore()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request error **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyApportionmentsPeriodsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockRejectedValue({ status: 500 })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao remover equipamento do rateio'
    )

    /* request success **/
    await waitFor(() => {
      result.current.toast.reset()
    })
    spyApportionmentsPeriodsApiV3.mockImplementation(() => ({
      delete: jest.fn().mockResolvedValue({ status: 204 })
    }))
    await waitFor(async () => {
      await result.current.method.handleDelete(120)
    })
    expect(result.current.toast.state.toasts).toHaveLength(1)
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Equipamento do rateio removido com sucesso'
    )
  })
})

describe('src/@core/presentation/shared/ApportionmentModal/_components/TabApportionmentPeriod | utils', () => {
  it('should check return the function formDataOutput', () => {
    const dataInput = {
      id: 1,
      period: '2025-01-01',
      periodStart: '2025-01-01',
      periodEnd: '2025-01-20',
      apportionmentId: 1
    }
    const dataOutput = {
      id: 1,
      period: '2025-01-01',
      periodStart: '2025-01-01',
      periodEnd: '2025-01-20',
      apportionmentId: 1
    }
    const result = formDataOutput(dataInput)

    expect(result).toEqual(dataOutput)
  })

  it('should check return the function formDataInput payload', () => {
    const dataInput = {
      id: 1,
      period: '2025-01-01',
      periodStart: '2025-01-01',
      periodEnd: '2025-01-20'
    }
    const dataOutput = {
      id: 1,
      period: '2025-01-01',
      periodStart: '2025-01-01',
      periodEnd: '2025-01-20'
    }
    const result = formDataInput(dataInput)

    expect(result).toEqual(dataOutput)
  })
})
