import '@/__mock__/logging/logger'

import { cleanup, renderHook, waitFor } from '@testing-library/react'

import useListApportionmentMeasuresUnits from '@/@core/framework/store/hook/useListApportionmentMeasuresUnits'
import useListApportionmentTariffTypes from '@/@core/framework/store/hook/useListApportionmentTariffTypes'
import useListApportionmentTypes from '@/@core/framework/store/hook/useListApportionmentTypes'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { appCookie } from '@/@core/infra/memory/cookie'
import {
  apportionmentMeasuresUnitMock1,
  apportionmentMeasuresUnitMock2
} from '@/__mock__/content/api-apportionment-measures-units.content'
import {
  apportionmentTariffTypesMock1,
  apportionmentTariffTypesMock2
} from '@/__mock__/content/api-apportionment-tariff-types'
import {
  apportionmentTypeMock1,
  apportionmentTypeMock2
} from '@/__mock__/content/api-apportionment-types.content'
import { apportionmentMock1 } from '@/__mock__/content/api-apportionment.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import {
  formDataInput,
  formDataOutput,
  useLanguageTabDataModal,
  useMethodsTabDataModal
} from './TabData.hooks'

cleanup()

const spyUseRouter = jest.spyOn(require('next/router'), 'useRouter')

jest.mock('@/@core/infra/api/ApportionmentsApiV3')
const spyApportionmentsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentsApiV3'),
  'apportionmentsApiV3'
)

jest.mock('@/@core/infra/api/ApportionmentTypesApiV3')
const spyApportionmentTypesApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentTypesApiV3'),
  'apportionmentTypesApiV3'
)

jest.mock('@/@core/infra/api/ApportionmentTariffTypesV3')
const spyApportionmentTariffTypesV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentTariffTypesV3'),
  'apportionmentTariffTypesV3'
)

jest.mock('@/@core/infra/api/ApportionmentMeasuresUnitsApiV3')
const spyApportionmentMeasuresUnitsApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentMeasuresUnitsApiV3'),
  'apportionmentMeasuresUnitsApiV3'
)

describe('@core/presentation/shared/ApportionmentModal/_components/TabData | useLanguageTabDataModal', () => {
  it('check de modal title', () => {
    const { result } = renderHook(
      () => ({
        language: useLanguageTabDataModal()
      }),
      { wrapper: AppStoreProvider }
    )

    expect(result.current.language.title).toBe('Informações')
  })
})

describe('@core/presentation/shared/ApportionmentModal/_components/TabData | useMethodsTabDataModal', () => {
  beforeEach(() => {
    appCookie.init()

    spyUseRouter.mockImplementation(() => ({
      query: { id: 120 }
    }))
  })

  it('should check return the function handleSubmit', async () => {
    const { result } = renderHook(
      () => ({
        toast: useSystemToastStore(),
        method: useMethodsTabDataModal()
      }),
      { wrapper: AppStoreProvider }
    )

    const payload = {
      id: null,
      name: 'NAME TESTE',
      description: 'DESCRIPTION TESTE',
      companyId: 1,
      apportionmentTypeId: 2,
      apportionmentTariffTypeId: 3,
      apportionmentMeasureUnitId: 4,
      mixedConsumption: false
    }

    /* request CREATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao criar rateio'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request CREATE success **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsApiV3.mockImplementationOnce(() => ({
      create: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: apportionmentMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit(payload)
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Rateio criado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')

    /* request UPDATE error **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockRejectedValue({
        status: 500,
        data: null
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Erro ao atualizar rateio'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('error')

    /* request UPDATE success **/
    await waitFor(() => {
      result.current.toast.reset()
    })

    spyApportionmentsApiV3.mockImplementationOnce(() => ({
      update: jest.fn().mockResolvedValueOnce({
        status: 201,
        data: apportionmentMock1
      })
    }))

    await waitFor(async () => {
      await result.current.method.handleSubmit({ ...payload, id: 44 })
    })
    expect(result.current.toast.state.toasts[0].message).toBe(
      'Rateio atualizado com sucesso'
    )
    expect(result.current.toast.state.toasts[0].type).toBe('success')
  })

  it('should check return the function getDependencies', async () => {
    const { result } = renderHook(
      () => ({
        method: useMethodsTabDataModal(),
        listApportionmentTypes: useListApportionmentTypes(),
        listApportionmentTariffTypes: useListApportionmentTariffTypes(),
        listApportionmentMeasuresUnits: useListApportionmentMeasuresUnits()
      }),
      { wrapper: AppStoreProvider }
    )

    /* request error **/
    spyApportionmentTypesApiV3.mockImplementationOnce(() => ({
      get: jest.fn().mockRejectedValue({ status: 500, data: null })
    }))
    await waitFor(async () => {
      await result.current.method.getDependencies({ apportionmentTypeId: null })
    })

    /* request apportionmentTypeId nullable **/
    spyApportionmentTypesApiV3.mockImplementationOnce(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: [apportionmentTypeMock1, apportionmentTypeMock2]
      })
    }))
    spyApportionmentTariffTypesV3.mockImplementationOnce(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: [apportionmentTariffTypesMock1, apportionmentTariffTypesMock2]
      })
    }))

    await waitFor(async () => {
      await result.current.method.getDependencies({ apportionmentTypeId: null })
    })

    expect(result.current.listApportionmentTypes.state.list).toHaveLength(2)
    expect(result.current.listApportionmentTariffTypes.state.list).toHaveLength(
      2
    )
    expect(
      result.current.listApportionmentMeasuresUnits.state.list
    ).toHaveLength(0)

    /* request apportionmentTypeId not nullable **/
    spyApportionmentTypesApiV3.mockImplementationOnce(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: [apportionmentTypeMock1, apportionmentTypeMock2]
      })
    }))
    spyApportionmentTariffTypesV3.mockImplementationOnce(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: [apportionmentTariffTypesMock1, apportionmentTariffTypesMock2]
      })
    }))
    spyApportionmentMeasuresUnitsApiV3.mockImplementationOnce(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: [apportionmentMeasuresUnitMock1, apportionmentMeasuresUnitMock2]
      })
    }))

    await waitFor(async () => {
      await result.current.method.getDependencies({ apportionmentTypeId: 3 })
    })

    expect(result.current.listApportionmentTypes.state.list).toHaveLength(2)
    expect(result.current.listApportionmentTariffTypes.state.list).toHaveLength(
      2
    )
    expect(
      result.current.listApportionmentMeasuresUnits.state.list
    ).toHaveLength(2)
  })
})

describe('@core/presentation/shared/ApportionmentModal/_components/TabData | utils', () => {
  it('should check return the function formDataOutput', () => {
    const dataInput = {
      id: 1,
      name: 'NAME TEST',
      description: 'DESC TEST',
      companies: [{ id: 1, name: 'COMPANY TEST' }],
      apportionmentType: '3',
      apportionmentTariffType: '4',
      apportionmentMeasureUnit: '5',
      mixedConsumption: false
    }
    const dataOutput = {
      id: 1,
      name: 'NAME TEST',
      description: 'DESC TEST',
      companyId: 1,
      apportionmentTypeId: 3,
      apportionmentTariffTypeId: 4,
      apportionmentMeasureUnitId: 5,
      mixedConsumption: false
    }
    const result = formDataOutput(dataInput)

    expect(result).toEqual(dataOutput)
  })

  it('should check return the function formDataInput full payload', () => {
    const dataInput = {
      id: 1,
      name: 'NAME TEST',
      description: 'DESC TEST',
      company: { id: 3, name: 'COMPANY TEST' },
      apportionmentType: { id: 3, name: '...' },
      apportionmentTariffType: { id: 4, name: '...' },
      apportionmentMeasureUnit: { id: 5, name: '...' },
      mixedConsumption: false
    }
    const dataOutput = {
      id: 1,
      name: 'NAME TEST',
      description: 'DESC TEST',
      companies: [{ id: 3, name: 'COMPANY TEST' }],
      apportionmentType: '3',
      apportionmentTariffType: '4',
      apportionmentMeasureUnit: '5',
      mixedConsumption: false
    }
    const result = formDataInput(dataInput)

    expect(result).toEqual(dataOutput)
  })

  it('should check return the function formDataInput partial payload', () => {
    const dataInput = {
      /** */
    }
    const dataOutput = {
      id: null,
      name: '',
      description: '',
      companies: [],
      apportionmentType: '',
      apportionmentTariffType: '',
      apportionmentMeasureUnit: '',
      mixedConsumption: false
    }
    const result = formDataInput(dataInput)

    expect(result).toEqual(dataOutput)
  })
})
