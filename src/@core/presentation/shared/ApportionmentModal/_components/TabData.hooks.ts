import useListApportionmentMeasuresUnits from '@/@core/framework/store/hook/useListApportionmentMeasuresUnits'
import useSystemLanguageStore from '@/@core/framework/store/hook/useSystemLanguageStore'
import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemToastStore from '@/@core/framework/store/hook/useSystemToastStore'
import { apportionmentsApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { languageByMode } from '@/@core/language'
import { useLog } from '@/@core/logging/logger'
import loggerRequest from '@/@core/logging/loggerRequest'
import { ListApportionmentMeasuresUnitsService } from '@/@core/services/listApportionmentMeasuresUnitsService'
import { ListApportionmentTariffTypesService } from '@/@core/services/listApportionmentTariffTypesService'
import { ListApportionmentTypesService } from '@/@core/services/listApportionmentTypesService'
import { toastMessageSwitch, toastTypeSwitch } from '@/@core/utils/toast'

import { IApportionmentData } from '../Modal.types'
import { ISubmitPayload, ITabDataValues } from './TabData.types'

export const useMethodsTabDataModal = () => {
  const systemLoading = useSystemLoadingStore()
  const systemToast = useSystemToastStore()
  const log = useLog()

  const languageModal = useLanguageTabDataModal()

  const listApportionmentMeasuresUnits = useListApportionmentMeasuresUnits()

  const listApportionmentTypesService = ListApportionmentTypesService()
  const listApportionmentMeasuresUnitsService =
    ListApportionmentMeasuresUnitsService()
  const listApportionmentTariffTypesService =
    ListApportionmentTariffTypesService()

  const handleSubmit = async (payload: ISubmitPayload) => {
    const {
      form: { messages }
    } = languageModal

    try {
      systemLoading.setData({ modalLoading: true })

      const { status, data } = payload.id
        ? await apportionmentsApiV3(http).update(payload.id, payload)
        : await apportionmentsApiV3(http).create(payload)

      const conditionalRequest = [201, 200].includes(status)

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id, conditionalRequest),
        type: toastTypeSwitch(conditionalRequest)
      })

      return { status: conditionalRequest, data }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'ApportionmentCostCenterModal/_components/TabData/handleSubmit'
      })

      systemToast.addToast({
        message: toastMessageSwitch(messages, payload.id),
        type: 'error'
      })

      return { status: false, data: null }
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  const getDependencies = async ({
    apportionmentTypeId
  }: {
    apportionmentTypeId: number | null
  }) => {
    try {
      systemLoading.setData({ modalLoading: true })

      /** reset */
      listApportionmentMeasuresUnits.set({ list: [] })

      /** update */
      await listApportionmentTypesService.handler()

      await listApportionmentTariffTypesService.handler()

      if (apportionmentTypeId) {
        await listApportionmentMeasuresUnitsService.handler(apportionmentTypeId)
      }
    } catch (error) {
      log.send(loggerRequest, {
        error: error,
        title: 'ApportionmentModal/_components/TabData/getDependencies'
      })
    } finally {
      systemLoading.setData({ modalLoading: false })
    }
  }

  return { handleSubmit, getDependencies }
}
export const useLanguageTabDataModal = () => {
  const { lang } = useSystemLanguageStore().state
  const { validationFields, btn, modal } = languageByMode(lang)
  const { cancel, save, add, clean } = btn
  const { requiredField } = validationFields
  const { title, form } = modal.apportionment.tabData

  return {
    title,
    form: {
      input: {
        name: form.input.name,
        description: form.input.description,
        company: form.input.company,
        consumption: form.input.consumption,
        consumptionInfo: form.input.consumptionInfo,
        measurementUnit: form.input.measurementUnit,
        unitMeasurementNotAvailable: form.input.unitMeasurementNotAvailable,
        tariffType: form.input.tariffType,
        type: form.input.type
      },
      messages: form.messages,
      requiredField,
      btn: { cancel, save, add, clean }
    }
  }
}

/** UTILS */
export const formDataInput = ({
  id,
  name,
  description,
  company,
  apportionmentType,
  apportionmentTariffType,
  apportionmentMeasureUnit,
  mixedConsumption
}: Partial<IApportionmentData>): ITabDataValues => {
  return {
    id: id ?? null,
    name: name ?? '',
    description: description ?? '',
    companies: company ? [company] : [],
    apportionmentType: apportionmentType?.id?.toString?.() ?? '',
    apportionmentTariffType: apportionmentTariffType?.id?.toString?.() ?? '',
    apportionmentMeasureUnit: apportionmentMeasureUnit?.id?.toString?.() ?? '',
    mixedConsumption: mixedConsumption ?? false
  }
}
export const formDataOutput = ({
  id,
  name,
  description,
  companies: [{ id: companyId }],
  apportionmentType,
  apportionmentTariffType,
  apportionmentMeasureUnit,
  mixedConsumption
}: ITabDataValues): ISubmitPayload => {
  return {
    id,
    name,
    description,
    companyId,
    apportionmentTypeId: Number(apportionmentType),
    apportionmentTariffTypeId: Number(apportionmentTariffType),
    apportionmentMeasureUnitId: Number(apportionmentMeasureUnit),
    mixedConsumption
  }
}
