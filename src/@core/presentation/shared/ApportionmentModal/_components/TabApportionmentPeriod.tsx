import { zodResolver } from '@hookform/resolvers/zod'
import dayjs from 'dayjs'
import { useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import useSystemLoadingStore from '@/@core/framework/store/hook/useSystemLoadingStore'
import useSystemStore from '@/@core/framework/store/hook/useSystemStore'
import {
  tabApportionmentPeriodCookie,
  tabCustomTariffsCookie
} from '@/@core/infra/memory/cookie'
import { IModalRootRef, Modal } from '@/@core/presentation/shared/Modal'
import { Button } from '@/@core/presentation/shared/ui/button'
import { Icon } from '@/@core/presentation/shared/ui/icons'
import { Input } from '@/@core/presentation/shared/ui/input'
import { Table } from '@/@core/presentation/shared/ui/table'
import { defineValuesFormFields } from '@/@core/utils/formFields'

import { IApportionmentData } from '../Modal.types'
import {
  formDataInput,
  formDataOutput,
  useLanguageTabApportionmentPeriodModal,
  useMethodsTabApportionmentPeriodModal,
  useStateTabApportionmentPeriodModal
} from './TabApportionmentPeriod.hooks'
import {
  ITabApportionmentPeriodModal,
  ITabApportionmentPeriodValues
} from './TabApportionmentPeriod.types'

export const TabApportionmentPeriod = (props: {
  apportionment: Partial<IApportionmentData>
}) => {
  const isMounted = useRef(false)

  const systemLoading = useSystemLoadingStore()
  const systemStore = useSystemStore()

  const stateTabModal = useStateTabApportionmentPeriodModal()
  const methodsTabModal = useMethodsTabApportionmentPeriodModal()
  const languageTabModal = useLanguageTabApportionmentPeriodModal()

  const searchFields = tabApportionmentPeriodCookie.get()

  const formFields = useTabApportionmentPeriodFormFields()

  const handler = async () => {
    if (
      systemStore.state.mountComponent?.['apportionment-tabApportionmentPeriod']
    )
      return

    await methodsTabModal.fetchData()

    systemStore.setMountComponent('apportionment-tabApportionmentPeriod')
  }

  useEffect(() => {
    if (!isMounted.current) {
      isMounted.current = true
      handler()
      return
    }
    return () => {
      isMounted.current = false
    }
  }, [])

  const onSubmit = async () => {
    const apportionmentId = Number(props.apportionment.id)

    const { status } = await methodsTabModal.handleSubmit(
      formDataOutput({ ...formFields.values, apportionmentId })
    )
    if (!status) return

    await methodsTabModal.fetchData()

    formFields.reset()
  }

  return (
    <>
      <div className="form-container my-4">
        <form
          className="grid lg:grid-cols-3 gap-2"
          id="form-tabCustomTariffs"
          onSubmit={formFields?.handleSubmit?.(onSubmit)}
        >
          <Input.Root>
            <Input.Label>{languageTabModal.form.input.period}</Input.Label>
            <Input.ContentDateMonth
              value={
                formFields.values.period
                  ? dayjs(formFields.values.period).toDate()
                  : undefined
              }
              onChange={(value) =>
                formFields.setValue(
                  'period',
                  dayjs(value).startOf('month').format('YYYY-MM-DD')
                )
              }
              slotEnd={
                <Icon
                  icon="calendar"
                  className="icon-menu-primary"
                  height="24"
                  width="24"
                  viewBox="0 0 20 20"
                />
              }
              helperText={formFields.errors.period?.message}
              disabled={systemLoading.state.modalLoading}
            />
          </Input.Root>
          <Input.Root>
            <Input.Label>{languageTabModal.form.input.periodStart}</Input.Label>
            <Input.ContentDate
              value={
                formFields.values.periodStart
                  ? dayjs(formFields.values.periodStart).toDate()
                  : undefined
              }
              onChange={(value) =>
                formFields.setValue(
                  'periodStart',
                  dayjs(value).format('YYYY-MM-DD')
                )
              }
              slotEnd={
                <Icon
                  icon="calendar"
                  className="icon-menu-primary"
                  height="24"
                  width="24"
                  viewBox="0 0 20 20"
                />
              }
              helperText={formFields.errors.periodStart?.message}
              disabled={systemLoading.state.modalLoading}
            >
              {formFields.values.periodStart
                ? dayjs(formFields.values.periodStart).format('DD/MM/YYYY')
                : ''}
            </Input.ContentDate>
          </Input.Root>
          <Input.Root>
            <Input.Label>{languageTabModal.form.input.periodEnd}</Input.Label>
            <Input.ContentDate
              value={
                formFields.values.periodEnd
                  ? dayjs(formFields.values.periodEnd).toDate()
                  : undefined
              }
              onChange={(value) =>
                formFields.setValue(
                  'periodEnd',
                  dayjs(value).format('YYYY-MM-DD')
                )
              }
              slotEnd={
                <Icon
                  icon="calendar"
                  className="icon-menu-primary"
                  height="24"
                  width="24"
                  viewBox="0 0 20 20"
                />
              }
              helperText={formFields.errors.periodEnd?.message}
              disabled={systemLoading.state.modalLoading}
            >
              {formFields.values.periodEnd
                ? dayjs(formFields.values.periodEnd).format('DD/MM/YYYY')
                : ''}
            </Input.ContentDate>
          </Input.Root>
        </form>

        <div className="footer-form">
          <Button type="button" onClick={() => formFields?.reset?.()}>
            {formFields.values.id
              ? languageTabModal.form.btn.cancel
              : languageTabModal.form.btn.clean}
          </Button>

          <Button type="submit" variant="primary" form="form-tabCustomTariffs">
            {formFields.values.id
              ? languageTabModal.form.btn.save
              : languageTabModal.form.btn.add}
          </Button>
        </div>
      </div>

      <Table.Root classNameWrapper="block">
        <Table.Header>
          <Table.Row>
            <Table.Head>{languageTabModal.table.columns.period}</Table.Head>
            <Table.Head>
              {languageTabModal.table.columns.periodStart}
            </Table.Head>
            <Table.Head>{languageTabModal.table.columns.periodEnd}</Table.Head>
            <Table.Head>{languageTabModal.table.columns.actions}</Table.Head>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {stateTabModal.table.items.map((apportionmentPeriod) => (
            <Table.Row key={apportionmentPeriod.id}>
              <Table.Cell>
                {dayjs(apportionmentPeriod.period).format('MM/YYYY')}
              </Table.Cell>
              <Table.Cell>
                {dayjs(apportionmentPeriod.periodStart).format('DD/MM/YYYY')}
              </Table.Cell>
              <Table.Cell>
                {dayjs(apportionmentPeriod.periodEnd).format('DD/MM/YYYY')}
              </Table.Cell>
              <Table.Cell width={80} role="td-actions">
                <div className="flex items-center gap-2">
                  <ButtonDelete
                    apportionmentPeriodId={apportionmentPeriod.id}
                    resetForm={formFields.reset}
                  />
                  <ButtonEdit
                    data={apportionmentPeriod}
                    onClick={formFields.setValues}
                  />
                </div>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>

        <Table.Paginate
          currentPage={searchFields.page}
          lastPage={stateTabModal.table.lastPage}
          handleChangePage={(page) => {
            tabCustomTariffsCookie.set({
              ...tabCustomTariffsCookie.get(),
              page
            })
            methodsTabModal.fetchData()
          }}
        />
      </Table.Root>
    </>
  )
}
const ButtonDelete = ({
  apportionmentPeriodId,
  resetForm
}: {
  apportionmentPeriodId: number
  resetForm: Function
}) => {
  const modalRef = useRef<IModalRootRef>(null)

  const methodsTabModal = useMethodsTabApportionmentPeriodModal()
  const languageTabModal = useLanguageTabApportionmentPeriodModal()

  const handleClickConfirm = async () => {
    const { status } = await methodsTabModal.handleDelete(apportionmentPeriodId)

    if (!status) return

    await methodsTabModal.fetchData()

    modalRef.current?.close()

    resetForm()
  }

  return (
    <>
      <button
        className="table-td-action hover:cursor-pointer"
        onClick={() => modalRef.current?.open()}
      >
        <Icon
          icon="trash01"
          className="icon-menu-primary"
          height="20"
          width="20"
          viewBox="0 0 20 20"
        />
      </button>

      <Modal.Root ref={modalRef} size="lg2">
        <Modal.Title>{languageTabModal.table.modalDelete.title}</Modal.Title>
        <Modal.Content>
          {languageTabModal.table.modalDelete.textInfo}
        </Modal.Content>
        <Modal.Footer>
          <Button type="button" onClick={() => modalRef.current?.close()}>
            {languageTabModal.table.modalDelete.btn.cancel}
          </Button>

          <Button
            type="button"
            variant="error-primary"
            onClick={handleClickConfirm}
          >
            {languageTabModal.table.modalDelete.btn.confirm}
          </Button>
        </Modal.Footer>
      </Modal.Root>
    </>
  )
}
const ButtonEdit = ({
  data,
  onClick
}: {
  data: ITabApportionmentPeriodModal
  onClick: (payload: Partial<ITabApportionmentPeriodValues>) => void
}) => {
  return (
    <button
      className="table-td-action hover:cursor-pointer"
      onClick={() => {
        onClick(formDataInput(data))
      }}
    >
      <Icon
        icon="edit"
        className="icon-menu-primary"
        height="20"
        width="20"
        viewBox="0 0 20 20"
      />
    </button>
  )
}

const formValuesTabApportionmentPeriodInitial: ITabApportionmentPeriodValues = {
  id: null,
  period: '',
  periodStart: '',
  periodEnd: ''
}
const useTabApportionmentPeriodFormFields = () => {
  const {
    form: { requiredField: message }
  } = useLanguageTabApportionmentPeriodModal()

  const {
    formState: { errors, isSubmitting },
    ...form
  } = useForm({
    resolver: zodResolver(
      z.object({
        id: z.number().nullable(),
        period: z.string().min(1, { message }),
        periodStart: z.string().min(1, { message }),
        periodEnd: z.string().min(1, { message })
      })
    ),
    defaultValues: { ...formValuesTabApportionmentPeriodInitial }
  })

  const values = form.watch()

  const setValues = (payload: Partial<ITabApportionmentPeriodValues>) => {
    defineValuesFormFields(
      form.setValue,
      payload,
      formValuesTabApportionmentPeriodInitial
    )
  }

  return { ...form, values, errors, isSubmitting, setValues }
}
