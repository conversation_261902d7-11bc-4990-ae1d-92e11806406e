import useListApportionmentTypes from '@/@core/framework/store/hook/useListApportionmentTypes'
import { apportionmentTypesApiV3 } from '@/@core/infra/api'
import { http } from '@/@core/infra/http'
import { listApportionmentTypesCookie } from '@/@core/infra/memory/cookie/ListApportionmentTypesCookie'

export const ListApportionmentTypesService = () => {
  const listApportionmentTypes = useListApportionmentTypes()

  const handler = async () => {
    const { list: listCookie } = listApportionmentTypesCookie.get()

    if (listCookie.length) {
      listApportionmentTypes.set({ list: listCookie })
      return
    }

    const { data: listApi } = await apportionmentTypesApiV3(http).get()

    listApportionmentTypesCookie.set({ list: listApi })

    listApportionmentTypes.set({ list: listApi })
  }
  return { handler }
}
