import { renderHook } from '@testing-library/react'
import { act } from 'react'

import useListCompaniesTypesStore from '@/@core/framework/store/hook/useListCompaniesTypesStore'
import {
  appCookie,
  listCompaniesTypesCookie
} from '@/@core/infra/memory/cookie'

import { companiesTypesMock1 } from '@/__mock__/content/api-companies-types.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import { ListCompaniesTypesService } from './listCompaniesTypesService'

jest.mock('@/@core/infra/api/CompaniesTypesApiV4')

const spyCompaniesTypesApiV4 = jest.spyOn(
  require('@/@core/infra/api/CompaniesTypesApiV4'),
  'companiesTypesApiV4'
)

describe('src/@core/services/listCompaniesTypesService', () => {
  beforeEach(() => {
    appCookie.init()
  })
  afterEach(() => {
    appCookie.reset()
  })

  it('should set store with valid cookie data without data in cookie', async () => {
    const { result } = renderHook(
      () => ({
        service: ListCompaniesTypesService(),
        store: useListCompaniesTypesStore()
      }),
      { wrapper: AppStoreProvider }
    )

    spyCompaniesTypesApiV4.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [companiesTypesMock1]
        }
      })
    }))

    expect(listCompaniesTypesCookie.get().list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    await act(async () => {
      await result.current.service.handler()
    })

    expect(listCompaniesTypesCookie.get().list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)
  })

  it('should set store with valid cookie data with data in cookie', async () => {
    listCompaniesTypesCookie.set({
      list: [companiesTypesMock1]
    })

    const { result } = renderHook(
      () => ({
        service: ListCompaniesTypesService(),
        store: useListCompaniesTypesStore()
      }),
      { wrapper: AppStoreProvider }
    )

    await act(async () => {
      result.current.store.set({ list: [] })
    })

    await act(async () => {
      await result.current.service.handler()
    })

    expect(listCompaniesTypesCookie.get().list).toHaveLength(1)
  })
})
