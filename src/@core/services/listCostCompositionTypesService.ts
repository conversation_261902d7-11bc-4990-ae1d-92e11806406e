import useListCostCompositionTypes from '@/@core/framework/store/hook/useListCostCompositionTypes'
import { costCompositionTypesApiV3 } from '@/@core/infra/api/CostCompositionTypesApiV3'
import { http } from '@/@core/infra/http'
import { listCostCompositionTypesCookie } from '@/@core/infra/memory/cookie'

export const ListCostCompositionTypesService = () => {
  const listCostCompositionTypes = useListCostCompositionTypes()

  const handler = async () => {
    const { list: listCookie } = listCostCompositionTypesCookie.get()

    if (listCookie.length) {
      listCostCompositionTypes.set({ list: listCookie })
      return
    }

    const {
      data: { items: listApi }
    } = await costCompositionTypesApiV3(http).get()

    listCostCompositionTypesCookie.set({ list: listApi })

    listCostCompositionTypes.set({ list: listApi })
  }
  return { handler }
}
