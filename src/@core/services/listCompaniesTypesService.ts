import useListCompaniesTypesStore from '@/@core/framework/store/hook/useListCompaniesTypesStore'
import { companiesTypesApiV4 } from '@/@core/infra/api/CompaniesTypesApiV4'
import { http } from '@/@core/infra/http'
import { listCompaniesTypesCookie } from '@/@core/infra/memory/cookie/ListCompaniesTypesCookie'

export const ListCompaniesTypesService = () => {
  const listCompaniesTypesStore = useListCompaniesTypesStore()

  const handler = async () => {
    const { list: listCookie } = listCompaniesTypesCookie.get()

    if (listCookie.length) {
      listCompaniesTypesStore.set({ list: listCookie })
      return
    }

    const {
      data: { items: listApi }
    } = await companiesTypesApiV4(http).get()

    listCompaniesTypesCookie.set({ list: listApi })

    listCompaniesTypesStore.set({ list: listApi })
  }

  return { handler }
}
