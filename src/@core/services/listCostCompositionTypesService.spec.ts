import { renderHook } from '@testing-library/react'
import { act } from 'react'

import useListCostCompositionTypes from '@/@core/framework/store/hook/useListCostCompositionTypes'
import {
  appCookie,
  listCostCompositionTypesCookie
} from '@/@core/infra/memory/cookie'
import {
  costCompositionTypesMock1,
  costCompositionTypesMock2
} from '@/__mock__/content/api-cost-composition-types.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'

import { ListCostCompositionTypesService } from './listCostCompositionTypesService'

jest.mock(
  '@/@core/infra/api/CostCompositionTypesApiV3/CostCompositionTypesApiV3'
)
const spyCostCompositionTypesApiV3 = jest.spyOn(
  require('@/@core/infra/api/CostCompositionTypesApiV3/CostCompositionTypesApiV3'),
  'costCompositionTypesApiV3'
)

describe('src/@core/services/listCostCompositionTypesService', () => {
  beforeEach(() => {
    appCookie.init()
  })
  afterEach(() => {
    appCookie.reset()
  })

  it('should set store with valid cookie data without data in cookie', async () => {
    const { result } = renderHook(
      () => ({
        service: ListCostCompositionTypesService(),
        store: useListCostCompositionTypes()
      }),
      { wrapper: AppStoreProvider }
    )

    spyCostCompositionTypesApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: {
          items: [costCompositionTypesMock1]
        }
      })
    }))

    expect(listCostCompositionTypesCookie.get().list).toHaveLength(0)
    expect(result.current.store.state.list).toHaveLength(0)

    await act(async () => {
      await result.current.service.handler()
    })

    expect(listCostCompositionTypesCookie.get().list).toHaveLength(1)
    expect(result.current.store.state.list).toHaveLength(1)
  })

  it('should set store with valid cookie data with data in cookie', async () => {
    listCostCompositionTypesCookie.set({
      list: [costCompositionTypesMock1, costCompositionTypesMock2]
    })

    const { result } = renderHook(
      () => ({
        service: ListCostCompositionTypesService(),
        store: useListCostCompositionTypes()
      }),
      { wrapper: AppStoreProvider }
    )

    await act(async () => {
      result.current.store.set({ list: [] })
    })

    await act(async () => {
      await result.current.service.handler()
    })

    expect(listCostCompositionTypesCookie.get().list).toHaveLength(2)
  })
})
