import useListApportionmentMeasuresUnits from '@/@core/framework/store/hook/useListApportionmentMeasuresUnits'
import { apportionmentMeasuresUnitsApiV3 } from '@/@core/infra/api/ApportionmentMeasuresUnitsApiV3'
import { http } from '@/@core/infra/http'

export const ListApportionmentMeasuresUnitsService = () => {
  const listApportionmentMeasuresUnits = useListApportionmentMeasuresUnits()

  const handler = async (typeId: number) => {
    const { data: listApi } = await apportionmentMeasuresUnitsApiV3(http).get({
      typeId
    })

    listApportionmentMeasuresUnits.set({ list: listApi })
  }
  return { handler }
}
