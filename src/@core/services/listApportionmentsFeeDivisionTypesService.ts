import useListApportionmentsFeeDivisionTypes from '@/@core/framework/store/hook/useListApportionmentsFeeDivisionTypes'
import { apportionmentsFeeDivisionTypesApiV3 } from '@/@core/infra/api/ApportionmentsFeeDivisionTypesApiV3'
import { http } from '@/@core/infra/http'
import { listApportionmentsFeeDivisionTypesCookie } from '@/@core/infra/memory/cookie'

export const ListApportionmentsFeeDivisionTypesService = () => {
  const listApportionmentsFeeDivisionTypes =
    useListApportionmentsFeeDivisionTypes()

  const handler = async ({ typeId }: { typeId?: number }) => {
    const { list: listCookie } = listApportionmentsFeeDivisionTypesCookie.get()

    if (listCookie.length) {
      listApportionmentsFeeDivisionTypes.set({ list: listCookie })
      return
    }

    const { data: listApi } = await apportionmentsFeeDivisionTypesApiV3(
      http
    ).get({ typeId })

    listApportionmentsFeeDivisionTypesCookie.set({ list: listApi })

    listApportionmentsFeeDivisionTypes.set({ list: listApi })
  }
  return { handler }
}
