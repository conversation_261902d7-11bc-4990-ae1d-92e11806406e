import { act, renderHook } from '@testing-library/react'

import {
  appCookie,
  listApportionmentTypesCookie
} from '@/@core/infra/memory/cookie'
import { apportionmentTypeMock1 } from '@/__mock__/content/api-apportionment-types.content'
import { AppStoreProvider } from '@/provider/AppStoreProvider'
import useListApportionmentTypes from '../framework/store/hook/useListApportionmentTypes'

import { ListApportionmentTypesService } from './listApportionmentTypesService'

jest.mock('@/@core/infra/api/ApportionmentTypesApiV3')
const spyApportionmentTypesApiV3 = jest.spyOn(
  require('@/@core/infra/api/ApportionmentTypesApiV3'),
  'apportionmentTypesApiV3'
)

describe('src/@core/services/ListApportionmentTypesService', () => {
  beforeEach(() => {
    appCookie.init()
  })
  afterEach(() => {
    appCookie.reset()
  })

  it('should set store with valid cookie data without data in cookie', async () => {
    const { result } = renderHook(
      () => ({
        service: ListApportionmentTypesService(),
        store: useListApportionmentTypes()
      }),
      { wrapper: AppStoreProvider }
    )

    spyApportionmentTypesApiV3.mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({
        status: 200,
        data: [apportionmentTypeMock1]
      })
    }))

    expect(result.current.store.state.list).toHaveLength(0)

    await act(async () => {
      await result.current.service.handler()
    })

    expect(result.current.store.state.list).toHaveLength(1)
  })

  it('should set store with valid cookie data with data in cookie', async () => {
    listApportionmentTypesCookie.set({
      list: [apportionmentTypeMock1]
    })

    const { result } = renderHook(
      () => ({
        service: ListApportionmentTypesService(),
        store: useListApportionmentTypes()
      }),
      { wrapper: AppStoreProvider }
    )

    await act(async () => {
      await result.current.service.handler()
    })

    expect(listApportionmentTypesCookie.get().list).toHaveLength(1)
  })
})
