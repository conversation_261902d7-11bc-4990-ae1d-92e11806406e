export type LanguageType = 'en' | 'pt-BR'
export type LanguageOptions = Record<LanguageType, LanguageTree>

type IFormData = Record<string, string>

type IModalData = {
  titleNew: string
  titleEdit: string
  form: IFormData
  messages: IFormMessages
}
type IModalDelete = {
  title: string
  textInfo: string
  textConfirm: string
  textCancel: string
  messages: IRequestMessages
}

export type IFormMessages = {
  createSuccessMessage: string
  createErrorMessage: string
  updateSuccessMessage: string
  updateErrorMessage: string
  deleteSuccessMessage?: string
  deleteErrorMessage?: string
  errorWhenSearchingZipCode?: string
}
type IRequestMessages = {
  successMessage: string
  errorMessage: string
}

type ITableColumns = Record<string, string>

type IModalTexts = Record<string, string>

export type LanguageTree = {
  validationFields: {
    requiredField: string
    invalidEmail: string
    MustContainLeastCaracter: (n: number) => string
    MustContainLeastItems: (n: number, v: string) => string
  }
  errors: {
    request: string
    warning: string
  }
  btn: {
    add: string
    save: string
    update: string
    edit: string
    clean: string
    remove: string
    cancel: string
    close: string
    filter: string
    confirm: string
    continue: string
    back: string
  }
  form: {
    search: string
    selectAll: string
    selectAnOption: string
  }
  table: {
    previous: string
    next: string
    of: string
    withoutData: string
    newRegister: string
    totalRegisters: string
  }
  modalDelete: {
    infoConfirmation: (text: string) => string
    btnCancel: string
    btnConfirm: (text: string) => string
    requestMessage: (text: string) => {
      successMessage: string
      errorMessage: string
    }
  }
  modal: {
    apportionment: {
      titleNew: string
      titleEdit: string
      tabs: string[]
      tabData: {
        title: string
        form: {
          input: IFormData
          messages: IFormMessages
        }
      }
      tabPeriod: {
        title: string
        form: {
          input: IFormData
          messages: IFormMessages
        }
        table: {
          columns: ITableColumns
          modalDelete: {
            title: string
            textInfo: string
            messages: IRequestMessages
          }
        }
      }
      tabAdditionalFees: {
        title: string
        form: {
          input: IFormData
          messages: IFormMessages
        }
        table: {
          columns: ITableColumns
          modalDelete: {
            title: string
            textInfo: string
            messages: IRequestMessages
          }
        }
      }
      tabCustomTariffs: {
        title: string
        form: {
          input: IFormData
          messages: IFormMessages
        }
        table: {
          columns: ITableColumns
          modalDelete: {
            title: string
            textInfo: string
            messages: IRequestMessages
          }
        }
      }
    }
    apportionmentExportData: {
      title: string
      form: {
        input: IFormData
        messages: IRequestMessages
        warningWithoutPeriod: string
        textWithoutPeriod: string
      }
    }
    costCenterMultiple: {
      title: string
      form: {
        inputEquipmentsAll: string
        inputEquipmentsAllCompanyGroup: string
        inputEquipments: string
        inputUsageLimit: string
      }
    }
    costCenter: {
      titleNew: string
      titleEdit: string
    }
    compositionValue: {
      title: string
      form: {
        inputCostCenter: string
        inputEquipments: string
        inputUsageLimit: string
        inputCostCompositionType: string
        inputApportionmentGroup: string
        messages: IRequestMessages
      }
    }
    apportionmentCalculate: {
      title: string
      form: {
        inputCalendar: string
        messages: IRequestMessages
        warningWithoutPeriod: string
        withoutPeriodText: string
      }
    }
    apportionmentGroup: IModalData
  }
  tab: {
    costCenterTabData: {
      title: string
      form: {
        input: IFormData
        messages: IFormMessages
      }
    }
    costCenterTabCustomTariffs: {
      form: {
        input: IFormData
        messages: IFormMessages
      }
      table: {
        columns: ITableColumns
        modalDelete: {
          title: string
          textInfo: string
          messages: IRequestMessages
        }
      }
    }
    costCenterTabCustomAdditionais: {
      form: {
        input: IFormData
        messages: IFormMessages
      }
      table: {
        columns: ITableColumns
        modalDelete: {
          title: string
          textInfo: string
          messages: IRequestMessages
        }
      }
    }
    costCenterTabEquipments: {
      form: {
        input: IFormData
        messages: IFormMessages
      }
      table: {
        columns: ITableColumns
        modalDelete: {
          title: string
          textInfo: string
          messages: IRequestMessages
        }
      }
    }
  }
  pages: {
    developing: string
    notFound: {
      title: string
    }
    accounts: {
      title: string
      btnNew: string
      table: {
        search: {
          fieldStatus: string
          fieldQuery: string
        }
        columns: {
          name: string
          actions: string
        }
        modalDelete: {
          title: string
          textInfo: string
          messages: IRequestMessages
        }
      }
    }
    accountId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        data: string
        subtitleData: string
        entities: string
        personalization: string
        settings: string
        management: string
      }
      tabData: {
        title: string
        form: {
          inputManagement: string
          inputStatus: string
          inputName: string
        }
        formMessages: IFormMessages
      }
      tabPersonalization: {
        titleColorDefault: string // color default
        titleColorSuggestion: string // color suggestion
        buttonApply: string // apply color
        demo: {
          buttonLabel: string
          buttonDefault: string
          buttonPrimary: string
          icon: string
          iconLeft: string
          iconRight: string
          formLabel: string
          fieldTextLabel: string
          fieldText: string
          fieldSelectLabel: string
          fieldSelect: string
          fieldCheckboxLabel: string
          fieldCheckbox: string
          fieldSwitchLabel: string
          fieldSwitch: string
        }
      }
      tabSettings: {
        title: string
        form: {
          inputNotification: string
        }
      }
      tabManagement: {
        title: string
        form: {
          inputAccount: string
        }
        table: {
          columns: {
            name: string
            actions: string
          }
        }
        messages: IRequestMessages
      }
    }
    'agents-ccee': {
      title: string
    }
    alarms: {
      title: string
      btnNew: string
      table: {
        search: {
          fieldStatus: string
          fieldQuery: string
        }
        columns: {
          status: string
          name: string
          actions: string
        }
        tableWithoutData: string
        modalDelete: {
          title: string
          textInfo: string
          textConfirm: string
          textCancel: string
          deleteSuccessMessage: string
          deleteErrorMessage: string
        }
        updateSuccessMessage: string
        updateErrorMessage: string
      }
    }
    alarmsId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        data: string
        subtitleData: string
        rules: string
        monitoring: string
        notification: string
        history: string
      }
      tabData: {
        title: string
        form: {
          input: IFormData
          /** formMessages: IFormMessages */
        }
        formMessages: IFormMessages
      }
      tabRules: {
        title: string
        form: {
          target: {
            title: string
          }
          shot: {
            title: string
            registered: string
          }
          normalized: {
            title: string
            registered: string
          }
          input: IFormData
          /** formMessages: IFormMessages */
        }
      }
      tabMonitoring: {
        title: string
        form: {
          daysWeek: Record<string, string>
          input: IFormData
          /** formMessages: IFormMessages */
        }
      }
      tabNotification: {
        title: string
        form: {
          input: IFormData
          /** formMessages: IFormMessages */
        }
        table: {
          columns: ITableColumns
        }
      }
      tabHistory: {
        title: string
        form: {
          input: IFormData
          /** formMessages: IFormMessages */
        }
        table: {
          columns: ITableColumns
        }
      }
    }
    apportionments: {
      title: string
      btnNew: string
      table: {
        search: {
          fieldQuery: string
        }
        columns: {
          name: string
          company: string
          type: string
          tariff: string
          cdcQuantity: string
          cdcValue: string
          actions: string
        }
        modalDelete: {
          title: string
          textInfo: string
          messages: IRequestMessages
        }
      }
    }
    apportionmentsId: {
      title: string
      dropdownItems: string[]
      sectionApportionment: {
        title: string
        table: {
          columns: ITableColumns
        }
      }
      sectionGroups: {
        title: string
        table: {
          columns: {
            name: string
            equipments: string
            actions: string
          }
          modalDelete: IModalDelete
        }
      }
      sectionCostCenter: {
        title: string
        table: {
          columns: {
            name: string
            tariffs: string
            equipments: string
            actions: string
          }
          modalDelete: IModalDelete
        }
      }
      sectionResultsHistory: {
        title: string
        table: {
          columns: ITableColumns
        }
        graphic: {
          title: string
        }
        withoutData: string
      }
    }
    companies: {
      title: string
      btnNew: string
      table: {
        search: Record<string, string>
        columns: Record<string, string>
        tableWithoutData: string
        modalDelete: {
          title: string
          textInfo: string
          textConfirm: string
          textCancel: string
          deleteSuccessMessage: string
          deleteErrorMessage: string
        }
      }
    }
    companyId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        data: string
        subtitleData: string
        contacts: string
        salesForce: string
      }
      tabData: {
        title: string
        form: IFormData
        formMessages: IFormMessages
      }
      tabContacts: {
        title: string
        form: IFormData
        formMessages: IFormMessages
        table: {
          columns: ITableColumns
          tableWithoutData: string
        }
        modalDelete: IModalDelete
      }
      tabSalesforce: {
        title: string
        form: IFormData
        formMessages: IFormMessages
        table: {
          columns: ITableColumns
          tableWithoutData: string
        }
        modalDelete: IModalDelete
      }
    }
    contracts: {
      title: string
    }
    dashboard: {
      title: string
      createDashboardModal: {
        title: string
        inputReplace: string
        inputReplaceInfo: string
        inputEntity: string
        inputAccount: string
        inputTemplate: string
        inputSearch: string
        submitMessages: IRequestMessages
      }
      deleteDashboardModal: {
        title: string
        inputAccount: string
        inputSearch: string
        submitMessages: {
          successMessage: string
          errorMessage: string
        }
      }
    }
    devices: {
      title: string
      btnNew: string
      formSearch: Record<string, string>
      table: {
        columns: Record<string, string>
        tableWithoutData: string
        modalDelete: {
          title: string
          textInfo: string
          textConfirm: string
          textCancel: string
          deleteSuccessMessage: string
          deleteErrorMessage: string
        }
      }
    }
    devicesId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        data: string
        subtitleData: string
        constant: string
        installation: string
        billings: string
      }
      tabData: {
        title: string
        form: IFormData
        formMessages: IFormMessages
        actionObservation: {
          add: string
          remove: string
        }
        modalObservationData: IModalTexts
        modalObservation: IModalTexts
        modalRemoveObservation: IModalTexts
        requestStatusObservation: IRequestMessages
      }
      tabConstants: {
        title: string
        form: IFormData
        formMessages: IFormMessages
        table: {
          columns: ITableColumns
          tableWithoutData: string
        }
        modalDelete: IModalDelete & {
          textwarning: string
          values: IFormData
        }
      }
      tabInstallations: {
        title: string
        form: IFormData
        formMessages: IFormMessages
        table: {
          columns: ITableColumns
          tableWithoutData: string
        }
        modalDelete: IModalDelete & {
          textwarning: string
          values: IFormData
        }
      }
      tabBillings: {
        title: 'Faturamento'
        form: IFormData
        formMessages: IFormMessages
        table: {
          columns: ITableColumns
        }
      }
    }
    distributors: {
      title: string
      btnNew: string
      table: {
        search: {
          fieldType: string
          fieldQuery: string
          buttonModalUpdateTariffs: string
        }
        columns: ITableColumns
        tableWithoutData: string
        modalDelete: {
          title: string
          textInfo: string
          textwarning: string
          textConfirm: string
          messages: IRequestMessages
        }
      }
      modalTariffs: {
        title: string
        fieldDate: string
      }
    }
    distributorId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        data: string
        subtitleData: string
        tariffs: string
      }
      tabData: {
        title: string
        form: IFormData
        formMessages: IFormMessages
      }
      tabTariffs: {
        title: string
        table: {
          columns: ITableColumns
          tableWithoutData: string
        }
      }
    }
    'energetic-statement': {
      title: string
    }
    equipments: {
      title: string
      btnNew: string
      table: {
        search: {
          fieldCompany: string
          fieldQuery: string
        }
        columns: {
          name: string
          companyName: string
          ruleOperationalTitle: string
          accountName: string
          actions: string
        }
        tableWithoutData: string
        modalDelete: {
          title: string
          textInfo: string
          textwarning: string
          deleteSuccessMessage: string
          deleteErrorMessage: string
        }
        modalReprocessEquipment: {
          title: string
          table: {
            name: string
            company: string
            MustContainLeastText: string
          }
          search: {
            title: string
            inputCompany: string
            inputQuery: string
          }
          form: {
            title: string
            inputInitialDate: string
            inputFinalDate: string
            messages: IRequestMessages
          }
        }
      }
    }
    equipmentsId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        data: string
        subtitleData: string
        telemetry: string
        property: string
        integration: string
      }
      tabData: {
        title: string
        form: {
          inputName: string
          inputCompany: string
          inputRuleOperational: string
          inputDistributor: string
          inputSCDEKey: string
        }
        formMessages: IFormMessages
      }
      tabTelemetry: {
        title: string
        switchTelemetryProcess: string
        switchAutofillMissingTelemetry: string
        switchConsumptionProjectionProcessing: string
        switchModulationProcess: string
        formMessages: IFormMessages
        modal: {
          createDashboard: {
            button: string
            title: string
            inputReplace: string
            inputReplaceInfo: string
            inputTemplate: string
            messages: {
              successMessage: string
              errorMessage: string
            }
          }
          reprocessData: {
            button: string
            title: string
            inputInitialDate: string
            inputFinalDate: string
            inputProperty: string
            checkInfo: string
            messages: IRequestMessages
          }
          checkMissingTelemetry: {
            button: string
            title: string
            alertText: string
            inputInitialDate: string
            inputFinalDate: string
            inputCheck: string
            messages: IRequestMessages
          }
          consumptionProjection: {
            button: string
            title: string
            inputInitialDate: string
            inputFinalDate: string
            messages: IRequestMessages
          }
          clearMeasurementdata: {
            button: string
            title: string
            step1: {
              inputProcessed: string
              inputReprocess: string
              inputInitialDate: string
              inputFinalDate: string
              inputProperties: string
              inputPropertiesWarning: string
            }
            step2: {
              columnData: string
              columnAffectedRows: string
            }
            step3: {
              info1: string
              info2: string
              buttonConfirm: string
            }
            messages: IRequestMessages
          }
          revertTelemetryFilling: {
            button: string
            title: string
            info1: string
            inputInitialDate: string
            inputFinalDate: string
            messages: IRequestMessages
          }
        }
      }
      tabProperty: {
        title: string
        form: {
          inputProperty: string
          inputOffset: string
          inputConversionFactor: string
        }
        formMessages: IFormMessages
        table: {
          columns: {
            name: string
            offset: string
            conversionFactor: string
            actions: string
          }
        }
      }
      tabIntegration: {
        title: string
        form: {
          inputIntegration: string
          inputRoute: string
          inputExternalId: string
        }
        formMessages: IFormMessages
        table: {
          columns: {
            integration: string
            route: string
            externalId: string
            actions: string
          }
        }
      }
    }
    'financial-statement': {
      title: string
    }
    installationDocument: {
      title: string
      btnNew: string
      table: {
        search: {
          fieldStatus: string
          fieldCompany: string
        }
        title: string
        inputQuery: string
        buttonDownload: string
        columns: {
          equipment: string
          actions: string
          identification: string
          date: string
          serialNumber: string
          status: string
        }
        tableWithoutData: string
        modalDelete: {
          title: string
          textInfo: string
          textConfirm: string
          textCancel: string
          deleteSuccessMessage: string
          deleteErrorMessage: string
        }
        errorDownloadingReport: string
      }
      reportSubmissionModal: {
        title: string
        inputEmail: string
        inputCopyEmail: string
        inputMessage: string
        formMessages: IFormMessages
      }
    }
    installationDocumentId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        monitoring: string
        productivity: string
      }
      tabMonitoring: {
        titleFormCustomerInformation: string
        titleFormMeterConfiguration: string
        title: string
        form: IFormData
        formMessages: IFormMessages
      }
      tabProductivity: {
        title: string
        columns: IFormData
        formMessages: IFormMessages
        reportModal: {
          title: string
          dateOfIssue: string
          referenceMonth: string
          customerInformation: {
            titleClient: string
            titleMeter: string
            labelCompany: string
            labelLuc: string
            labelDate: string
            labelNsTelemetry: string
            labelMeterModel: string
            labelProtocol: string
            labelAddress: string
            labelBaudRate: string
            labelParity: string
            labelCurrentRelation: string
            labelPotentialRelation: string
            labelMeterConstant: string
            labelPowerSystem: string
          }
          titleValidation: string
          Representatives: {
            titleRepresentatives: string
            noc: string
            installer: string
          }
          backButton: string
          saveReportButton: string
          sendingButton: string
        }
        imageDisplayModal: {
          imageDescription: string
          closeModalButton: string
        }
        documentUploadModal: {
          title: string
        }
        modalDelete: {
          title: string
          textInfo: string
          textConfirm: string
          textCancel: string
        }
        previewButton: string
        updateErrorMessage: string
        deleteSuccessMessage: string
        deleteErrorMessage: string
      }
    }
    integrations: {
      title: string
      btnNew: string
      table: {
        columns: {
          name: string
          integration: string
          account: string
          actions: string
        }
        tableWithoutData: string
        modalDelete: {
          title: string
          text1: string
          text2: string
          deleteSuccessMessage: string
          deleteErrorMessage: string
        }
      }
    }
    integrationId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        data: string
        subtitleData: string
      }
      tabData: {
        title: string
        form: {
          account: string
          name: string
          integration: string
          token: string
          username: string
          password: string
          frequency: string
          delay: string
        }
        formMessages: IFormMessages
      }
    }
    monitoring: {
      title: string
      table: {
        search: {
          fieldStatus: string
          fieldType: string
          fieldQuery: string
        }
        columns: {
          equipmentId: string
          equipmentName: string
          serialNumber: string
          status: string
          version: string
          lastReading: string
          lastAlarm: string
          template: string
          company: string
          distributor: string
          location: string
          connection: string
          actions: string
        }
        modalMeasurementCount: {
          title: string
          table: {
            name: string
            company: string
            MustContainLeastText: string
          }
          search: {
            title: string
            inputStatus: string
            inputType: string
            inputQuery: string
          }
          form: {
            title: string
            inputInitialDate: string
            inputFinalDate: string
            messages: IRequestMessages
          }
        }
        modalMonitoringDownload: {
          title: string
          form: {
            inputProbeList: string
            inputCountList: string
            messages: IRequestMessages
          }
        }
      }
    }
    'monitor-alarms': {
      title: string
    }
    'physical-assets': {
      title: string
    }
    'probes-map': {
      title: string
    }
    properties: {
      title: string
      btnNew: string
      table: {
        search: {
          fieldQuery: string
        }
        columns: {
          name: string
          displayName: string
          actions: string
          type: string
        }
        tableWithoutData: string
        modalDelete: {
          title: string
          textInfo: string
          deletionConfirmationText: string
          textConfirm: string
          textCancel: string
          messages: IRequestMessages
        }
      }
    }
    propertyId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        data: string
        subtitleData: string
      }
      tabData: {
        title: string
        form: {
          name: string
          displayName: string
          description: string
          subTypes: string
          storeable: string
          type: string
          processable: string
        }
        formMessages: IFormMessages
      }
    }
    reports: {
      title: string
      tabs: {
        reports: string
        generate: string
        historical: string
      }
      tabReports: {
        panel: {
          title: string
          subTitle: string
          buttonGenerateReport: string
        }
        latest: {
          title: string
          seeMore: string
        }
        errorRequest: string
      }
      tabHistorical: {
        title: string
        table: {
          search: {
            fieldQuery: string
          }
          columns: {
            name: string
            update: string
            entity: string
            typeMeasurements: string
            actions: string
          }
          modalDelete: {
            title: string
            textInfo: string
            messages: IRequestMessages
          }
        }
      }
      tabGenerate: {
        form: {
          title: string
          entity_fields: string
          entity_type_fields: string
          entity_data_fields: string
          period_fields: string
          aggregate_fields: string
          consumption: string
          demand: string
          power_factor: string
          consumption_account: string
          input: {
            'entity_fields.entity': string
            'entity_type_fields.typeMeasurement': string
            'entity_type_fields.account_type': string
            'entity_data_fields.entity_data': string
            'entity_data_fields.entity_data_group': string
            'entity_data_fields.entity_data_account': string
            'period_fields.initial_date': string
            'period_fields.final_date': string
            'period_fields.syntax_date': string
            'aggregate_fields.date_interval': string
            'aggregate_fields.date_interval_number': string
            'aggregate_fields.type': string
            'type_data.consumption.tariff_post': string
            'type_data.consumption.capacity': string
            'type_data.consumption.consumption': string
            'type_data.consumption.greatness': string
            'type_data.demand.tariff_post': string
            'type_data.demand.capacity': string
            'type_data.demand.consumption': string
            'type_data.power_factor.tariff_post': string
            'type_data.power_factor.capacity': string
          }
          generateReport: string
          requiredFinalDate: string
          requiredTypeData: string
          downloadCompact: string
          downloadDetailed: string
        }
        table: {
          titleInfo: string
          titleTable: string
          infoGenerateReport: string
          infoOrderDate: string
          infoRequestedPeriod: string
        }
        modalSave: {
          form: {
            input: {
              name: string
            }
            messages: IFormMessages
          }
        }
      }
    }
    statistics: {
      title: string
      btnDownload: string
      indicatorNames: Record<string, string>
      tabs: {
        productivity: string
        monitoring: string
      }
      tabProductivity: {}
      tabMonitoring: {}
    }
    statisticsProductivityIndicator: {
      title: string
      table: {
        columns: {
          name: string
        }
        tableWithoutData: string
      }
    }
    users: {
      title: string
      btnNew: string
      table: {
        search: {
          fieldAccounts: string
          fieldQuery: string
        }
        columns: {
          name: string
          email: string
          account: string
          lastAcess: string
          actions: string
        }
        tableWithoutData: string
        modalDelete: {
          title: string
          textInfo: string
          textConfirm: string
          textCancel: string
          deleteSuccessMessage: string
          deleteErrorMessage: string
        }
      }
    }
    userId: {
      title: string
      titleNew: string
      titleEdit: string
      tabs: {
        data: string
        subtitleData: string
        permissions: string
        entities: string
      }
      tabData: {
        title: string
        form: {
          adminField: string
          activeField: string
          inputFirstName: string
          inputLastName: string
          inputEmail: string
          inputPincode: string
          inputCellPhone: string
          inputPassword: string
          inputPasswordConfirmation: string
          inputAccount: string
          fieldValidation: {
            inputCellPhone: string
            inputPasswordConfirmation: string
          }
          passwordValidation: {
            passwordValidationTitle: string
            minimumCharacters: string
            maximumCharacters: string
            number: string
            capitalLetter: string
            lowercaseLetter: string
            specialCharacter: string
            differentPasswords: string
          }
        }
        formMessages: IFormMessages
      }
      tabPermissions: {
        title: string
        columns: {
          permission: string
          list: string
          create: string
          delete: string
        }
        formMessages: {
          createPermissionSuccessMessage: string
          createPermissionErrorMessage: string
        }
      }
      tabEntities: {
        title: string
        informationMessage: string
        formSearch: {
          fieldAccount: string
          fieldQuery: string
        }
        formMessages: {
          createEntitySuccessMessage: string
          createEntityErrorMessage: string
        }
        entitiesNotFound: string
        entitiesEmpty: string
      }
    }
  }
  layout: {
    header: {
      menu: {
        menuItemHello: string
        menuItemEditProfile: string
        menuItemLogout: string
      }
    }
    modal: {
      progressDeleteAccount: {
        title: string
        step1: {
          text1: string
          text2: string
          btn: string
        }
        step2: {
          text1: string
          btn: string
        }
      }
    }
  }
  pwa: {
    install: string
    installed: string
    installUnavailable: string
  }
  chart: {
    noData: string
  }
  notifications: {
    title: string
    defaulterClient: string
  }
}
