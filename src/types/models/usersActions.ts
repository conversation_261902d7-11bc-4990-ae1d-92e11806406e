export type UserActionModuleNameType =
  | 'agentes-ccee'
  | 'alarme'
  | 'balanço-energético'
  | 'balanço-financeiro'
  | 'contas'
  | 'contratos'
  | 'dashboard'
  | 'dispositivo'
  | 'dispositivos-alarmados'
  | 'distribuidoras'
  | 'empresa'
  | 'equipamento'
  | 'mapa'
  | 'monitoramento'
  | 'propriedades'
  | 'rateios'
  | 'relatórios'
  | 'setores'
  | 'unidades'

export interface UserActionSubmodule {
  id: number
  name: string
  slug: string
}
export interface UserAction {
  id: number
  action: {
    id: number
    name: string
  }
  // actionId: number
  // actionName: string
  module: {
    id: number
    name: UserActionModuleNameType
    slug: string
  }
  // moduleId: number
  // moduleName: string
  // moduleSlug: string
  submodule: UserActionSubmodule | null
  // submoduleId: number
  // submoduleName: string
  accountUserId: number
  permissionsIds: number[]
}

export interface UserActionResponse {
  id: number
  action: {
    id: number
    name: string
  }
  module: {
    id: number
    name: UserActionModuleNameType
    slug: string
  }
  submodule: UserActionSubmodule | null
}
