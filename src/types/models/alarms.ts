import { Order } from '../system'

export interface AlarmSearch {
  _q: string
  _page: number
  _limit: number
  _order: Order
  _sort: 'id' | string
  statusIds: number
}

export interface AlarmModule {
  id: number
  name: string
}
export interface AlarmStatus {
  id: number
  name: string
}
export interface Alarm {
  id: number
  name: string
  description: string
  timeConfirmation: number
  initialHour: string | null
  finalHour: string | null
  daysWeek: number[] | null
  daysRetention: number
  status: boolean
  accountId: {
    id: number
    name: string | null
  }
  category: {
    id: number
    name: string
  }
}
export interface AlarmResponse {
  id: number
  name: string
  description: string
  timeConfirmation: number
  initialHour: string | null
  finalHour: string | null
  daysWeek: number[] | null
  daysRetention: number
  status: boolean
  accountId: {
    id: number
    name: string | null
  }
  category: {
    id: number
    name: string
  }
}

export interface AccountId extends Alarm {}
export interface AccountIdResponse extends AlarmResponse {}

export interface AlarmPostPayload {
  form: {
    alarmsCategoryId: number
    name: string
    description: string
    timeConfirmation: number
    initialHour: string
    finalHour: string
    daysWeek: number[]
    daysRetention: number
    status: boolean
    accountId: number
  }
}
export interface AlarmPostResponse extends AlarmResponse {}

export interface AlarmPutPayload {
  id: number
  form: {
    name: string
    description: string
    timeConfirmation: number
    initialHour: string
    finalHour: string
    daysWeek: number[]
    daysRetention: number
    status: boolean
  }
}

export interface AlarmPutResponse extends AlarmResponse {}

export interface AlarmDeletePayload {
  id: number
}

export interface AlarmDeleteResponse {}
