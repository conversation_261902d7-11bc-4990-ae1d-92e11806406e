import { Order } from '../system'

export interface AccountSearch {
  _q: string
  _page: number
  _limit: number
  _order: Order
  _sort: 'id'
  statusIds: number[]
}

export interface AccountModule {
  id: number
  name: string
}
export interface AccountStatus {
  id: number
  name: string
}
export interface Account {
  id: number
  name: string
  modules: AccountModule[]
  modulesIds: number[]
  statusId: number | null
  status: AccountStatus | null
}
export interface AccountResponse {
  id: number
  name: string
  modules: AccountModule[]
  status: AccountStatus
}

export interface AccountId extends Account {}
export interface AccountIdResponse extends AccountResponse {}

export interface AccountPostPayload {
  form: {
    name: string
    moduleIds: number[]
  }
}
export interface AccountPostResponse extends AccountResponse {}

export interface AccountPutPayload {
  id: number
  form: {
    name: string
    moduleIds: number[]
    account_status_id: number
  }
}

export interface AccountPutResponse extends AccountResponse {}

export interface AccountDeletePayload {
  id: number
  form: { password: string }
}

export interface AccountDeleteResponse {}
