import type { NextPage } from 'next'
import type { AppProps } from 'next/app'

export type AppPage<
  P = {
    layout?: LayoutType
  },
  IP = P
> = NextPage<P, IP> & {
  layout?: LayoutType
}

export type AppPropsLayout = AppProps & {
  Component: AppPage
}

export type LayoutType = 'private' | 'public'

export type StatusMenuDesktop = 'open' | 'close' | 'float'

export type StatusMenuMobile = 'open' | 'close'

export type PanelPosition = 'left' | 'right'

export type PanelType = 'AccountFormSearchPanel'

export type IMenuItem = {
  id: number
  name: string
  slug: string
  to: string
  active: boolean
  order: number
  showSubmodule: boolean
  subModules: IMenuSubItem[]
  prefixLegacy?: string
  prefixBeta?: string
  onlyDesktop?: boolean
}

export type IMenuSubItem = {
  id: number
  name: string
  slug: string
  to: string
  active: boolean
  order: number
  prefixLegacy?: string
  prefixBeta?: string
  onlyDesktop?: boolean
}
