import { GetServerSidePropsContext } from 'next'

export interface ContextSSR extends GetServerSidePropsContext {}

export type LoadingThunkType = 'idle' | 'pending' | 'succeeded' | 'failed'

export type Order = 'desc' | 'asc'

export interface IProgressDeleteAccount {
  onSuccessDelete: () => void
  id: number
  name: string
}
export type ModalContentStatus = 'initial' | 'show' | 'hiding' | 'hidden'
export interface ModalContentOptions {
  progressDeleteAccount: IProgressDeleteAccount
}
export interface ModalContent {
  status: ModalContentStatus
  active: keyof ModalContentOptions | null
  options: ModalContentOptions
}
