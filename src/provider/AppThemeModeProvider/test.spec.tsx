import React from 'react'
import { act, cleanup, screen } from '@testing-library/react'

import { useThemeMode } from '@/hooks/useThemeMode'
import { render } from '@/utils/setupTest'

import { AppThemeModeProvider } from './index'

cleanup()

const ComponentTest = () => {
  const { toggleThemeMode } = useThemeMode()

  return (
    <div data-testid="component-test">
      <button data-testid="btn" onClick={() => toggleThemeMode()}>
        change mode
      </button>

      <h1>component Test</h1>
    </div>
  )
}

describe('src/provider/AppThemeModeProvider/index.tsx', () => {
  test('click using toggleThemeMode', async () => {
    const toggleThemeModeMock = jest.fn()

    render(
      <AppThemeModeProvider toggleThemeMode={toggleThemeModeMock}>
        <ComponentTest />
      </AppThemeModeProvider>
    )

    await act(async () => {
      const el = await screen.findByTestId('btn')
      el.click()
    })

    expect(toggleThemeModeMock).toBeCalledTimes(1)
  })
})
