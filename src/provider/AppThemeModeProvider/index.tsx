import { ThemeModeContext } from '@/context/themeModeContext'
import { useMemo } from 'react'

type Props = {
  children: React.ReactNode
  toggleThemeMode: () => void
}
export const AppThemeModeProvider = ({ children, toggleThemeMode }: Props) => {
  const value = useMemo(() => ({ toggleThemeMode }), [])

  return (
    <ThemeModeContext.Provider value={value}>
      {children}
    </ThemeModeContext.Provider>
  )
}
