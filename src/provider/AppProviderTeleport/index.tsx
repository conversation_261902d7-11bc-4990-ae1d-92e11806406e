import { TeleportContext } from '@/context/teleportContext'
import { TeleportType } from '@/types/teleport'
import { FC, ReactNode, useMemo, useState } from 'react'

export const ProviderTeleport: FC<{ children: ReactNode }> = ({ children }) => {
  const [teleportMap, setTeleportMap] = useState(new Map())

  const addTeleportItem = (portalType: TeleportType, component: ReactNode) => {
    teleportMap.set(portalType, component)

    setTeleportMap(new Map(teleportMap))
  }

  const removeTeleportItem = (portalType: TeleportType) => {
    teleportMap.delete(portalType)

    setTeleportMap(new Map(teleportMap))
  }

  const value = useMemo(
    () => ({ teleportMap, addTeleportItem, removeTeleportItem }),
    [teleportMap]
  )

  return (
    <TeleportContext.Provider value={value}>
      {children}
    </TeleportContext.Provider>
  )
}
