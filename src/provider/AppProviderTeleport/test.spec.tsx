import React from 'react'
import { render, act, cleanup } from '@testing-library/react'

import { TeleportContext } from '@/context/teleportContext'

import { ProviderTeleport } from './index'

cleanup()

describe('ProviderTeleport', () => {
  let setTestedComponent: any

  const TestComponent: React.FC = () => {
    const { teleportMap, addTeleportItem, removeTeleportItem } =
      React.useContext(TeleportContext)

    setTestedComponent = { teleportMap, addTeleportItem, removeTeleportItem }

    return null
  }

  it('should add and remove items correctly', () => {
    render(
      <ProviderTeleport>
        <TestComponent />
      </ProviderTeleport>
    )

    act(() => {
      setTestedComponent.addTeleportItem('titlePage', <div>Test</div>)
    })

    expect(setTestedComponent.teleportMap.get('titlePage')).toBeDefined()

    act(() => {
      setTestedComponent.removeTeleportItem('titlePage')
    })

    expect(setTestedComponent.teleportMap.get('titlePage')).toBeUndefined()
  })
})
