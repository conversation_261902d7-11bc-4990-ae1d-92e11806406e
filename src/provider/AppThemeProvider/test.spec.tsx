import { cleanup, render } from '@testing-library/react'
import { AppThemeProvider } from '.'

cleanup()

const ComponentTest = () => {
  return <>Hello</>
}
describe('src/provider/AppThemeProvider/index.tsx', () => {
  test('render provider mode dark', () => {
    render(
      <AppThemeProvider mode="dark">
        <ComponentTest />
      </AppThemeProvider>
    )
  })

  test('render provider', () => {
    render(
      <AppThemeProvider mode="light">
        <ComponentTest />
      </AppThemeProvider>
    )
  })
})
