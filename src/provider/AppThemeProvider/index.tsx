import { useMemo } from 'react'
import { createTheme, Theme<PERSON>rovider, PaletteMode } from '@mui/material'
import { ThemeModeTypes, ZordonThemeOptions } from '@/types/theme'

declare module '@mui/material/styles' {
  interface Palette {
    primaryLight: 'red'
  }
  interface PaletteOptions {
    primaryLight: 'red'
  }
}

const getDesignMode = (mode: PaletteMode): ZordonThemeOptions => ({
  palette: {
    mode,
    ...(mode === 'light'
      ? {
          primary: {
            main: 'rgba(53, 165, 91, 1)'
          },
          secondary: {
            main: 'rgba(130, 134, 139, 1)'
          },
          success: {
            main: 'rgba(40, 199, 111, 1)'
          },
          error: {
            main: 'rgba(234, 84, 85, 1)'
          },
          warning: {
            main: 'rgba(255, 159, 67, 1)'
          },
          info: {
            main: 'rgba(0, 207, 232, 1)'
          },
          dark: {
            main: 'rgba(75, 75, 76, 1)'
          }
        }
      : {
          primary: {
            main: 'rgba(53, 165, 91, 1)'
          },
          secondary: {
            main: 'rgba(130, 134, 139, 1)'
          },
          success: {
            main: 'rgba(40, 199, 111, 1)'
          },
          error: {
            main: 'rgba(234, 84, 85, 1)'
          },
          warning: {
            main: 'rgba(255, 159, 67, 1)'
          },
          info: {
            main: 'rgba(0, 207, 232, 1)'
          },
          dark: {
            main: 'rgba(75, 75, 75, 1)'
          }
        }),
    primaryLight: 'red'
  },
  paletteZordon: {
    mode,
    ...(mode === 'light'
      ? {
          background1: 'rgba(248, 248, 248, 1)'
        }
      : {
          background1: 'rgba(37, 36, 41, 1)'
        })
  }
})

type Props = {
  children: React.ReactNode
  mode: ThemeModeTypes
}
export const AppThemeProvider = ({ children, mode }: Props) => {
  const theme = useMemo(() => createTheme(getDesignMode(mode)), [mode])

  return <ThemeProvider theme={theme}> {children}</ThemeProvider>
}
