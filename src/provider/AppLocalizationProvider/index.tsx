import { LocalizationProvider as LocalizationProviderMUI } from '@mui/x-date-pickers'
import { AdapterDayjs as AdapterDayjsMUI } from '@mui/x-date-pickers/AdapterDayjs'
import { ReactNode } from 'react'
import 'dayjs/locale/pt-br'

export const AppLocalizationProvider = ({
  children
}: {
  children: ReactNode
}) => {
  return (
    <LocalizationProviderMUI
      dateAdapter={AdapterDayjsMUI}
      adapterLocale="pt-br"
    >
      {children}
    </LocalizationProviderMUI>
  )
}
