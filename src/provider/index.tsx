import { usePrepareInstallPWA } from '@/hooks/usePreparePWA'
import { ThemeModeTypes } from '@/types/theme'
import { useState } from 'react'
import { AppLocalizationProvider } from './AppLocalizationProvider'
import { ProviderTeleport } from './AppProviderTeleport'
import { AppStoreProvider } from './AppStoreProvider'
import { AppThemeModeProvider } from './AppThemeModeProvider'
import { AppThemeProvider } from './AppThemeProvider'

export const AppProvider = ({ children }: { children: React.ReactNode }) => {
  const [mode, setMode] = useState<ThemeModeTypes>('light')

  const toggleThemeMode = () => {
    setMode(mode === 'light' ? 'dark' : 'light')
  }

  usePrepareInstallPWA()

  return (
    <AppThemeModeProvider toggleThemeMode={toggleThemeMode}>
      <AppThemeProvider mode={mode}>
        <AppStoreProvider>
          <ProviderTeleport>
            <AppLocalizationProvider>
              <>{children}</>
            </AppLocalizationProvider>
          </ProviderTeleport>
        </AppStoreProvider>
      </AppThemeProvider>
    </AppThemeModeProvider>
  )
}
