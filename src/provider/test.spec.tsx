import React from 'react'
import { act, cleanup, screen } from '@testing-library/react'

import { useThemeMode } from '@/hooks/useThemeMode'
import { render } from '@/utils/setupTest'
import { ThemeModeTypes } from '@/types/theme'

import { AppProvider } from './index'

cleanup()

const spyUseState = jest.spyOn(React, 'useState')

jest.mock('./AppStoreProvider', () => ({
  AppStoreProvider: (p: { children: React.ReactNode }) => (
    <div>{p.children}</div>
  )
}))
jest.mock('./AppThemeProvider', () => ({
  AppThemeProvider: (p: { children: React.ReactNode }) => (
    <div>{p.children}</div>
  )
}))

const ComponentTest = () => {
  const { toggleThemeMode } = useThemeMode()

  return (
    <div data-testid="component-test">
      <button data-testid="btn" onClick={() => toggleThemeMode()}>
        change mode
      </button>

      <h1>component Test</h1>
    </div>
  )
}

describe('src/provider/', () => {
  test('render component', async () => {
    render(
      <AppProvider>
        <ComponentTest />
      </AppProvider>
    )

    const el = await screen.findByTestId('component-test')

    expect(el).toBeInTheDocument()
  })

  // test('click theme mode', async () => {
  //   jest.spyOn(React, 'useState').mockImplementation(() => ['dark', jest.fn()])

  //   const { findByTestId } = render(
  //     <AppProvider>
  //       <ComponentTest />
  //     </AppProvider>
  //   )

  //   const el = await findByTestId('btn-change-mode')
  //   el.click()
  // })

  test('click theme mode light', async () => {
    let mode: ThemeModeTypes = 'light'

    const setMode = () => (mode = 'dark')

    spyUseState.mockImplementationOnce(() => ['light', setMode])

    render(
      <AppProvider>
        <ComponentTest />
      </AppProvider>
    )

    await act(async () => {
      const el = await screen.findByTestId('btn')
      el.click()
    })

    expect(mode).toEqual('dark')
  })

  test('click theme mode dark', async () => {
    let mode: ThemeModeTypes = 'dark'

    const setMode = () => (mode = 'light')

    jest.spyOn(React, 'useState').mockImplementation(() => ['dark', setMode])

    render(
      <AppProvider>
        <ComponentTest />
      </AppProvider>
    )

    await act(async () => {
      const el = await screen.findByTestId('btn')
      el.click()
    })

    expect(mode).toEqual('light')
  })
})
