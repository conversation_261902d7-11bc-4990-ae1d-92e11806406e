@font-face {
  font-family: 'AcuminPro-Black';
  src: url('/fonts/acuminPro/AcuminPro-Black.ttf') format('truetype');
}

@font-face {
  font-family: 'AcuminPro-Bold';
  src: url('/fonts/acuminPro/AcuminPro-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'AcuminPro-ExtraLight';
  src: url('/fonts/acuminPro/AcuminPro-ExtraLight.ttf') format('truetype');
}

@font-face {
  font-family: 'AcuminPro-Light';
  src: url('/fonts/acuminPro/AcuminPro-Light.ttf') format('truetype');
}

@font-face {
  font-family: 'AcuminPro-Medium';
  src: url('/fonts/acuminPro/AcuminPro-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'AcuminPro-Regular';
  src: url('/fonts/acuminPro/AcuminPro-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'AcuminPro-Semibold';
  src: url('/fonts/acuminPro/AcuminPro-Semibold.ttf') format('truetype');
}

@font-face {
  font-family: 'AcuminPro-Thin';
  src: url('/fonts/acuminPro/AcuminPro-Thin.ttf') format('truetype');
}

* {
  box-sizing: border-box;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-track {
    @apply bg-gray-400/25;
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb {
    @apply bg-gray-400/25;
    border-radius: 4px;
  }
}

html,
body {
  padding: 0;
  margin: 0;
  min-width: 320px;
}
body {
  @apply bg-primary font-acuminPro-Regular antialiased;
}

@media (max-width: 900px) {
  body {
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    /* Using safe areas with fallback values */
    margin: env(safe-area-inset-top, 5px) env(safe-area-inset-bottom, 5px) !important;
  }
}

.max-w-desktop {
  @apply max-w-[1800px];
}

#__next {
  @apply relative h-screen overflow-y-hidden;
}

.t-containerEdge {
  @apply relative;

  &--top,
  &--bottom {
    @apply hidden md:block;
    position: relative;

    &:before {
      @apply bg-[#EAECF0];
      content: '';
      display: block;
      width: 30px;
      height: 1px;
      position: absolute;
      transform: rotate(315deg);
    }
  }
  &--top {
    @apply bg-[#FCFCFD] border-[1px] border-b-transparent -mb-[1px] h-[20px];
    clip-path: polygon(
      20px 0,
      100% 0,
      100% 100%,
      calc(100% - 20px) 100%,
      0 100%,
      0 20px
    );

    &:before {
      top: 9px;
      left: -5px;
    }
  }
  &--bottom {
    @apply bg-[#FCFCFD] border-[1px] border-t-transparent -mt-[1px] mb-3 h-[24px];
    clip-path: polygon(
      100% 0,
      100% calc(100% - 20px),
      calc(100% - 20px) 100%,
      0 100%,
      0 0
    );

    &:before {
      bottom: 9px;
      right: -5px;
    }
  }
}

td.auto {
  width: 100%;
}

.table-td-actions {
  @apply h-[20px] flex items-center justify-end gap-2;
}

.table-td-action {
  @apply min-w-[20px] min-h-[20px] inline-flex items-center justify-center p-1 rounded text-gray-700 dark:text-comerc-grayLight-300;
}

/** Labels: Input.Controll, Select.Controll, Select.Root */
.MuiFormLabel-root.MuiInputLabel-root {
  @apply text-zinc-500;

  &.Mui-focused {
    color: var(--palette-600);
  }
}

/** Border: Input.Controll, Select.Controll, Select.Root */
.MuiInputBase-root {
  border-radius: 2px !important;
}
.MuiInputBase-root.MuiOutlinedInput-root.Mui-focused
  .MuiOutlinedInput-notchedOutline {
  border-color: var(--palette-600);
  border-width: 1px;
}

/** Input.Root */
.InputRoot {
  > label {
    @apply text-zinc-500;
  }
  .MuiFormControl-root {
    display: flex;
  }
  .MuiFormHelperText-root {
    margin: 0.1785rem 0.25rem;
  }
  .Mui-focused .MuiOutlinedInput-notchedOutline {
    border-color: var(--palette-600);
  }
}

/** Checkbox.Content */
.MuiButtonBase-root.MuiCheckbox-root.Mui-checked {
  color: var(--palette-600);
}

/** Switch.Content */
.MuiButtonBase-root.MuiSwitch-switchBase {
  &.Mui-checked {
    color: var(--palette-600);

    & + .MuiSwitch-track {
      background-color: var(--palette-600);
    }
  }
}

.MuiTabs-root {
  .MuiTabs-indicator {
    display: none;
  }

  .MuiTabs-scroller {
    .MuiButtonBase-root {
      min-width: unset;
      min-height: unset;
    }
  }
}

.MuiPickersMonth-monthButton.Mui-selected {
  background-color: var(--palette-600) !important;
}

.grid-wrapper {
  [data-icon-key] {
    @apply duration-150 ease-linear rotate-0;

    &[data-icon-status='false'] {
      @apply -rotate-180;
    }
  }
  [data-content-key] {
    @apply duration-200;
    &[data-content-status='false'] {
      @apply opacity-0 max-h-0 overflow-hidden;
    }
  }

  .grid-checkbox {
    &.row-header {
      background-color: var(--bg-comerc-grayLight-300);
    }

    &.row-primary {
      @apply bg-comerc-grayLight-200 dark:bg-comerc-grayLight-800;
    }

    &.row-secondary {
      &:hover {
        @apply bg-comerc-grayLight-100 dark:bg-comerc-grayLight-700;
      }
    }
  }
}

.button-color {
  input {
    @apply hidden;
  }
  label {
    @apply w-10 h-10 flex rounded-full cursor-pointer border-2 border-transparent;
  }
  span {
    @apply w-7 h-7 block rounded-full m-auto;
  }

  &.--default {
    label.active {
      border-color: #309752;
    }
    span {
      background-color: #309752;
    }
  }
  &.--red {
    label.active {
      border-color: red;
    }
    span {
      background-color: red;
    }
  }
  &.--blue {
    label.active {
      border-color: blue;
    }
    span {
      background-color: blue;
    }
  }
}

.MuiButtonBase-root.MuiTab-root.Mui-selected {
  color: var(--palette-600);
}

.MuiTabs-vertical .MuiTabs-flexContainer {
  align-items: start;
}

.statistics-cardEdge {
  @apply border-[1px]  border-zinc-300 shadow bg-white py-5;
  position: relative;
  clip-path: polygon(
    20px 0,
    100% 0,
    100% calc(100% - 20px),
    calc(100% - 20px) 100%,
    0 100%,
    0 20px
  );

  &::after,
  &::before {
    @apply bg-white border-y-[1px] border-zinc-300;
    content: '';
    display: block;
    width: 30px;
    height: 15px;
    position: absolute;
    transform: rotate(315deg);
  }

  &::before {
    top: -2.5px;
    left: -10px;
  }

  &::after {
    bottom: -3px;
    right: -10px;
  }
}

.app-toast {
  z-index: 50;
  position: absolute;
  overflow: hidden;

  @media (max-width: 900px) {
    @apply top-5 right-5;
  }
  @media (min-width: 900px) {
    &.--topLeft,
    &.--topCenter,
    &.--topRight {
      @apply top-5;
    }

    &.--bottomLeft,
    &.--bottomCenter,
    &.--bottomRight {
      @apply bottom-5;
    }

    &.--left,
    &.--topLeft,
    &.--bottomLeft {
      &.--min-float {
        @apply left-[100px];
      }
      &.--max-float {
        @apply left-[300px];
      }
    }

    &.--right,
    &.--topRight,
    &.--bottomRight {
      @apply right-5;
    }

    &.--left,
    &.--right {
      @apply top-[50%] translate-y-[-50%];
    }

    &.--center {
      @apply top-[50%] left-[50%] translate-y-[-50%] translate-x-[-50%];
    }

    &.--topCenter {
      @apply left-[50%];
    }

    &.--topCenter,
    &.--bottomCenter {
      @apply left-[50%];
      @apply left-[50%] translate-x-[-50%];
    }
  }
}

.app-menuLayout {
  &::-webkit-scrollbar {
    width: 2px;
  }
  &::-webkit-scrollbar-track {
    background: var(--palette-400);
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.25);
  }
}

.MuiPaginationItem-ellipsis {
  display: none;
}

.cell-device-status {
  &.is-connected {
    @apply border-comerc-primary-500 text-comerc-primary-700 bg-comerc-primary-25;
  }
  &.is-disconnected {
    @apply border-comerc-grayLight-500 text-comerc-grayLight-700 bg-comerc-gray-25;
  }
  &.is-alarm {
    @apply border-comerc-error-500 text-comerc-error-700 bg-comerc-error-25;
  }
  &.is-under_observation {
    @apply border-comerc-warning-500 text-comerc-warning-700 bg-comerc-warning-25;
  }
  &.is-stand_by {
    @apply border-comerc-tertiary-500 text-comerc-tertiary-700 bg-comerc-tertiary-25;
  }
}

.cell-company-type {
  &.is-company {
    @apply border-comerc-primary-500 text-comerc-primary-700 bg-comerc-primary-50;
  }
  &.is-unit {
    @apply border-comerc-warning-500 text-comerc-warning-700 bg-comerc-warning-50;
  }
  &.is-sector {
    @apply border-comerc-tertiary-500 text-comerc-tertiary-700 bg-comerc-tertiary-50;
  }
  &.is-default {
    @apply border-comerc-grayLight-500 text-comerc-grayLight-700 bg-comerc-gray-50;
  }
}

.menu-equipment-icon-status {
  &.is-connected {
    @apply border-[#5294294d] fill-[#9fd87c];
  }
  &.is-disconnected {
    @apply border-[#0000004d] fill-[#000000b3];
  }
  &.is-alarm {
    @apply border-[#e411344d] fill-[#e41134];
  }
  &.is-under_observation {
    @apply border-[#e411344d] fill-[#946a00];
  }
  &.is-stand_by {
    @apply border-[#286dc870] fill-[#286dc8];
  }
}

.modal {
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  visibility: hidden;
  transition: 0.3s;
  opacity: 0;
  @apply p-2 z-10 bg-black/20;

  &.fadeIn,
  &.fadeOut {
    visibility: visible;
  }
  &.fadeIn {
    opacity: 1;
  }
}
.modal-content {
  margin: auto;
  transition: 0.5s;
  opacity: 0;
  transform: translateY(0.75rem);
  &.fadeIn {
    opacity: 1;
    transform: translateY(0);
  }
}

/** react-select */
.reactSelect__control {
  @apply rounded-lg;
  min-height: 40px !important;
  border-width: 1px !important;
  border-color: #d6d6d6 !important;
  background-color: transparent !important;

  &--is-focused {
    box-shadow: 0 0 0 4px #45d42e24 !important;
    outline: none;
  }
  &--menu-is-open {
    border-color: #45d42e !important;
    box-shadow: 0 0 0 4px #45d42e24 !important;
  }
}

.dark {
  .inputContent::placeholder {
    color: #a3a3a3 !important;
  }

  .reactSelect__control {
    border-color: var(--gray-light-mode-800) !important;
    &:hover {
      border-color: #a3a3a3 !important;
    }
  }

  /* Hightcharts */
  .highcharts-legend-item text {
    fill: var(--gray-light-mode-50) !important;
  }

  .highcharts-axis-labels text {
    fill: var(--gray-light-mode-25) !important;
  }

  .reactSelect__single-value {
    border-color: transparent !important;
    color: #a3a3a3 !important;
  }

  .reactSelect__menu {
    @apply border-[1px] border-primary;
    background-color: #030712 !important;
  }
}

.inputContent {
  @apply disabled:bg-comerc-gray-25 disabled:border-comerc-gray-50;

  &.isError {
    @apply focus:border-comerc-error-600;
  }
  &.isValid {
    @apply focus:border-comerc-primary-600;
  }
  &.isInvalid {
    @apply focus:border-comerc-warning-600;
  }
  &.isActive {
    @apply text-[#141515] focus:border-comerc-gray-300 dark:focus:border-comerc-grayLight-100 dark:text-comerc-grayLight-50;
  }
  &.isHover {
    @apply text-[#141515] focus:border-comerc-gray-200;
  }
  &.isDisabled {
    @apply text-comerc-gray-500 bg-transparent;
  }
}

.inputContent {
  @apply disabled:text-comerc-grayLight-500 disabled:border-comerc-gray-50 bg-transparent disabled:dark:bg-transparent;
}

.reactSelect__indicators {
  @apply flex text-gray-600;

  .reactSelect__indicator {
    @apply w-[25px] h-full p-0 flex [&>*]:m-auto cursor-pointer text-inherit;
  }
}

.reactSelect__control.reactSelect__control--is-focused {
  @apply border-[1px] border-comerc-primary-300;
}

.reactSelect__single-value {
  color: #424242 !important;
}

.reactSelect__menu {
  @apply p-2 pr-1 rounded-lg shadow-lg shadow-comerc-grayLight-900;
  margin-top: 4px !important;
}

.reactSelect__menu-list {
  @apply border-none shadow-none border-2;

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  &::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  &::-webkit-scrollbar-thumb {
    @apply bg-comerc-grayLight-200 rounded-lg;
  }
  .reactSelect__group-heading {
    @apply text-sm font-bold fg-secondary py-1;
    text-transform: unset;
  }
  .reactSelect__option {
    @apply text-comerc-grayLight-900 dark:text-comerc-grayLight-400 dark:hover:text-comerc-grayLight-400 pl-5;
    text-transform: unset;
  }

  .reactSelect__option.reactSelect__option--is-focused,
  .reactSelect__option.reactSelect__option--is-selected {
    @apply rounded-r-lg;
    @apply bg-comerc-grayLight-50 dark:bg-comerc-grayLight-800;
  }
}

.reactSelect__multi-value__label {
  @apply grid grid-cols-2 rounded-[6px] !important;
  @apply border-transparent;
  @apply text-comerc-grayLight-500 dark:text-comerc-grayLight-400 !important;
}
.reactSelect__multi-value {
  @apply bg-transparent rounded-md border-[1px] border-black/10 dark:border-white/10 !important;
}
.reactSelect__multi-value__remove {
  @apply border-transparent duration-150 hover:bg-comerc-grayLight-200 hover:dark:bg-comerc-grayLight-700 !important;
}

/** inputContent */
.inputContentWrapper {
  @apply border-comerc-gray-100 text-[#141515];
  @apply hover:border-comerc-grayLight-200 dark:border-comerc-grayLight-600 dark:hover:border-comerc-grayLight-400;

  &.isError {
    @apply border-comerc-error-300 box-shadow-comerc-error;
  }
  &.isValid {
    @apply border-comerc-primary-300 box-shadow-comerc-primary;
  }
  &.isInvalid {
    @apply border-comerc-warning-600;
  }
  &.isActive {
    @apply border-comerc-gray-300;
  }
  &.isHover {
    @apply border-comerc-gray-200;
  }
  &.isDisabled {
    @apply border-comerc-gray-300 bg-comerc-grayLight-50;
  }
}

.inputRoot {
  [data-inputlabel]:not(.isDisabled),
  [data-inputtext],
  [data-inputcontent] {
    &.isError {
      @apply text-comerc-error-600;
    }
    &.isValid {
      @apply text-comerc-primary-600;
    }
    &.isInvalid {
      @apply text-comerc-warning-600;
    }
    &.isActive {
      @apply text-[#141515] dark:text-comerc-grayLight-300;
    }
    &.isHover {
      @apply text-[#141515];
    }
    &.isDisabled {
      @apply text-comerc-gray-50;
    }
  }
}

.box-shadow-comerc-primary {
  box-shadow: 0px 1px 2px 0px var(--shadow),
    0px 0px 0px 4px var(--shadow-primary);
}

.box-shadow-comerc-error {
  box-shadow: 0px 1px 2px 0px var(--shadow), 0px 0px 0px 4px var(--shadow-error);
}

.icon--cut {
  @apply relative;
  &::after {
    content: '';
    @apply block w-[2px] h-full rounded bg-black absolute top-0 right-[calc(50%-1px)] opacity-75 rotate-45;
  }
}

.table-mobile {
  .table-mobile-cell {
    @apply flex items-center font-sans text-[14px] font-normal leading-[20px] break-all;
    @apply min-h-[44px] py-[12px] px-[24px];

    width: 100%;
    // @apply border-t-[1px] border-t-[#e5e5e5] dark:border-t-comerc-grayLight-800 text-gray-600;
  }

  .table-mobile-row {
    @apply border-primary;
  }

  .table-mobile-item > :nth-child(2).table-mobile-row .table-mobile-cell {
    border-top-width: 1px;
  }
  .table-mobile-item .table-mobile-row:not(:first-child) .table-mobile-cell {
    border-bottom-width: 1px;
  }

  .table-mobile-row .table-mobile-cell {
    @apply text-primary;
    border-color: inherit;
  }

  .table-mobile-row > .table-mobile-cell:first-child {
    @apply max-w-[136px];
    border-right-width: 1px;
  }
}

:where([header-dropdown-content], [header-dropdown-content='false']) {
  @apply overflow-hidden duration-150 ease-out max-h-0;
}
[header-dropdown-content='true'] {
  @apply max-h-[500px] border border-primary bg-secondary;
}

[header-dropdown-btn='true'] {
  @apply bg-secondary;

  [header-dropdown-btn-icon] {
    @apply -rotate-180;
  }
}
[data-menu-desktop] {
  @apply w-0 overflow-x-hidden lg:w-[80px] hover:w-[312px] duration-100 desktopMd:w-[312px];
  .menu-items {
    @apply mx-auto;
  }
  .menu-item {
    @apply py-[8px] px-[12px] w-[280px];
  }

  .menu-handle-button {
    @apply my-[32px] ml-[260px] py-[10px] px-[9px] rounded-[6px] size-[40px] hover:bg-comerc-grayLight-100;
  }
}

[data-menu-desktop='open'] {
  @apply w-[312px];
  .menu-handle-button {
    @apply ml-[260px];
  }
}

[data-menu-desktop='close'] {
  .menu-list-item-subModules {
    @apply max-w-0 max-h-0 overflow-hidden opacity-0;
  }
  .menu-item-text,
  .menu-item-icon {
    @apply max-w-0 opacity-0;
  }
  .menu-item {
    @apply w-[48px] p-[12px];
  }
  .menu-handle-button {
    @apply mx-auto;
  }
}

[data-dashboardmenu_entity_btn='open'] {
  > * {
    @apply -rotate-180;
  }
}
[data-dashboardmenu_entity_entities] {
  @apply duration-150;
}
[data-dashboardmenu_entity_entities='show'] {
  @apply h-max opacity-100;
}
[data-dashboardmenu_entity_entities='hide'] {
  @apply h-0 overflow-y-hidden opacity-0;
}

.dashboard-widgets {
  @apply grid gap-4 mb-4;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}
.dashboard-widget {
  @apply min-h-[160px] border-[1px] border-comerc-grayLight-200 rounded-[8px] p-3 dark:border-comerc-grayLight-700;
}
.dashboard-widget-title {
  @apply text-comerc-grayLight-500 text-xl not-italic font-semibold leading-8 mb-[8px] dark:text-comerc-vibra-grayLightMode-50;
}
.dashboard-tabs-container {
  @apply flex items-end relative gap-4 overflow-x-auto overflow-y-visible will-change-scroll scrollbar-hidden;

  & .dashboard-tab {
    @apply z-[10] relative border-b-[3px] text-sm font-semibold px-6 pt-1 pb-2 min-h-[36px] duration-300 min-w-max cursor-pointer text-comerc-gray-500 -mb-[1px];
  }

  & .dashboard-tabs-bar {
    @apply absolute bottom-0 left-0 border-b-[1px] border-comerc-gray-50;
  }
}

.alarm-badge-status-is-connected {
  @apply border-comerc-primary-500 text-comerc-primary-700 bg-comerc-primary-25;
}
.alarm-badge-status-is-disconnected {
  @apply border-comerc-grayLight-500 text-comerc-grayLight-700 bg-comerc-gray-25;
}
.alarm-badge-status-is-under_observation {
  @apply border-comerc-warning-500 text-comerc-warning-700 bg-comerc-warning-25;
}
.alarm-badge-status-is-alarm {
  @apply border-comerc-error-500 text-comerc-error-700 bg-comerc-error-25;
}
.alarm-badge-status-is-disabled {
  @apply border-comerc-grayDark-500 text-comerc-grayDark-700 bg-comerc-gray-25;
}
.alarm-badge-status-is-stand_by {
  @apply border-comerc-tertiary-500 text-comerc-tertiary-700 bg-comerc-tertiary-25;
}

//alarm Icon Status
.alarm-icon-status-is-connected {
  @apply text-comerc-primary-500 stroke-comerc-primary-500;
}
.alarm-icon-status-is-disconnected {
  @apply text-comerc-grayLight-500 stroke-comerc-grayLight-500;
}
.alarm-icon-status-is-under_observation {
  @apply text-comerc-warning-500 stroke-comerc-warning-500;
}
.alarm-icon-status-is-alarm {
  @apply text-comerc-error-500 stroke-comerc-error-500;
}
.alarm-icon-status-is-disabled {
  @apply text-comerc-grayDark-700 stroke-comerc-grayDark-700;
}
.alarm-icon-status-is-stand_by {
  @apply text-comerc-tertiary-600 stroke-comerc-tertiary-600;
}

@keyframes animate-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.animate-fadeIn {
  transition: all 0.3s ease-in-out;
  animation: animate-fadeIn 0.3s ease-in-out;
}
.numeric-measurement {
  &__number {
  }
}

/*Report Panel*/
.hero-container {
  @apply w-[80vw] relative min-h-[310px] flex items-center bg-right bg-no-repeat bg-contain;
  &-gradient {
    @apply absolute inset-0 bg-gradient-to-r from-[#61cebd] to-[#74e49b];
  }
  &-overlay {
    @apply absolute inset-0 bg-reports-hero bg-right bg-no-repeat bg-contain;
  }
  &-content {
    @apply relative w-full text-comerc-neutral-white ml-4;
  }
  &-title {
    @apply text-[32px];
  }
}
.reports-container {
  @apply w-[30vw] place-content-between font-normal;
  &-card {
    @apply h-full border-[1px] border-primary rounded-md p-2 md:p-4;
  }
  &-header {
    @apply flex justify-between;
  }
  &-title {
    @apply text-sm text-primary;
  }
  &-link {
    @apply text-sm text-comerc-primary-500 dark;
  }
}

.report-form {
  .report-group {
    animation: animate-fadeIn 0.3s ease-in-out;
    @apply mb-7;
  }
  .report-group-title {
    @apply text-sm text-primary font-bold mb-1;
  }
  .report-group-fields {
    @apply grid grid-cols-2 gap-2;
  }
  .inputRoot {
    gap: unset;
  }
}
.report-table {
  animation: animate-fadeIn 0.3s ease-in-out;
  @apply border-[1px] border-primary;
}
.icon-style {
  @apply icon-menu-primary stroke-comerc-primary-600;
}

.menu-open-force {
  @apply ml-[312px] !important;
}

.form-container {
  @apply py-[20px] px-[24px] border-[1px] rounded-[16px] dark:border-comerc-vibra-grayLightMode-800;

  .footer-form {
    @apply flex justify-end gap-2 border-t-[1px] border-t-comerc-grayLight-200 mt-[32px] pt-[20px] dark:border-t-comerc-grayLight-800;
  }
}
